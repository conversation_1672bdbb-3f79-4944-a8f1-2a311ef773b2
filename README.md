## Important information!!!
This repository has two branches:
- develop
- release/v2

The 'develop' branch contains the old pieces of code, old dependencies and uses nodejs <= 14.
If you want to fix something, pls create a branch from develop and merge PR in the develop branch changing the version in package.json.
You can use any version from 1.0.0 to 2.0.0

The 'release/v2' branch is intended for the transition to new technologies. The version will start from 2.0.0 in package.json

### Breaking Changes
- 'jsonwebtoken' has been wrapped by token namespace from "@skywind-group/sw-utils (it is used as peerDependencies in sw-utils ) ([NJSMGRN-88](https://jira.skywindgroup.com/browse/NJSMGRN-88))
- 'agentkeepalive'has been wrapped by keepalive namespace from "@skywind-group/sw-utils (it is used as peerDependencies in sw-utils ) ([NJSMGRN-88](https://jira.skywindgroup.com/browse/NJSMGRN-88))
- 'safe/safeGet' method has been removed. Use optional chaining. ([NJSMGRN-29](https://jira.skywindgroup.com/browse/NJSMGRN-29))
- 'fast-xml-parser' has been moved to peerDependencies ([NJSMGRN-88](https://jira.skywindgroup.com/browse/NJSMGRN-88))
- 'https' has been moved to peerDependencies ([NJSMGRN-88](https://jira.skywindgroup.com/browse/NJSMGRN-88))
- 'superagent-proxy' has been moved to peerDependencies ([NJSMGRN-88](https://jira.skywindgroup.com/browse/NJSMGRN-88))
- '@skywind-group/sw-deferred-payment' has been moved to peerDependencies ([NJSMGRN-90](https://jira.skywindgroup.com/browse/NJSMGRN-90))
- '@skywind-group/sw-round-details-report' has been moved to peerDependencies ([NJSMGRN-90](https://jira.skywindgroup.com/browse/NJSMGRN-90))
