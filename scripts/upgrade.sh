#!/usr/bin/env bash

branch_name="feature/SWS-49228-please-involve-new-x13-social-casino-currency-to-the-system"
commit_message="SWS-49228: Please involve new X13 social casino currency to the system"

V1_VERSION="1.6.10"
V2_VERSION="2.3.10"
target_branch="release/5.51"

clean_up() {
  test -d "$tmp_dir" && rm -fr "$tmp_dir"
}

upgrade() {
  git clone --branch "${target_branch}" --depth 1 "${1}"
  cd "$(basename "$1" .git)" || exit

  git checkout -b "${branch_name}"
  VERSION="${2}" node "${dir}/update-version.js"
  git commit -a -m "${commit_message}"
  git push -u origin "${branch_name}"
  cd ".." || exit
}

upgrade_v1() {
  upgrade "$1" $V1_VERSION
}

upgrade_v2() {
  upgrade "$1" $V2_VERSION
}

tmp_dir=$( mktemp -d -t my-script )
trap "clean_up $tmp_dir" EXIT

dir="$(cd -P -- "$(dirname -- "${BASH_SOURCE[0]}")" && pwd -P)"
cd "${tmp_dir}" || exit

#upgrade_v1 "ssh://******************************:7999/sbep/sw-currency-exchange-api.git"
#upgrade_v2 "ssh://******************************:7999/sbep/sw-management-api.git"
#upgrade_v2 "ssh://******************************:7999/swb/ipm-mock.git"
#upgrade_v2 "ssh://******************************:7999/swb/sw-integration-seamless.git"
#upgrade_v2 "ssh://******************************:7999/swjpn/sw-jpn-server-api.git"
#upgrade_v2 "ssh://******************************:7999/sbep/sw-slot-engine.git"

#upgrade_v1 "ssh://******************************:7999/swb/sw-integration-relax.git"
