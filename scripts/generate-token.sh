#!/bin/bash

# Token Generation Script for SW Currency Exchange API
# This script generates JWT tokens using standard Unix tools and openssl

set -e

# Default configuration values (matching src/skywind/config.ts)
DEFAULT_SECRET="TU8N9oP4pPfrUMaRYkjwBsOyw0hgg39sPsTjONrgnN1ErJbn2"
DEFAULT_ALGORITHM="HS256"
DEFAULT_ISSUER="skywindgroup"
DEFAULT_EXPIRES_IN=300

# Load environment variables or use defaults
SECRET="${INTERNAL_SERVER_TOKEN_SECRET:-$DEFAULT_SECRET}"
ALGORITHM="${INTERNAL_SERVER_TOKEN_ALGORITHM:-$DEFAULT_ALGORITHM}"
ISSUER="${INTERNAL_SERVER_TOKEN_ISSUER:-$DEFAULT_ISSUER}"
EXPIRES_IN="${INTERNAL_SERVER_TOKEN_EXPIRES_IN:-$DEFAULT_EXPIRES_IN}"

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Generate JWT tokens for SW Currency Exchange API using standard Unix tools"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -s, --secret SECRET     JWT secret (default: from env or built-in)"
    echo "  -a, --algorithm ALG     JWT algorithm: HS256, HS384, HS512 (default: $DEFAULT_ALGORITHM)"
    echo "  -i, --issuer ISSUER     JWT issuer (default: $DEFAULT_ISSUER)"
    echo "  -e, --expires SECONDS   Token expiration in seconds (default: $DEFAULT_EXPIRES_IN)"
    echo "  --env                   Show current environment configuration"
    echo ""
    echo "Examples:"
    echo "  $0"
    echo "  $0 -e 3600"
    echo "  $0 -a HS512"
    echo ""
    echo "Requirements:"
    echo "  - openssl (for HMAC signature generation)"
    echo "  - base64 (for encoding)"
    echo "  - date (for timestamps)"
    echo ""
    echo "Environment Variables:"
    echo "  INTERNAL_SERVER_TOKEN_SECRET     - JWT secret key"
    echo "  INTERNAL_SERVER_TOKEN_ALGORITHM  - JWT algorithm (HS256, HS384, HS512)"
    echo "  INTERNAL_SERVER_TOKEN_ISSUER     - JWT issuer"
    echo "  INTERNAL_SERVER_TOKEN_EXPIRES_IN - Token expiration in seconds"
}

# Function to show current environment configuration
show_env_config() {
    echo "Current Token Configuration:"
    echo "  Secret: ${SECRET:0:10}... (truncated)"
    echo "  Algorithm: $ALGORITHM"
    echo "  Issuer: $ISSUER"
    echo "  Expires In: $EXPIRES_IN seconds"
    echo ""
    echo "Environment Variables:"
    echo "  INTERNAL_SERVER_TOKEN_SECRET=${INTERNAL_SERVER_TOKEN_SECRET:-"(not set)"}"
    echo "  INTERNAL_SERVER_TOKEN_ALGORITHM=${INTERNAL_SERVER_TOKEN_ALGORITHM:-"(not set)"}"
    echo "  INTERNAL_SERVER_TOKEN_ISSUER=${INTERNAL_SERVER_TOKEN_ISSUER:-"(not set)"}"
    echo "  INTERNAL_SERVER_TOKEN_EXPIRES_IN=${INTERNAL_SERVER_TOKEN_EXPIRES_IN:-"(not set)"}"
}

# Function to check if required tools are available
check_dependencies() {
    local missing_tools=()

    if ! command -v openssl &> /dev/null; then
        missing_tools+=("openssl")
    fi

    if ! command -v base64 &> /dev/null; then
        missing_tools+=("base64")
    fi

    if ! command -v date &> /dev/null; then
        missing_tools+=("date")
    fi

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        echo "Error: Missing required tools: ${missing_tools[*]}"
        echo "Please install the missing tools to use this script"
        exit 1
    fi
}

# Function to base64url encode (JWT standard)
base64url_encode() {
    base64 | tr '+/' '-_' | tr -d '='
}

# Function to create JWT header
create_jwt_header() {
    local algorithm="$1"
    echo -n "{\"alg\":\"$algorithm\",\"typ\":\"JWT\"}" | base64url_encode
}

# Function to create JWT payload
create_jwt_payload() {
    local issuer="$1"
    local expires_in="$2"

    # Get current timestamp
    local iat=$(date +%s)
    local exp=$((iat + expires_in))

    # Create minimal payload with only standard JWT claims
    local payload="{\"iat\":$iat,\"exp\":$exp,\"iss\":\"$issuer\"}"

    echo -n "$payload" | base64url_encode
}

# Function to create HMAC signature
create_signature() {
    local data="$1"
    local secret="$2"
    local algorithm="$3"

    case "$algorithm" in
        "HS256")
            echo -n "$data" | openssl dgst -sha256 -hmac "$secret" -binary | base64url_encode
            ;;
        "HS384")
            echo -n "$data" | openssl dgst -sha384 -hmac "$secret" -binary | base64url_encode
            ;;
        "HS512")
            echo -n "$data" | openssl dgst -sha512 -hmac "$secret" -binary | base64url_encode
            ;;
        *)
            echo "Error: Unsupported algorithm: $algorithm"
            echo "Supported algorithms: HS256, HS384, HS512"
            exit 1
            ;;
    esac
}

# Function to generate JWT token
generate_token() {
    # Create JWT components
    local header=$(create_jwt_header "$ALGORITHM")
    local jwt_payload=$(create_jwt_payload "$ISSUER" "$EXPIRES_IN")
    local signing_input="${header}.${jwt_payload}"
    local signature=$(create_signature "$signing_input" "$SECRET" "$ALGORITHM")

    # Combine all parts
    echo "${header}.${jwt_payload}.${signature}"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -s|--secret)
            SECRET="$2"
            shift 2
            ;;
        -a|--algorithm)
            ALGORITHM="$2"
            shift 2
            ;;
        -i|--issuer)
            ISSUER="$2"
            shift 2
            ;;
        -e|--expires)
            EXPIRES_IN="$2"
            shift 2
            ;;
        --env)
            show_env_config
            exit 0
            ;;
        -*)
            echo "Error: Unknown option $1"
            show_usage
            exit 1
            ;;
        *)
            echo "Error: Unexpected argument $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if required tools are available
check_dependencies

# Generate and output the token
echo "Generating token..."
echo ""
TOKEN=$(generate_token)
echo "Generated Token:"
echo "$TOKEN"
echo ""
echo "Token Details:"
echo "  Algorithm: $ALGORITHM"
echo "  Issuer: $ISSUER"
echo "  Expires In: $EXPIRES_IN seconds"
echo "  Length: ${#TOKEN} characters"
