version: '3.4'
services:
  utils:
    image: ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED}
    build:
      context: .
      dockerfile: Dockerfile
    links:
      - db
      - redis
    environment:
      PGUSER: postgres
      PGDATABASE: postgres
      SONAR_HOST_URL: "${SONAR_HOST_URL}"
      SONAR_AUTH_TOKEN: "${SONAR_AUTH_TOKEN}"
    command: sh -c "npm run eslint && npm run test && npm run sonar"
  db:
    image: postgres:10
    ports:
      - 5432
    environment:
      POSTGRES_HOST_AUTH_METHOD: trust
  redis:
    image: redis:alpine
    ports:
      - 6379
