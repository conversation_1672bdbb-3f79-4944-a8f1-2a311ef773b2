{"name": "@skywind-group/sw-wallet-adapter-core", "version": "2.1.5", "description": "", "packageManager": "npm@10.8.2", "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"clean": "rm -rf lib", "compile": "tsc -b tsconfig.json", "test": "mocha -r ts-node/register --exit src/**/*.spec.ts", "lint": "eslint --ext .ts src", "changelog": "npx auto-changelog -p", "version": "echo $npm_package_version $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/skywind/version"}, "repository": {"type": "git", "url": "ssh://******************************:7999/swb/sw-wallet-adapter-core.git"}, "author": "", "license": "ISC", "devDependencies": {"@skywind-group/sw-deferred-payment": "^2.1.0", "@skywind-group/sw-round-details-report": "^1.1.2", "@types/node": "^20.11.13", "@testdeck/mocha": "^0.3.3", "@types/jsonwebtoken": "^9.0.5", "@types/mocha": "^10.0.6", "@types/superagent": "^8.1.3", "bole": "^5.0.10", "bole-console": "^0.1.10", "chai": "^4.4.0", "mocha": "^10.2.0", "reflect-metadata": "^0.2.1", "agentkeepalive": "^4.5.0", "superagent": "10.2.0", "jsonwebtoken": "^9.0.2", "auto-changelog": "^2.4.0", "eslint": "^8.56.0", "eslint-plugin-node": "^11.1.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "prom-client": "~15.0.0", "ts-node": "^10.9.2", "typescript": "5.3.3"}, "peerDependencies": {"@skywind-group/sw-utils": "^2.3.0", "@skywind-group/sw-deferred-payment": "1 || 2", "@skywind-group/sw-round-details-report": "1 || 2", "fast-xml-parser": "4.4.1", "superagent-proxy": "3.0.0"}, "engines": {"node": ">=14"}, "auto-changelog": {"commitLimit": false, "commitUrl": "https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/{id}", "compareUrl": "https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2F{from}&sourceBranch=refs%2Ftags%2F{to}", "issueUrl": "https://jira.skywindgroup.com/browse/{id}", "ignoreCommitPattern": "Pull request|Merge branch|Merge remote-tracking branch|Merge pull request|update changelog.md", "issuePattern": "[A-Z]+-\\d+", "hideCredit": true, "hideEmptyReleases": true}}