{"name": "@skywind-group/sw-utils", "version": "2.4.1", "description": "", "license": "ISC", "main": "lib/index.js", "typings": "resources/index.d.ts", "scripts": {"clean": "rm -rf ./lib", "compile": "tsc", "eslint": "eslint --ext .ts src resources", "test": "nyc mocha lib/test/**/*.spec.js lib/test/*.spec.js", "test:ts": "nyc mocha src/test/**/*.spec.ts src/test/*.spec.ts", "sonar": "node sonarqube.mjs", "clean:all": "rm -rf node_modules package-lock.json coverage .nyc_output lib .scannerwork", "eslint:fix": "eslint --fix --ext .ts src resources", "changelog": "npx auto-changelog -p"}, "devDependencies": {"@skywind-group/gelf-stream": "1.2.6", "@testdeck/mocha": "^0.3.3", "@types/benchmark": "^2.1.5", "@types/bluebird": "^3.5.42", "@types/chai": "^4.3.16", "@types/chai-as-promised": "^7.1.8", "@types/cls-hooked": "^4.3.8", "@types/crc": "^3.8.3", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/mocha": "^10.0.7", "@types/node": "^20.16.12", "@types/shortid": "^0.0.32", "@types/sinon": "^17.0.3", "@types/sinon-chai": "3.2.12", "@types/superagent": "^8.1.7", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.16.1", "@typescript-eslint/parser": "^7.16.1", "agentkeepalive": "^4.5.0", "auto-changelog": "^2.4.0", "benchmark": "^2.1.4", "bluebird": "^3.7.2", "bole": "^5.0.14", "bole-console": "^0.1.10", "chai": "^4.4.1", "chai-as-promised": "^7.1.2", "cls-bluebird": "^2.1.0", "cls-hooked": "^4.2.2", "crc": "^4.3.2", "emitter-listener": "^1.1.2", "eslint": "^8.57.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.4.0", "eslint-plugin-sonarjs": "^1.0.3", "eslint-plugin-unicorn": "^53.0.0", "express": "^4.19.2", "express-prom-bundle": "^7.0.0", "fastify": "^4.28.1", "@fastify/middie": "^8.3.1", "generic-pool": "^3.9.0", "hashids": "^2.3.0", "ioredis": "^5.4.1", "js-big-integer": "1.0.2", "jsonwebtoken": "^9.0.2", "kafka-node": "5.0.0", "kafkajs": "^2.2.4", "measured-core": "^2.0.0", "mocha": "^10.6.0", "nanoid": "^3.3.6", "nyc": "^17.0.0", "pg": "^8.13.0", "prom-client": "~15.0.0", "redis": "^2.8.0", "reflect-metadata": "^0.2.2", "sequelize": "^6.37.4", "sha-1": "^1.0.0", "sha1": "^1.1.1", "sinon": "^18.0.0", "sinon-chai": "3.7.0", "sonarqube-scanner": "3.5.0", "stream-buffers": "^3.0.3", "superagent": "^9.0.2", "superagent-mocker": "0.5.2", "ts-node": "^10.9.2", "typescript": "5.5.4", "uuid": "^9.0.1"}, "packageManager": "npm@11.0.0", "repository": {"type": "git", "url": "ssh://******************************:7999/sbep/sw-utils.git"}, "peerDependencies": {"@skywind-group/gelf-stream": "1.2.6", "agentkeepalive": "^4.5.0", "crc": "^4.3.2", "emitter-listener": "^1.1.2", "express-prom-bundle": "^6.6.0 || ^7.0.0 || 8", "generic-pool": "^3.9.0", "hashids": "^2.3.0", "ioredis": "^5.4.1", "js-big-integer": "1.0.2", "jsonwebtoken": "^9.0.2", "kafka-node": "5.0.0", "measured-core": "^2.0.0", "prom-client": "^14.2.0 || ~15.0.0", "uuid": "^9.0.1", "kafkajs": "^2.2.4"}, "peerDependenciesMeta": {"jsonwebtoken": {"optional": true}, "agentkeepalive": {"optional": true}, "hashids": {"optional": true}, "kafka-node": {"optional": true}, "kafkajs": {"optional": true}, "@skywind-group/gelf-stream": {"optional": true}, "generic-pool": {"optional": true}, "ioredis": {"optional": true}, "measured-core": {"optional": true}, "crc": {"optional": true}, "emitter-listener": {"optional": true}, "express-prom-bundle": {"optional": true}, "prom-client": {"optional": true}, "js-big-integer": {"optional": true}, "uuid": {"optional": true}}, "engines": {"node": ">=14.17"}, "auto-changelog": {"commitLimit": false, "commitUrl": "https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/{id}", "compareUrl": "https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2F{from}&sourceBranch=refs%2Ftags%2F{to}", "issueUrl": "https://jira.skywindgroup.com/browse/{id}", "ignoreCommitPattern": "Pull request|Merge branch|Merge remote-tracking branch|Merge pull request|update changelog.md", "issuePattern": "[A-Z]+-\\d+", "hideCredit": true, "hideEmptyReleases": true}}