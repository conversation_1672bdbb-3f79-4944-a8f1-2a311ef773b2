FROM node:22.16.0-bullseye AS build

# Install dependencies
RUN apt-get -yqq --no-install-recommends install git curl unzip

WORKDIR /app
COPY . /app/

RUN npm install \
    && npm run compile \
    && npm run version \
    && rm .npmrc

FROM node:22.16.0-alpine AS main

RUN mkdir -p /opt/gpapi \
    && adduser -u 2003 -h /opt/gpapi -Ds /sbin/nologin gpapi

WORKDIR /app
COPY --from=build /app .

RUN rm -rf src coverage lib/test .git .nyc_output && npm cache clean --force

EXPOSE 5000
USER node
CMD ["node", "--max-http-header-size=81000", "lib/skywind/app"]