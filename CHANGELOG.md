### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

#### [v2.1.5](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.1.4&sourceBranch=refs%2Ftags%2Fv2.1.5)

- EDCT-140: Optional fields for SWGameInfo [`#EDCT-140`](https://jira.skywindgroup.com/browse/EDCT-140)
- SWS-50742: Add FUN_BONUS to PlayMode [`#SWS-50742`](https://jira.skywindgroup.com/browse/SWS-50742)

#### [v2.1.4](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.1.3&sourceBranch=refs%2Ftags%2Fv2.1.4)

> 22 May 2025

- SWS-45605 Add semicolon [`#SWS-45605`](https://jira.skywindgroup.com/browse/SWS-45605)
- SWS-45605 Add the forceFinish method to the internal api service [`#SWS-45605`](https://jira.skywindgroup.com/browse/SWS-45605)

#### [v2.1.3](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.1.2&sourceBranch=refs%2Ftags%2Fv2.1.3)

> 14 April 2025

- NJSMGRN-465: bump sw-deferred-payment [`#NJSMGRN-465`](https://jira.skywindgroup.com/browse/NJSMGRN-465)

#### [v2.1.2](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.1.1&sourceBranch=refs%2Ftags%2Fv2.1.2)

> 3 April 2025

- SWS-44925 Fix typos [`#SWS-44925`](https://jira.skywindgroup.com/browse/SWS-44925)
- SWS-44925 Add the option to skip EGP promotions on getting merchant promotions [`#SWS-44925`](https://jira.skywindgroup.com/browse/SWS-44925)
- SWS-44138 add overridePreviousPlayerPromo to PromoInfo [`#SWS-44138`](https://jira.skywindgroup.com/browse/SWS-44138)
- merge develop [`23edeb8`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/23edeb8bde64786cdf9484aee7c2ed815ac98047)

#### [v2.1.1](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.1.0&sourceBranch=refs%2Ftags%2Fv2.1.1)

> 19 February 2025

- SWS-47669 Add new wallet type [`#SWS-47669`](https://jira.skywindgroup.com/browse/SWS-47669)

#### [v2.1.0](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.33&sourceBranch=refs%2Ftags%2Fv2.1.0)

> 3 January 2025

- NJSMGRN-406: support nodejs 14 [`#NJSMGRN-406`](https://jira.skywindgroup.com/browse/NJSMGRN-406)
- fix lint [`04144cb`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/04144cb0e8f2bb73ea6974dbcdee116e37d0089d)

#### [v2.0.33](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.31&sourceBranch=refs%2Ftags%2Fv2.0.33)

> 6 December 2024

- SWS-44925 Fix typos [`#SWS-44925`](https://jira.skywindgroup.com/browse/SWS-44925)
- SWS-44925 Update sw-utils [`#SWS-44925`](https://jira.skywindgroup.com/browse/SWS-44925)
- SWS-44925 Fix more eslint issues [`#SWS-44925`](https://jira.skywindgroup.com/browse/SWS-44925)
- SWS-44925 Suppress eslint warning [`#SWS-44925`](https://jira.skywindgroup.com/browse/SWS-44925)
- SWS-44925 Add the option to skip EGP promotions on getting merchant promotions [`#SWS-44925`](https://jira.skywindgroup.com/browse/SWS-44925)

#### [v2.0.31](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.30&sourceBranch=refs%2Ftags%2Fv2.0.31)

> 26 August 2024

- SWS-44138 add overridePreviousPlayerPromo to PromoInfo [`#SWS-44138`](https://jira.skywindgroup.com/browse/SWS-44138)

#### [v2.0.30](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.29&sourceBranch=refs%2Ftags%2Fv2.0.30)

> 1 August 2024

- Update dependency fast-xml-parser to v4.4.1 [SECURITY] [`a61006d`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/a61006d311b2cab02be7858f663ff8e5b3fab79b)

#### [v2.0.29](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.28&sourceBranch=refs%2Ftags%2Fv2.0.29)

> 16 July 2024

- Update npm to v10.8.2 [`6294159`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/62941595015ca216460dfb9091b3d7a8e7f5d4c9)

#### [v2.0.28](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.27&sourceBranch=refs%2Ftags%2Fv2.0.28)

> 27 June 2024

- SWS-44622 Bump version once more [`#SWS-44622`](https://jira.skywindgroup.com/browse/SWS-44622)

#### [v2.0.27](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.26&sourceBranch=refs%2Ftags%2Fv2.0.27)

> 27 June 2024

- SWS-44622 Add dynamicMaxTotalBetLimit to MerchantGameTokenData [`#SWS-44622`](https://jira.skywindgroup.com/browse/SWS-44622)
- SWS-44622 Add dynamicMaxTotalBetLimit to MerchantGameTokenData [`#SWS-44622`](https://jira.skywindgroup.com/browse/SWS-44622)

#### [v2.0.26](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.25&sourceBranch=refs%2Ftags%2Fv2.0.26)

> 27 June 2024

- SWS-44622 Merge develop to release/v2 [`#SWS-44622`](https://jira.skywindgroup.com/browse/SWS-44622)
- SWS-44542 Bump version [`#SWS-44542`](https://jira.skywindgroup.com/browse/SWS-44542)
- SWS-44542 Rename and move maxTotalBet key to StartGameTokenData [`#SWS-44542`](https://jira.skywindgroup.com/browse/SWS-44542)

#### [v2.0.25](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.24&sourceBranch=refs%2Ftags%2Fv2.0.25)

> 18 June 2024

- Update dependency fast-xml-parser to v4.4.0 [`d30b318`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/d30b318feeef81c7641f5af46feeba018ba35688)

#### [v2.0.24](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.23&sourceBranch=refs%2Ftags%2Fv2.0.24)

> 18 June 2024

- SWS-45037 Add "ts" to the FinalizeGameRequest interface [`#SWS-45037`](https://jira.skywindgroup.com/browse/SWS-45037)
- SWS-45037 Add "ts" to the FinalizeGameRequest interface [`#SWS-45037`](https://jira.skywindgroup.com/browse/SWS-45037)
- SWS-44542 Add optional maxTotalBet key to BaseGameTokenData [`#SWS-44542`](https://jira.skywindgroup.com/browse/SWS-44542)
- SWS-44683 Add the "forwardToWrapper" special flag to SWError and the game token [`#SWS-44683`](https://jira.skywindgroup.com/browse/SWS-44683)
- SWS-44683 Add the "forwardToWrapper" special flag to SWError and the game token [`#SWS-44683`](https://jira.skywindgroup.com/browse/SWS-44683)

#### [v2.0.23](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.22&sourceBranch=refs%2Ftags%2Fv2.0.23)

> 3 June 2024

- Update npm to v10.8.1 [`b798038`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/b7980387281b67b87aedb05e809a53598e3decb6)

#### [v2.0.22](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.21&sourceBranch=refs%2Ftags%2Fv2.0.22)

> 17 May 2024

- Update npm to v10.8.0 [`adbf5f6`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/adbf5f64062fff252201c7690c4bb18e85020099)

#### [v2.0.21](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.20&sourceBranch=refs%2Ftags%2Fv2.0.21)

> 15 May 2024

- SWS-44657 Add new flag called "forbidOnlineRetries" to the game token [`#SWS-44657`](https://jira.skywindgroup.com/browse/SWS-44657)
- SWS-44657 Add new flag called "forbidOnlineRetries" to the game token [`#SWS-44657`](https://jira.skywindgroup.com/browse/SWS-44657)

#### [v2.0.20](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.19&sourceBranch=refs%2Ftags%2Fv2.0.20)

> 14 May 2024

- Update npm to v10.7.0 [`0bf39c7`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/0bf39c707eb5df7dc50a0d5afb2ad86fa19309a6)

#### [v2.0.19](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.18&sourceBranch=refs%2Ftags%2Fv2.0.19)

> 29 April 2024

- Update npm to v10.6.0 [`2534d6a`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/2534d6af4a534a047e243997710cef77d2607c82)

#### [v2.0.18](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.17&sourceBranch=refs%2Ftags%2Fv2.0.18)

> 12 April 2024

- Update npm to v10.5.2 [`4a5d8ae`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/4a5d8aef29a2373e147be37fb8a99cc12db803a7)

#### [v2.0.17](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.16&sourceBranch=refs%2Ftags%2Fv2.0.17)

> 8 April 2024

- Update npm to v10.5.1 [`874ef68`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/874ef68c756c9cd487bf643fe91ef4ac9dc2fec6)

#### [v2.0.16](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.15&sourceBranch=refs%2Ftags%2Fv2.0.16)

> 1 April 2024

- Update dependency fast-xml-parser to v4.3.6 [`babefe7`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/babefe79d9077b3b60e2432f3475887afec49bb6)

#### [v2.0.15](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.14&sourceBranch=refs%2Ftags%2Fv2.0.15)

> 1 March 2024

- Update npm to v10.5.0 [`62a6076`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/62a607648ae6fd1fb72fd593e5140d93ba651286)
- Update dependency fast-xml-parser to v4.3.5 [`70d3a40`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/70d3a4008794b40df875c32f29bf6dc07f897485)

#### [v2.0.14](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.13&sourceBranch=refs%2Ftags%2Fv2.0.14)

> 5 February 2024

- Update dependency fast-xml-parser to v4.3.4 [`ba4c15a`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/ba4c15a4e44a1215cb199f538fe03c400a02bde5)

#### [v2.0.13](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.12&sourceBranch=refs%2Ftags%2Fv2.0.13)

> 2 February 2024

- Update npm to v10.4.0 [`fd90f12`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/fd90f12b6e713a48987b783ef7b4573b95936394)

#### [v2.0.12](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.11&sourceBranch=refs%2Ftags%2Fv2.0.12)

> 31 January 2024

- SWS-43682 Add new merchant search functionality [`#SWS-43682`](https://jira.skywindgroup.com/browse/SWS-43682)
- merge develop to v2 and fix conflicts, update renovate config [`9287f83`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/9287f83eda2d4c883c0e9b563beeaaed1ff134e6)

#### [v2.0.11](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.10&sourceBranch=refs%2Ftags%2Fv2.0.11)

> 29 January 2024

- POP-64 Correct totalBetMultiplier type [`#POP-64`](https://jira.skywindgroup.com/browse/POP-64)
- POP-64 Add totalBetMultiplier to SWGameInfo [`#POP-64`](https://jira.skywindgroup.com/browse/POP-64)
- SWS-43847: added isLive flag to interface [`#SWS-43847`](https://jira.skywindgroup.com/browse/SWS-43847)
- SWS-43689 Update the GameQueryParams interface with "skipJurisdictionFiltering" and make it accept any type of params [`#SWS-43689`](https://jira.skywindgroup.com/browse/SWS-43689)
- NMSS-406 Make startDate optional for promotion creation [`#NMSS-406`](https://jira.skywindgroup.com/browse/NMSS-406)
- SWS-43104 add option for active promo to be also string [`#SWS-43104`](https://jira.skywindgroup.com/browse/SWS-43104)
- SWS-43587 Add promo information to the finalizeGame request [`#SWS-43587`](https://jira.skywindgroup.com/browse/SWS-43587)
- merge develop to release/v2 [`a900145`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/a9001458461e12e417c281a458d7f327b7dab38c)

#### [v2.0.10](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.9&sourceBranch=refs%2Ftags%2Fv2.0.10)

> 29 January 2024

- update nodejs and update renovate config [`dbdbf29`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/dbdbf29dbaee8634ea1caa8ad9923fd25efc09ab)
- fixed tslint [`ca219f7`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/ca219f77840c2a47028d713c6bccec7b16c63b7a)
- Update Dev libraries [`39daef2`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/****************************************)

#### [v2.0.9](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.8&sourceBranch=refs%2Ftags%2Fv2.0.9)

> 23 January 2024

- Update Dev libraries [`55463ed`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/55463edc43eb800335e68e2106f30e0378a058e7)

#### [v2.0.8](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.7&sourceBranch=refs%2Ftags%2Fv2.0.8)

> 16 January 2024

- Update Dev libraries [`373049e`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/373049e66d4207a31d690769f73a29d4a3513ea1)

#### [v2.0.7](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.6&sourceBranch=refs%2Ftags%2Fv2.0.7)

> 9 January 2024

- Update Dev libraries [`54383cd`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/54383cd290efd7710767efd3d564352277c84158)

#### [v2.0.6](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.5&sourceBranch=refs%2Ftags%2Fv2.0.6)

> 19 December 2023

- Update Dev libraries [`8cd5eec`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/8cd5eec7ae91fbf5185dbb27d3d6027f291af73a)

#### [v2.0.5](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.4&sourceBranch=refs%2Ftags%2Fv2.0.5)

> 12 December 2023

- Update Dev libraries [`44f9e02`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/44f9e02c4a18b60841b5ab826b5e3b16b5cf6e1b)

#### [v2.0.4](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.3&sourceBranch=refs%2Ftags%2Fv2.0.4)

> 29 November 2023

- NJSMGRN-11: sw-utils version was updated [`#NJSMGRN-11`](https://jira.skywindgroup.com/browse/NJSMGRN-11)
- NJSMGRN-170: jsonwebtoken and keepalive were wrapped by  "@skywind-group/sw-utils" [`#NJSMGRN-170`](https://jira.skywindgroup.com/browse/NJSMGRN-170)
- NJSMGRN-151: renovate config was added [`#NJSMGRN-151`](https://jira.skywindgroup.com/browse/NJSMGRN-151)
- NJSMGRN-23: libs were re-neweled [`#NJSMGRN-23`](https://jira.skywindgroup.com/browse/NJSMGRN-23)
- SWS-43378 increase version [`#SWS-43378`](https://jira.skywindgroup.com/browse/SWS-43378)
- SWS-43378 add missing attributes from RollbackRequest interface [`#SWS-43378`](https://jira.skywindgroup.com/browse/SWS-43378)
- SWS-43333 Set "@types/cookiejar" to version "2.1.1" to fix the build [`#SWS-43333`](https://jira.skywindgroup.com/browse/SWS-43333)
- SWS-43333 Remove "force-finish" from GameFinalizationType [`#SWS-43333`](https://jira.skywindgroup.com/browse/SWS-43333)
- SWS-37775 Add smResult to the RoundStatistics interface [`#SWS-37775`](https://jira.skywindgroup.com/browse/SWS-37775)
- SWS-42458: [QTech] "completed" field is not sent in /rollback request to wallet [`#SWS-42458`](https://jira.skywindgroup.com/browse/SWS-42458)

#### [v2.0.3](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.2&sourceBranch=refs%2Ftags%2Fv2.0.3)

> 20 October 2023

- NJSMGRN-11: uuid, emitter-listener libs are not required - it use lazy loading for now [`#NJSMGRN-11`](https://jira.skywindgroup.com/browse/NJSMGRN-11)
- NJSMGRN-23: not used dependencies were removed [`#NJSMGRN-23`](https://jira.skywindgroup.com/browse/NJSMGRN-23)
- SWS-37562 Move GameFinalizationType to wallet-adapter-core [`#SWS-37562`](https://jira.skywindgroup.com/browse/SWS-37562)
- SWS-37562 Remove unused enum elements from BrandFinalizationType [`#SWS-37562`](https://jira.skywindgroup.com/browse/SWS-37562)
- SWS-42263 Fix assertions [`#SWS-42263`](https://jira.skywindgroup.com/browse/SWS-42263)
- SWS-42263 Add tests [`#SWS-42263`](https://jira.skywindgroup.com/browse/SWS-42263)
- SWS-42263 Skip censoring certain object values [`#SWS-42263`](https://jira.skywindgroup.com/browse/SWS-42263)
- prod libs moved to peerDependencies NJSMGRN-88 [`#NJSMGRN-88`](https://jira.skywindgroup.com/browse/NJSMGRN-88)
- peerDependenciesMeta was deleted [`1ac1f94`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/1ac1f94675dacb21c1f04070b959bc7f4558b7d0)
- prod SW-Libs were moved to peerDependencies [`f35259e`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/f35259ef1ccd0224bbd4b4e751a8c2b323cab3f6)

#### [v2.0.2](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.1&sourceBranch=refs%2Ftags%2Fv2.0.2)

> 5 October 2023

- chnagelog file was added NJSMGRN-46 [`#NJSMGRN-46`](https://jira.skywindgroup.com/browse/NJSMGRN-46)
- changelog was added NJSMGRN-46 [`#NJSMGRN-46`](https://jira.skywindgroup.com/browse/NJSMGRN-46)

#### [v2.0.1](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.0&sourceBranch=refs%2Ftags%2Fv2.0.1)

> 5 October 2023

- "typescript": to version "5.2.2" NJSMGRN-78, remove safe/safeGet  NJSMGRN-29 [`#NJSMGRN-78`](https://jira.skywindgroup.com/browse/NJSMGRN-78) [`#NJSMGRN-29`](https://jira.skywindgroup.com/browse/NJSMGRN-29)
- SWS-42666 Export enums [`#SWS-42666`](https://jira.skywindgroup.com/browse/SWS-42666)
- SWS-42666 Add the "specialFlag" property on the SWError class [`#SWS-42666`](https://jira.skywindgroup.com/browse/SWS-42666)
- moved to node version: 20.7.0 NJSMGRN-79 [`#NJSMGRN-79`](https://jira.skywindgroup.com/browse/NJSMGRN-79)
- Automatic merge from release/v2 -&gt; develop [`#NJSMGRN-48`](https://jira.skywindgroup.com/browse/NJSMGRN-48) [`#NJSMGRN-28`](https://jira.skywindgroup.com/browse/NJSMGRN-28) [`#NJSMGRN-26`](https://jira.skywindgroup.com/browse/NJSMGRN-26) [`#NJSMGRN-25`](https://jira.skywindgroup.com/browse/NJSMGRN-25) [`#NJSMGRN-11`](https://jira.skywindgroup.com/browse/NJSMGRN-11) [`#NJSMGRN-31`](https://jira.skywindgroup.com/browse/NJSMGRN-31) [`#NJSMGRN-31`](https://jira.skywindgroup.com/browse/NJSMGRN-31) [`#NJSMGRN-48`](https://jira.skywindgroup.com/browse/NJSMGRN-48) [`#NJSMGRN-23`](https://jira.skywindgroup.com/browse/NJSMGRN-23) [`#NJSMGRN-23`](https://jira.skywindgroup.com/browse/NJSMGRN-23)
- Revert "Automatic merge from release/v2 -&gt; develop" [`99ca1e5`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/99ca1e57c0324a3ff8e711f4ba638a809c03d90a)
- packageManager was updated [`006803b`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/006803b6bc8e95f7e6801d3035269029f7839260)
- updated to latest version of sw-utils [`a838928`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/a838928b408e5c615c531a1606c44bf1409abd5b)
- version was updated [`d3d98b5`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/d3d98b58591fe4578bb3d941e5db020177bc0723)
- version was corrected [`8a89afa`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/8a89afa1bb978b4ba404ef6293a557cd933f572e)
- Jenkinsfile edited [`9548bd8`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/9548bd8f816e4a9d73996f8a2960729758e21155)
- "express-prom-bundle" (peerDependencies) was removed [`994f855`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/994f855e9961225326bcbf322a029cce71e96739)
- "kafka-node" was added as required for test step [`2004ebe`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/2004ebeb740862f19c34998cf9e0d6d72a38fb8b)

### [v2.0.0](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2F0.6.126&sourceBranch=refs%2Ftags%2Fv2.0.0)

> 4 October 2023

- SWS-41266: Bump version [`#SWS-41266`](https://jira.skywindgroup.com/browse/SWS-41266)
- SWS-41266: Modify BaseTokenData interface [`#SWS-41266`](https://jira.skywindgroup.com/browse/SWS-41266)
- comments were taking into account, some dependencies were updated  + NJSMGRN-48 [`#NJSMGRN-48`](https://jira.skywindgroup.com/browse/NJSMGRN-48)
- SWS-42374 fix build [`#SWS-42374`](https://jira.skywindgroup.com/browse/SWS-42374)
- SWS-42374 fix build [`#SWS-42374`](https://jira.skywindgroup.com/browse/SWS-42374)
- SWS-42374 add also externalId for PlayerPromotionInfo to do just only one request [`#SWS-42374`](https://jira.skywindgroup.com/browse/SWS-42374)
- fast-xml-parser was updated, some options are not supported anymore (it was deleted) NJSMGRN-28 [`#NJSMGRN-28`](https://jira.skywindgroup.com/browse/NJSMGRN-28)
- node version was updated NJSMGRN-26 [`#NJSMGRN-26`](https://jira.skywindgroup.com/browse/NJSMGRN-26)
- eslint was applied, some files were corrected according to eslint rules NJSMGRN-25 [`#NJSMGRN-25`](https://jira.skywindgroup.com/browse/NJSMGRN-25)
- bugfix/SWS-41755-pop-custom-error [`#SWS-41755`](https://jira.skywindgroup.com/browse/SWS-41755)
- sw-utils dependency was updated NJSMGRN-11 [`#NJSMGRN-11`](https://jira.skywindgroup.com/browse/NJSMGRN-11)
- skipLibCheck was added NJSMGRN-31 [`#NJSMGRN-31`](https://jira.skywindgroup.com/browse/NJSMGRN-31)
- sw-deferred-payment dependency was updated NJSMGRN-31 [`#NJSMGRN-31`](https://jira.skywindgroup.com/browse/NJSMGRN-31)
- lint was renamed NJSMGRN-48 [`#NJSMGRN-48`](https://jira.skywindgroup.com/browse/NJSMGRN-48)
- chai, jsonwebtoken, agentkeepalive libs were updated NJSMGRN-23 [`#NJSMGRN-23`](https://jira.skywindgroup.com/browse/NJSMGRN-23)
- mocha libs were updated NJSMGRN-23 [`#NJSMGRN-23`](https://jira.skywindgroup.com/browse/NJSMGRN-23)
- SWS-41662 bump version [`#SWS-41662`](https://jira.skywindgroup.com/browse/SWS-41662)
- SWS-41662 add isPromoInternal to type definition for GameTokenData [`#SWS-41662`](https://jira.skywindgroup.com/browse/SWS-41662)
- changing type of currencies [`#SWS-41243`](https://jira.skywindgroup.com/browse/SWS-41243)
- solving the conflicts [`#SWS-41243`](https://jira.skywindgroup.com/browse/SWS-41243)
- updating the getGamesInfo [`#SWS-41243`](https://jira.skywindgroup.com/browse/SWS-41243)
- SWS-35544 Add closeInSWWalletOnly param to the FinalizeGameRequest interface [`#SWS-35544`](https://jira.skywindgroup.com/browse/SWS-35544)
- added method for gameCode Info . new PR because the previous one was broken . [`#SWS-41243`](https://jira.skywindgroup.com/browse/SWS-41243)
- SWS-41082 fix conflict [`#SWS-41082`](https://jira.skywindgroup.com/browse/SWS-41082)
- SWS-41082 bump version [`#SWS-41082`](https://jira.skywindgroup.com/browse/SWS-41082)
- SWS-41034: Reduce gameprovider-api logs [`#SWS-41034`](https://jira.skywindgroup.com/browse/SWS-41034)
- SWS-41082 bump version [`#SWS-41082`](https://jira.skywindgroup.com/browse/SWS-41082)
- add necessary attributes for SWS-41082 [`#SWS-41082`](https://jira.skywindgroup.com/browse/SWS-41082)
- SWS-40562 Add lb ans csh fields to startGameTokenData [`#SWS-40562`](https://jira.skywindgroup.com/browse/SWS-40562)
- feature/SWS-40429-round-result-library [`#SWS-40429`](https://jira.skywindgroup.com/browse/SWS-40429)
- hotfix/SWS-40429-stars-result-builder [`#SWS-40429`](https://jira.skywindgroup.com/browse/SWS-40429)
- hotfix/SWS-40429-stars-result-builder [`#SWS-40429`](https://jira.skywindgroup.com/browse/SWS-40429)
- feature/SWS-40429-stars-result-builder [`#SWS-40429`](https://jira.skywindgroup.com/browse/SWS-40429)
- feature/SWS-40429-stars-result-builder [`#SWS-40429`](https://jira.skywindgroup.com/browse/SWS-40429)
- rename hasJackpotIds to isReturnJackpotIdsEnabled  SWS-40389 [`#SWS-40389`](https://jira.skywindgroup.com/browse/SWS-40389)
- rename getJackpotIds to hasJackpotIds [`#SWS-40389`](https://jira.skywindgroup.com/browse/SWS-40389)
- add getJackpotIds in MerchantGameInitRequest [`#SWS-40389`](https://jira.skywindgroup.com/browse/SWS-40389)
- feature/PSTAR-45-round-result-details [`#PSTAR-45`](https://jira.skywindgroup.com/browse/PSTAR-45)
- SWS-40190: Prevent removing active promotions [`#SWS-40190`](https://jira.skywindgroup.com/browse/SWS-40190)
- bump version to 156 SWS-39671 [`#SWS-39671`](https://jira.skywindgroup.com/browse/SWS-39671)
- REV-66:Support for reevo free-bets [`#REV-66`](https://jira.skywindgroup.com/browse/REV-66)
- remove auth token [`#SWS-39671`](https://jira.skywindgroup.com/browse/SWS-39671)
- remove comment because gameToken is optional and is not sent in seamless adapter [`#SWS-39671`](https://jira.skywindgroup.com/browse/SWS-39671)
- do gameToken optional in RegisterRoundRequest [`#SWS-39671`](https://jira.skywindgroup.com/browse/SWS-39671)
- add merch_id and merch_password to RegisterRoundResponse [`#SWS-39671`](https://jira.skywindgroup.com/browse/SWS-39671)
- SWS-38047: add balances to game token data inteface [`#SWS-38047`](https://jira.skywindgroup.com/browse/SWS-38047)
- SWS-37797: add method to internal api to get spin image [`#SWS-37797`](https://jira.skywindgroup.com/browse/SWS-37797)
- SWS-37797: join finalization enums for backward compatibility [`#SWS-37797`](https://jira.skywindgroup.com/browse/SWS-37797)
- SWS-37797: add disablePlayerPhantomFeatures flag to base game token data [`#SWS-37797`](https://jira.skywindgroup.com/browse/SWS-37797)
- SWS-37562: Finalization improvements - fix comments [`#SWS-37562`](https://jira.skywindgroup.com/browse/SWS-37562)
- SWS-37562: Finalization improvements - fix comments [`#SWS-37562`](https://jira.skywindgroup.com/browse/SWS-37562)
- SWS-37562: Finalization improvements - fix comments [`#SWS-37562`](https://jira.skywindgroup.com/browse/SWS-37562)
- SWS-37562: Finalization improvements - split BrandFinalizationType [`#SWS-37562`](https://jira.skywindgroup.com/browse/SWS-37562)
- SWS-37381 Add nickname to PlayerInfo [`#SWS-37381`](https://jira.skywindgroup.com/browse/SWS-37381)
- SWS-37381 Add nickname to startGameToken [`#SWS-37381`](https://jira.skywindgroup.com/browse/SWS-37381)
- SWS-37040 Bump version [`#SWS-37040`](https://jira.skywindgroup.com/browse/SWS-37040)
- SWS-37040 Add licenseeId to the StartGameTokenData interface [`#SWS-37040`](https://jira.skywindgroup.com/browse/SWS-37040)
- SWS-36818 - Use player country from operator in geographical restrictions on game launch [`#SWS-36818`](https://jira.skywindgroup.com/browse/SWS-36818)
- SWS-36818 - Use player country from operator in geographical restrictions on game launch [`#SWS-36818`](https://jira.skywindgroup.com/browse/SWS-36818)
- SWS-36818 - Use player country from operator in geographical restrictions on game launch [`#SWS-36818`](https://jira.skywindgroup.com/browse/SWS-36818)
- SWS-36818 - Use player country from operator in geographical restrictions on game launch [`#SWS-36818`](https://jira.skywindgroup.com/browse/SWS-36818)
- SWS-31589: Add additional field to the IPM protocol with operation timestamp, that will be the same in case of retransmissions [`#SWS-31589`](https://jira.skywindgroup.com/browse/SWS-31589)
- SWS-31589: Add additional field to the IPM protocol with operation timestamp, that will be the same in case of retransmissions [`#SWS-31589`](https://jira.skywindgroup.com/browse/SWS-31589)
- SWS-35990 update package.json version [`#SWS-35990`](https://jira.skywindgroup.com/browse/SWS-35990)
- SWS-35783 Fix tslint [`#SWS-35783`](https://jira.skywindgroup.com/browse/SWS-35783)
- SWS-35783 Add enpoint for game history by roundIds [`#SWS-35783`](https://jira.skywindgroup.com/browse/SWS-35783)
- SWS-35990 allow baseUrl have question mark in the end [`#SWS-35990`](https://jira.skywindgroup.com/browse/SWS-35990)
- SWS-35438: Implement support list of parameters for hash calculation per operator. [`#SWS-35438`](https://jira.skywindgroup.com/browse/SWS-35438)
- SWS-35237 revert 'Fix getFullUrl logic to allow baseUrl to include question mark' PR [`#SWS-35237`](https://jira.skywindgroup.com/browse/SWS-35237)
- Revert "SWS-35237: Add tests" [`#SWS-35237`](https://jira.skywindgroup.com/browse/SWS-35237)
- SWS-35667 Add gameTitle to smResultDetails [`#SWS-35667`](https://jira.skywindgroup.com/browse/SWS-35667)
- SWS-35237: Add tests [`#SWS-35237`](https://jira.skywindgroup.com/browse/SWS-35237)
- SWS-35237: Update version [`#SWS-35237`](https://jira.skywindgroup.com/browse/SWS-35237)
- SWS-35237: Fix getFullUrl logic to allow baseUrl to include question mark [`#SWS-35237`](https://jira.skywindgroup.com/browse/SWS-35237)
- SWS-31589: define configs for add timestamp in bonus api request [`#SWS-31589`](https://jira.skywindgroup.com/browse/SWS-31589)
- SWS-31589: define configs for add timestamp in bonus api request [`#SWS-31589`](https://jira.skywindgroup.com/browse/SWS-31589)
- SWS-31589: define configs for add timestamp in bonus api request [`#SWS-31589`](https://jira.skywindgroup.com/browse/SWS-31589)
- SWS-31589: define configs for add timestamp in bonus api request [`#SWS-31589`](https://jira.skywindgroup.com/browse/SWS-31589)
- SWS-31589: define configs for add timestamp in bonus api request [`#SWS-31589`](https://jira.skywindgroup.com/browse/SWS-31589)
- SWS-35124 Improve logging for errors thrown by node [`#SWS-35124`](https://jira.skywindgroup.com/browse/SWS-35124)
- SWS-33704: Support distributionType for offline deferred payment [`#SWS-33704`](https://jira.skywindgroup.com/browse/SWS-33704)
- SWS-34556 update package version [`#SWS-34556`](https://jira.skywindgroup.com/browse/SWS-34556)
- SWS-34556 add get criticalFiles methods for games and platform to internalAPIService [`#SWS-34556`](https://jira.skywindgroup.com/browse/SWS-34556)
- SWS-31549 Add smResultExtraData [`#SWS-31549`](https://jira.skywindgroup.com/browse/SWS-31549)
- SWS-34360: Bump version [`#SWS-34360`](https://jira.skywindgroup.com/browse/SWS-34360)
- SWS-34360: Add interruptSocketForLiveGame common method [`#SWS-34360`](https://jira.skywindgroup.com/browse/SWS-34360)
- SWS-34254: Bump version [`#SWS-34254`](https://jira.skywindgroup.com/browse/SWS-34254)
- SWS-34254: Add PLATFORM_TYPE enum [`#SWS-34254`](https://jira.skywindgroup.com/browse/SWS-34254)
- SWS-33704: Update sw-deferred-payment to 1.15.0 [`#SWS-33704`](https://jira.skywindgroup.com/browse/SWS-33704)
- SWS-33704: add supportPromoDistributionType to merchant params [`#SWS-33704`](https://jira.skywindgroup.com/browse/SWS-33704)
- SWS-33704: add supportPromoDistributionType to merchant params [`#SWS-33704`](https://jira.skywindgroup.com/browse/SWS-33704)
- SWS-33704: add supportDistributionType to merchant params [`#SWS-33704`](https://jira.skywindgroup.com/browse/SWS-33704)
- SWS-33632: Add roundPID to rollback request [`#SWS-33632`](https://jira.skywindgroup.com/browse/SWS-33632)
- gulp was deleted [`c5208b9`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/c5208b9d3edfecb320c44299a1c0026be3b6124e)
- add registerRound to paymentService [`2be6b80`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/2be6b80f1eef542957c2ba72ad198e9ad5fc88ed)
- readme was added [`7ac4643`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/7ac464312223abcaa005361a1ac25866661b21db)
- clean, compile command were updated. "status": "gulp status" was deleted (originaly, it was not iplemented) [`e9fdba0`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/e9fdba0cccdd1444a03ee43bb11d3a9ae7181981)
- complete the def for get Games [`96de00e`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/96de00e3d892bb5bcabfe8f6b313889117b26d0e)
- moved to  "target": "es2022" [`7242c3f`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/7242c3f90f537989e41824135519a25d67a4d772)
- eslint-plugin version was updated to stable [`866e167`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/866e1675c7daabf6429a3e35729f6564c9a6c5a4)
- superagent libs were updated. "gulp test" was replaced with mocha command [`2dceb6c`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/2dceb6c689bd3221c683f616cc30a59f0b15b387)
- Jenkinsfile edited: @version2 [`701e6e2`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/701e6e2041ff899afb0747867910cee47ed5dae0)
- Jenkinsfile edited [`e446595`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/e44659508c2e7db9aa12fcacdd40012c35dd0bef)
- Jenkinsfile edited [`b9e5867`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/b9e5867e4a9a3742f8eb185a8da7d7da0ed1eb4a)
- node version was updated for Jenkins job [`bc03175`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/bc03175557eaf3ce2a83123fa1f575f84664dcbf)

#### [0.6.126](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2F0.6.20&sourceBranch=refs%2Ftags%2F0.6.126)

> 25 April 2022

- SWS-33632: Add roundPID to rollback request [`#SWS-33632`](https://jira.skywindgroup.com/browse/SWS-33632)
- SWS-32559 Add global exports for promo enums [`#SWS-32559`](https://jira.skywindgroup.com/browse/SWS-32559)
- SWS-32559 Remove "tournament" promo type [`#SWS-32559`](https://jira.skywindgroup.com/browse/SWS-32559)
- SWS-32559 Add common Promo logic [`#SWS-32559`](https://jira.skywindgroup.com/browse/SWS-32559)
- SWS-32559 Add common Promo logic [`#SWS-32559`](https://jira.skywindgroup.com/browse/SWS-32559)
- bugfix/SWS-32177-password-is-not-hidden [`#SWS-32177`](https://jira.skywindgroup.com/browse/SWS-32177)
- SWS-32255: Add InterruptSocketError [`#SWS-32255`](https://jira.skywindgroup.com/browse/SWS-32255)
- SWS-30874 Fix setServerCall method not having a return type [`#SWS-30874`](https://jira.skywindgroup.com/browse/SWS-30874)
- bugfix/MICGMG-213-playcheck-language [`#MICGMG-213`](https://jira.skywindgroup.com/browse/MICGMG-213)
- bugfix/MICGMG-213-playcheck-language [`#MICGMG-213`](https://jira.skywindgroup.com/browse/MICGMG-213)
- SWS-31293: Unify finalization methods [`#SWS-31293`](https://jira.skywindgroup.com/browse/SWS-31293)
- SWS-29392: Add previousGameToken in game init request [`#SWS-29392`](https://jira.skywindgroup.com/browse/SWS-29392)
- SWS-29392: Add previousGameToken in game init request [`#SWS-29392`](https://jira.skywindgroup.com/browse/SWS-29392)
- SWS-29392: Add previousGameToken in game init request [`#SWS-29392`](https://jira.skywindgroup.com/browse/SWS-29392)
- SWS-30874 Bumped version [`#SWS-30874`](https://jira.skywindgroup.com/browse/SWS-30874)
- SWS-30874 Added optional env parameter inside the startGameTokenData interface [`#SWS-30874`](https://jira.skywindgroup.com/browse/SWS-30874)
- BANGINT-128 Added externalGameId as an optional parameter on the MerchantGameInitRequest interface [`#BANGINT-128`](https://jira.skywindgroup.com/browse/BANGINT-128)
- SWS-30896: Disable online retries for live game [`#SWS-30896`](https://jira.skywindgroup.com/browse/SWS-30896)
- SWS-30900 - Add new finalization type "Manual Payments" - update comment [`#SWS-30900`](https://jira.skywindgroup.com/browse/SWS-30900)
- SWS-30900 - Add new finalization type "Manual Payments" [`#SWS-30900`](https://jira.skywindgroup.com/browse/SWS-30900)
- SWS-30626 Add smResult field to Payment [`#SWS-30626`](https://jira.skywindgroup.com/browse/SWS-30626)
- SWS-30450: Support new error code and new translation approach [`#SWS-30450`](https://jira.skywindgroup.com/browse/SWS-30450)
- Automatic merge from release/4.64 -&gt; develop [`#SWS-30663`](https://jira.skywindgroup.com/browse/SWS-30663)
- feature/SWS-30663-external-game-id [`#SWS-30663`](https://jira.skywindgroup.com/browse/SWS-30663)
- feature/MICGMG-182-games-mapping [`#MICGMG-182`](https://jira.skywindgroup.com/browse/MICGMG-182)
- SWS-30266: Rename gb2 to british [`#SWS-30266`](https://jira.skywindgroup.com/browse/SWS-30266)
- SWS-29815 remove required [`#SWS-29815`](https://jira.skywindgroup.com/browse/SWS-29815)
- SWS-29815 fix tslint [`#SWS-29815`](https://jira.skywindgroup.com/browse/SWS-29815)
- SWS-29815 export interface [`#SWS-29815`](https://jira.skywindgroup.com/browse/SWS-29815)
- SWS-29815 pass freebets balance to get game token info [`#SWS-29815`](https://jira.skywindgroup.com/browse/SWS-29815)
- SWS-29814 fix interface [`#SWS-29814`](https://jira.skywindgroup.com/browse/SWS-29814)
- SWS-29814 fix tslint [`#SWS-29814`](https://jira.skywindgroup.com/browse/SWS-29814)
- SWS-29814 add new method [`#SWS-29814`](https://jira.skywindgroup.com/browse/SWS-29814)
- SWS-29814 increase version [`#SWS-29814`](https://jira.skywindgroup.com/browse/SWS-29814)
- SWS-29814 add flag to interface [`#SWS-29814`](https://jira.skywindgroup.com/browse/SWS-29814)
- SWS-29466 Bumped version [`#SWS-29466`](https://jira.skywindgroup.com/browse/SWS-29466)
- SWS-29466 Added swiss regulation to the merchant regulations list [`#SWS-29466`](https://jira.skywindgroup.com/browse/SWS-29466)
- feature/MICGMG-12-Basic-Wallet-Bet: [`#MICGMG-12`](https://jira.skywindgroup.com/browse/MICGMG-12)
- SWS-28642 - Add another free-bets extension which will run before the normal free-bets extension - add skip validation flag [`#SWS-28642`](https://jira.skywindgroup.com/browse/SWS-28642)
- SWS-26113 Made parameters mandatory [`#SWS-26113`](https://jira.skywindgroup.com/browse/SWS-26113)
- SWS-26113 Update fetching jackpots from phantom [`#SWS-26113`](https://jira.skywindgroup.com/browse/SWS-26113)
- SWS-28645 increase version [`#SWS-28645`](https://jira.skywindgroup.com/browse/SWS-28645)
- SWS-28645 update interface with isExternalLogin [`#SWS-28645`](https://jira.skywindgroup.com/browse/SWS-28645)
- SWS-26113: add phantom service [`#SWS-26113`](https://jira.skywindgroup.com/browse/SWS-26113)
- SWS-28129 [ESL][Bonus API] Development Request for externalId Param [`#SWS-28129`](https://jira.skywindgroup.com/browse/SWS-28129)
- SWS-27854 fix login terminal request [`#SWS-27854`](https://jira.skywindgroup.com/browse/SWS-27854)
- QTECH-269: remove ticket from LoginTerminalRequest [`#QTECH-269`](https://jira.skywindgroup.com/browse/QTECH-269)
- QTECH-269: LoginTerminalRequest contains any additional fields [`#QTECH-269`](https://jira.skywindgroup.com/browse/QTECH-269)
- SWS-27853 fix tslint [`#SWS-27853`](https://jira.skywindgroup.com/browse/SWS-27853)
- SWS-27853 add login terminal response to index [`#SWS-27853`](https://jira.skywindgroup.com/browse/SWS-27853)
- SWS-27853 rewrite login terminal protocol [`#SWS-27853`](https://jira.skywindgroup.com/browse/SWS-27853)
- SWS-27897: fix lint [`#SWS-27897`](https://jira.skywindgroup.com/browse/SWS-27897)
- SWS-27897: add new internal api method [`#SWS-27897`](https://jira.skywindgroup.com/browse/SWS-27897)
- SWS-27853 updates interfaces [`#SWS-27853`](https://jira.skywindgroup.com/browse/SWS-27853)
- SWS-27853 add support 3rd party login terminal [`#SWS-27853`](https://jira.skywindgroup.com/browse/SWS-27853)
- SWS-27326: update interface [`#SWS-27326`](https://jira.skywindgroup.com/browse/SWS-27326)
- SWS-27326: update interface [`#SWS-27326`](https://jira.skywindgroup.com/browse/SWS-27326)
- SWS-27326: update interface [`#SWS-27326`](https://jira.skywindgroup.com/browse/SWS-27326)
- SWS-27326: update interface [`#SWS-27326`](https://jira.skywindgroup.com/browse/SWS-27326)
- SWS-27049 - [Seamless][Romanian regulation] Add betCount field to transfer-out requests [`#SWS-27049`](https://jira.skywindgroup.com/browse/SWS-27049)
- SWS-26725 add tests [`#SWS-26725`](https://jira.skywindgroup.com/browse/SWS-26725)
- SWS-26725 add tests [`#SWS-26725`](https://jira.skywindgroup.com/browse/SWS-26725)
- SWS-26725 add get domain function [`#SWS-26725`](https://jira.skywindgroup.com/browse/SWS-26725)
- SWS-18862 Update references [`#SWS-18862`](https://jira.skywindgroup.com/browse/SWS-18862)
- SWS-26328 add operator site id [`#SWS-26328`](https://jira.skywindgroup.com/browse/SWS-26328)
- SWS-26262 save rtp deduction to request [`#SWS-26262`](https://jira.skywindgroup.com/browse/SWS-26262)
- SWS-26118 add comment [`#SWS-26118`](https://jira.skywindgroup.com/browse/SWS-26118)
- SWS-26118 add operatorSiteExternalCode to merchant game token info [`#SWS-26118`](https://jira.skywindgroup.com/browse/SWS-26118)
- SWSSI-25561 fix type [`#SWSSI-25561`](https://jira.skywindgroup.com/browse/SWSSI-25561)
- SWSSI-25561 get short structure [`#SWSSI-25561`](https://jira.skywindgroup.com/browse/SWSSI-25561)
- SWS-25561: Call Phantom API to get the list of JPs and get the tickers for it [`#SWS-25561`](https://jira.skywindgroup.com/browse/SWS-25561)
- SWS-24005 Fix processResponse error handling [`#SWS-24005`](https://jira.skywindgroup.com/browse/SWS-24005)
- SWS-21835 Refactor cert module [`#SWS-21835`](https://jira.skywindgroup.com/browse/SWS-21835)
- SWS-21835 Fix error [`#SWS-21835`](https://jira.skywindgroup.com/browse/SWS-21835)
- SWS-21835 Alter getEnvValue [`#SWS-21835`](https://jira.skywindgroup.com/browse/SWS-21835)
- SWS-21835 ssl extension to httpBaseRequest [`#SWS-21835`](https://jira.skywindgroup.com/browse/SWS-21835)
- SWSSI-23128 password bonus auth [`#SWSSI-23128`](https://jira.skywindgroup.com/browse/SWSSI-23128)
- SWSSI-23128 password bonus auth [`#SWSSI-23128`](https://jira.skywindgroup.com/browse/SWSSI-23128)
- SWS-22616 - Add "freeBetsDisabled" field to game token [`#SWS-22616`](https://jira.skywindgroup.com/browse/SWS-22616)
- SWS-22340: update version [`#SWS-22340`](https://jira.skywindgroup.com/browse/SWS-22340)
- SWS-22340: fix exporting [`#SWS-22340`](https://jira.skywindgroup.com/browse/SWS-22340)
- SWS-22340: update version [`#SWS-22340`](https://jira.skywindgroup.com/browse/SWS-22340)
- SWS-22340: add error [`#SWS-22340`](https://jira.skywindgroup.com/browse/SWS-22340)
- SWS-21999 Add jp isLocal support [`#SWS-21999`](https://jira.skywindgroup.com/browse/SWS-21999)
- SWS-22081 Extend PAYMENT_TYPE [`#SWS-22081`](https://jira.skywindgroup.com/browse/SWS-22081)
- SWS-19269 - added secured object data of request to log [`#SWS-19269`](https://jira.skywindgroup.com/browse/SWS-19269)
- SWS-21779: refine offline bonus payment interfaces [`#SWS-21779`](https://jira.skywindgroup.com/browse/SWS-21779)
- SWS-21774 - added new parameter custId for offline bonus payments [`#SWS-21774`](https://jira.skywindgroup.com/browse/SWS-21774)
- SWS-21774 - added implementation of new method to MerchantAdapterDecorator [`#SWS-21774`](https://jira.skywindgroup.com/browse/SWS-21774)
- SWS-21774 - new method offline bonus payments [`#SWS-21774`](https://jira.skywindgroup.com/browse/SWS-21774)
- SWS-21444 Add progressive part of jackpot win to jackpot payment [`#SWS-21444`](https://jira.skywindgroup.com/browse/SWS-21444)
- SWS-21216 [Tuko][EU STG][Seamless integration] Provide to Client Session Id and Ticket Id [`#SWS-21216`](https://jira.skywindgroup.com/browse/SWS-21216)
- SWS-21444 Add progressive part of jackpot win to jackpot payment [`#SWS-21444`](https://jira.skywindgroup.com/browse/SWS-21444)
- SWSSI-229 sw-wallet-adapter-core fix interface for GameRound (roundId - is number) [`#SWSSI-229`](https://jira.skywindgroup.com/browse/SWSSI-229)
- SWS-21216 [Tuko][EU STG][Seamless integration] Provide to Client Session Id and Ticket Id [`#SWS-21216`](https://jira.skywindgroup.com/browse/SWS-21216)
- SWS-21216 [Tuko][EU STG][Seamless integration] Provide to Client Session Id and Ticket Id [`#SWS-21216`](https://jira.skywindgroup.com/browse/SWS-21216)
- SWSSI-229 roundId number [`#SWSSI-229`](https://jira.skywindgroup.com/browse/SWSSI-229)
- SWS-21279 Calculation session time and net position for UK2 [`#SWS-21279`](https://jira.skywindgroup.com/browse/SWS-21279)
- SWS-21279 Calculation session time and net position for UK2 [`#SWS-21279`](https://jira.skywindgroup.com/browse/SWS-21279)
- SWS-21279 Calculation session time and net position for UK2 [`#SWS-21279`](https://jira.skywindgroup.com/browse/SWS-21279)
- SWS-20104 Remove redundant file [`#SWS-20104`](https://jira.skywindgroup.com/browse/SWS-20104)
- SWS-20104 Add new PopupButtonAction [`#SWS-20104`](https://jira.skywindgroup.com/browse/SWS-20104)
- SWS-20683 fix tslint [`#SWS-20683`](https://jira.skywindgroup.com/browse/SWS-20683)
- SWS-20683 remove uselss code [`#SWS-20683`](https://jira.skywindgroup.com/browse/SWS-20683)
- SWS-20683 review fix #1 [`#SWS-20683`](https://jira.skywindgroup.com/browse/SWS-20683)
- SWS-20683 fix circular object in error [`#SWS-20683`](https://jira.skywindgroup.com/browse/SWS-20683)
- SWS-20505 add jp type info to jackpotDetails [`#SWS-20505`](https://jira.skywindgroup.com/browse/SWS-20505)
- SWS-19132 add optional lobby session id [`#SWS-19132`](https://jira.skywindgroup.com/browse/SWS-19132)
- SWS-20371 [Sisal] Error "dataObject.hasOwnProperty is not a function" on getting critical files [`#SWS-20371`](https://jira.skywindgroup.com/browse/SWS-20371)
- SWS-20371 [Sisal] Error "dataObject.hasOwnProperty is not a function" on getting critical files [`#SWS-20371`](https://jira.skywindgroup.com/browse/SWS-20371)
- SWS-19130 Integrate critical files features into sisal adapters [`#SWS-19130`](https://jira.skywindgroup.com/browse/SWS-19130)
- SWS-19335 increase version [`#SWS-19335`](https://jira.skywindgroup.com/browse/SWS-19335)
- SWS-19335 add status [`#SWS-19335`](https://jira.skywindgroup.com/browse/SWS-19335)
- SWS-19335 fix tslint [`#SWS-19335`](https://jira.skywindgroup.com/browse/SWS-19335)
- SWS-19335 add feature to gameInfo interface [`#SWS-19335`](https://jira.skywindgroup.com/browse/SWS-19335)
- SWS-18776 - new flag in payment: free spin in free bet [`#SWS-18776`](https://jira.skywindgroup.com/browse/SWS-18776)
- SWS-18277 fix post request [`#SWS-18277`](https://jira.skywindgroup.com/browse/SWS-18277)
- SWS-18780 Merchant's password in the log. [`#SWS-18780`](https://jira.skywindgroup.com/browse/SWS-18780)
- SWS-18277 use Date.now() [`#SWS-18277`](https://jira.skywindgroup.com/browse/SWS-18277)
- SWS-18277 fix tslint [`#SWS-18277`](https://jira.skywindgroup.com/browse/SWS-18277)
- SWS-18277 small fix [`#SWS-18277`](https://jira.skywindgroup.com/browse/SWS-18277)
- SWS-18277 add request time [`#SWS-18277`](https://jira.skywindgroup.com/browse/SWS-18277)
- SWS-17981 update mechanism of building urls [`#SWS-17981`](https://jira.skywindgroup.com/browse/SWS-17981)
- SWS-18112 [POP External] Error in client on launching a game [`#SWS-18112`](https://jira.skywindgroup.com/browse/SWS-18112)
- SWS-17834 Change MerchantAdapterDecorator.commitBonusPayment [`#SWS-17834`](https://jira.skywindgroup.com/browse/SWS-17834)
- SWS-17654 Use promo.externalId field for promo_id field of BNS credit request [`#SWS-17654`](https://jira.skywindgroup.com/browse/SWS-17654)
- SWS-17479 update version [`#SWS-17479`](https://jira.skywindgroup.com/browse/SWS-17479)
- SWS-17479 fixing full URL concatenating method [`#SWS-17479`](https://jira.skywindgroup.com/browse/SWS-17479)
- SWS-15978 remove redundant balance fields [`#SWS-15978`](https://jira.skywindgroup.com/browse/SWS-15978)
- SWS-16766 refactoring. [`#SWS-16766`](https://jira.skywindgroup.com/browse/SWS-16766)
- SWS-16543 Refactor to use URL [`#SWS-16543`](https://jira.skywindgroup.com/browse/SWS-16543)
- SWS-16543 Support base urls ended with / [`#SWS-16543`](https://jira.skywindgroup.com/browse/SWS-16543)
- SWS-16543 Support urls started with / [`#SWS-16543`](https://jira.skywindgroup.com/browse/SWS-16543)
- GAMING1-115: Fix logs output messages [`d7b7ce6`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/d7b7ce65b2566dd69024d30502aef42f20a10063)
- GAMING1-100: Detail errors of xml parser [`ffb917f`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/ffb917f4637726b7ee6f779345cc4a9f42495cd6)
- New finalization logic - update interface [`09de775`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/09de775e3003777135ff1b8fa79315ef8c341a09)
- GAMING1-115: Fix logs output messages [`53a1c4a`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/53a1c4aee3c370e0f27f00d957dd4d717c143a79)
- SWB365-262 - Free Bet: Internal server error if FreeSpinToken expired during play free bets [`73b6fe7`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/73b6fe76d9cc5f211526611b185efbaac22e5a68)
- Add env wrapper [`5e4634d`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/5e4634d5da802fa452d2db6735a6c8a536146d01)
- HUB88-100: game type interface [`c2ce962`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/c2ce9628d07e76deec7a2eb212ecd4d5d7df2d00)
- SWB365-250 - Add settings to the gameToken [`0f078ae`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/0f078aeec2325216727f320e9dfdce6541c0d6dc)
- Alter baseRequest [`9182111`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/9182111e53c50f20299f0b302baa4095cdf1a358)
- SWBETV2-94 Add multibet info to GameTokenData [`2eb7cb2`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/2eb7cb2332f012251528dc80da704ddb6996e304)
- SWBETV2-94 Multibet info in start game token [`2b04e1f`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/2b04e1feeb66a20581be5eebd8a94d6a858031d7)
- HUB88-100: update interface [`8b605ef`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/8b605ef0cb64991866d9bb092009ebec3138632c)
- HUB88-100: game type interface [`2120cb0`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/2120cb017f41e1b8dcf6973d8e2c8097a64ef46c)
- GAMING1-115: Fix logs output messages [`3301705`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/33017059bc14d2336572a00b129cd406b968ba5b)
- GAMING1-115: Fix logs output messages [`559a5c0`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/559a5c03a5904e6fc099ce4a2733904dd4834f83)
- Fix ca processing [`370370b`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/370370b5485dd14073578bfb546531c613575861)
- SWB365-250 - Add settings to the gameToken attempt #2 [`ba6bd00`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/ba6bd00ec219b45d0223f1051b2ef9a82dd1a05a)
- fix tslint [`e736fc3`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/e736fc396a6e623fc8e3b6aaf96320302ddc3e33)
- SWB365-262 - grr.... [`83d0906`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/83d0906d7ba6f186e85ece8b4bb8b841d1ce1fb1)
- GAMING1-115: Fix logs output messages [`ae5bf9c`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/ae5bf9c12273c394f68ba5215a9cbe37a219395a)
- GAMING1-15 Add currency field into payment request [`634152e`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/634152e9568ee36fcb2f98e3a34abfc5b982946f)
- Change ca process [`aee6ea8`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/aee6ea80b49904238ca926410f231b5555b24a62)
- version bump: [`ef6ab61`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/ef6ab613a3f2007837cc60b34a4aec1d5ece5b0b)
- HUB88-100: lint [`df86b24`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/df86b24f5f66d6a999c10688669531fda8a027b0)
- package.json edited online with Bitbucket [`e4e4d4b`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/e4e4d4b483167715022838a6d9013950c62b43fd)
- GAMING1-115: Fix logs output messages [`adf5e19`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/adf5e19bc3391ef8ae39e7dbc0fb5dd778577afc)
- SWBETV2-134 change version [`0b20a1d`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/0b20a1d5b1363a636939a8fc5e8e307cb4659dd1)
- Bump version [`4143e8e`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/4143e8e5736f938d121f9396efbe928a0d79f05e)
- SWBETV2-94 Change version [`a936227`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/a936227239fb198c2224dd102aee3f5ef3cac012)
- SWBETV2-94 isMultibet flag as nullable [`6e3bf34`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/6e3bf34075ef49638a657ead020eaf9fd672b14d)
- SWB365-262 - version [`3ab6ad4`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/3ab6ad4ad7556937d6b5ca585c1d635d5fb28d30)
- SWB365-232: update version [`f73d584`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/f73d584e1ea29ef2e48be3c4418d51393c363882)
- SWB365-232: update getSecuredObjectData [`ca0b984`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/ca0b9849db828f387a06b2deebf04ee1afe270f0)
- increase version [`3928e3e`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/3928e3e9e110529fdbed4d878031d73d3f724804)
- SWBETV2-134 add operation field to PaymentRequest [`c7f0e6b`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/c7f0e6b6e358b8fbfe5680b77d7f3b6a5dac8b61)
- Fix syntax [`9e8586d`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/9e8586d3ecdcba68cfa2821250717a810f64ab18)

#### [0.6.20](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2F0.6.11&sourceBranch=refs%2Ftags%2F0.6.20)

> 2 March 2020

- SWS-15350 refactoring [`#SWS-15350`](https://jira.skywindgroup.com/browse/SWS-15350)
- SWS-15350 merge [`#SWS-15350`](https://jira.skywindgroup.com/browse/SWS-15350)
- SWS-15350 update logging [`#SWS-15350`](https://jira.skywindgroup.com/browse/SWS-15350)
- SWS-15972 Add base http options [`#SWS-15972`](https://jira.skywindgroup.com/browse/SWS-15972)
- SWS-16346 - [GVC_HTTP][DEFERRED_PMNT] "Merchant internal error: Not supported bonus payment - tournament" in response to "/v1/payment/start" [`#SWS-16346`](https://jira.skywindgroup.com/browse/SWS-16346)
- SWS-13367 Remove unused field [`#SWS-13367`](https://jira.skywindgroup.com/browse/SWS-13367)
- SWS-13367 Start game improvements [`#SWS-13367`](https://jira.skywindgroup.com/browse/SWS-13367)
- SWS-15972 Merchant adapter decorator [`#SWS-15972`](https://jira.skywindgroup.com/browse/SWS-15972)
- SWS-16291 Fix utils and init logger [`#SWS-16291`](https://jira.skywindgroup.com/browse/SWS-16291)
- SWS-16291 Add request logging [`#SWS-16291`](https://jira.skywindgroup.com/browse/SWS-16291)
- SWS-16291 Support of ipm & mrch for seamless [`#SWS-16291`](https://jira.skywindgroup.com/browse/SWS-16291)
- SWS-13374 Fix login terminal & keep alive [`#SWS-13374`](https://jira.skywindgroup.com/browse/SWS-13374)
- SWS-13374 Update player code [`#SWS-13374`](https://jira.skywindgroup.com/browse/SWS-13374)
- SWS-13374 Fix merchant info urls [`#SWS-13374`](https://jira.skywindgroup.com/browse/SWS-13374)
- SWS-15888 [Betconstruct] Enhance protocol for operator needs [`#SWS-15888`](https://jira.skywindgroup.com/browse/SWS-15888)
- SWS-15888 [Betconstruct] Enhance protocol for operator needs [`#SWS-15888`](https://jira.skywindgroup.com/browse/SWS-15888)

#### [0.6.11](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/compare/diff?targetBranch=refs%2Ftags%2F0.4.30&sourceBranch=refs%2Ftags%2F0.6.11)

> 24 January 2020

- SWS-15767 gameSessionId not required in payment request [`#SWS-15767`](https://jira.skywindgroup.com/browse/SWS-15767)
- SWS-14780 Add keepalive for merchant [`#SWS-14780`](https://jira.skywindgroup.com/browse/SWS-14780)
- SWS-15283 add gameSessionId [`#SWS-15283`](https://jira.skywindgroup.com/browse/SWS-15283)
- SWS-14638 Send Additional information about GRC redeem to IPM [`#SWS-14638`](https://jira.skywindgroup.com/browse/SWS-14638)
- SWS-13185 increase version [`#SWS-13185`](https://jira.skywindgroup.com/browse/SWS-13185)
- SWS-15200 Add deviceId to BaseGameTokenData [`#SWS-15200`](https://jira.skywindgroup.com/browse/SWS-15200)
- SWS-13185 add ssl options [`#SWS-13185`](https://jira.skywindgroup.com/browse/SWS-13185)
- SWS-15130 fix [`#SWS-15130`](https://jira.skywindgroup.com/browse/SWS-15130)
- SWS-15130 extra params are optional [`#SWS-15130`](https://jira.skywindgroup.com/browse/SWS-15130)
- SWS-15130 extra payment paramters [`#SWS-15130`](https://jira.skywindgroup.com/browse/SWS-15130)
- SWS-13588 Extended interface for game and platform critical files interface [`#SWS-13588`](https://jira.skywindgroup.com/browse/SWS-13588)
- SWS-15151 fix tslint [`#SWS-15151`](https://jira.skywindgroup.com/browse/SWS-15151)
- SWS-15151 add commitBonusPayment method to httpPaymentService [`#SWS-15151`](https://jira.skywindgroup.com/browse/SWS-15151)
- SWS-8464 [POP] Implement setlimits call to POP [`#SWS-8464`](https://jira.skywindgroup.com/browse/SWS-8464)
- SWS-14389 Add new game action button [`#SWS-14389`](https://jira.skywindgroup.com/browse/SWS-14389)
- SWS-14491 Export of ModuleWithHashList interface [`#SWS-14491`](https://jira.skywindgroup.com/browse/SWS-14491)
- SWS-14491 Bumped minor version [`#SWS-14491`](https://jira.skywindgroup.com/browse/SWS-14491)
- SWS-14491 Added ModuleWithHashList interface, modified ReportCriticalFilesToMerchantRequest [`#SWS-14491`](https://jira.skywindgroup.com/browse/SWS-14491)
- SWS-14336 - Implement bonus api to be able to commit deferred payment - PR remove stub method [`#SWS-14336`](https://jira.skywindgroup.com/browse/SWS-14336)
- SWS-14336 - Implement bonus api to be able to commit deferred payment [`#SWS-14336`](https://jira.skywindgroup.com/browse/SWS-14336)
- SWS-13184 change finalize interface [`#SWS-13184`](https://jira.skywindgroup.com/browse/SWS-13184)
- SWS-13184 add game token info import [`#SWS-13184`](https://jira.skywindgroup.com/browse/SWS-13184)
- SWS-13184 remove segmentId from start game token [`#SWS-13184`](https://jira.skywindgroup.com/browse/SWS-13184)
- SWS-13184 extend get gameTokenData method [`#SWS-13184`](https://jira.skywindgroup.com/browse/SWS-13184)
- SWS-13184 fix refund [`#SWS-13184`](https://jira.skywindgroup.com/browse/SWS-13184)
- SWS-13184 add missing interfaces [`#SWS-13184`](https://jira.skywindgroup.com/browse/SWS-13184)
- SWS-13184 add critical files interface [`#SWS-13184`](https://jira.skywindgroup.com/browse/SWS-13184)
- SWS-13184 fix externalResellerPath type [`#SWS-13184`](https://jira.skywindgroup.com/browse/SWS-13184)
- SWS-13184 fix tslint [`#SWS-13184`](https://jira.skywindgroup.com/browse/SWS-13184)
- SWS-13184 increase version [`#SWS-13184`](https://jira.skywindgroup.com/browse/SWS-13184)
- SWS-13184 remove useless patterns [`#SWS-13184`](https://jira.skywindgroup.com/browse/SWS-13184)
- SWS-13184 add interface for superagent response [`#SWS-13184`](https://jira.skywindgroup.com/browse/SWS-13184)
- SWS-13616 add new error to export [`#SWS-13616`](https://jira.skywindgroup.com/browse/SWS-13616)
- SWS-13616 fix tslint [`#SWS-13616`](https://jira.skywindgroup.com/browse/SWS-13616)
- SWS-13616 add stop offline retransmission error [`#SWS-13616`](https://jira.skywindgroup.com/browse/SWS-13616)
- SWS-13702 remove body [`#SWS-13702`](https://jira.skywindgroup.com/browse/SWS-13702)
- SWS-13702 fix missing trace id [`#SWS-13702`](https://jira.skywindgroup.com/browse/SWS-13702)
- SWS-11718 increase version [`#SWS-11718`](https://jira.skywindgroup.com/browse/SWS-11718)
- SWS-11718 fix slash [`#SWS-11718`](https://jira.skywindgroup.com/browse/SWS-11718)
- SWS-11718 fix tslint [`#SWS-11718`](https://jira.skywindgroup.com/browse/SWS-11718)
- SWS-11718 add jackpot ticker service [`#SWS-11718`](https://jira.skywindgroup.com/browse/SWS-11718)
- SWS-11316 remove url resolve from http base service [`#SWS-11316`](https://jira.skywindgroup.com/browse/SWS-11316)
- SWS-11316 fix history endpoint [`#SWS-11316`](https://jira.skywindgroup.com/browse/SWS-11316)
- SWS-11316 fix tslint [`#SWS-11316`](https://jira.skywindgroup.com/browse/SWS-11316)
- SWS-12530 ability to skip timestamp in token [`#SWS-12530`](https://jira.skywindgroup.com/browse/SWS-12530)
- remove-url resolve [`ea55f9b`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/ea55f9be6b080218f18467c742ea6df953dff435)
- increase version [`a73df51`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/a73df51e4a928925efbd964e964a91bbcc5e0786)
- increase-version [`fac9376`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/fac937613c5601f4de8d0707ba44453e35deafaf)

#### 0.4.30

> 6 December 2019

- SWS-11316 implement internal API [`#SWS-11316`](https://jira.skywindgroup.com/browse/SWS-11316)
- SWS-11316 fix imports [`#SWS-11316`](https://jira.skywindgroup.com/browse/SWS-11316)
- SWS-11316 implement promotion internal API [`#SWS-11316`](https://jira.skywindgroup.com/browse/SWS-11316)
- SWS-11931 additional fields in FinalizeGame [`#SWS-11931`](https://jira.skywindgroup.com/browse/SWS-11931)
- SWS-11931 declare finalizeGame method [`#SWS-11931`](https://jira.skywindgroup.com/browse/SWS-11931)
- SWS-12166 remove duplicates [`#SWS-12166`](https://jira.skywindgroup.com/browse/SWS-12166)
- SWS-12166 update actionable interfaces [`#SWS-12166`](https://jira.skywindgroup.com/browse/SWS-12166)
- SWS-12022 increase version [`#SWS-12022`](https://jira.skywindgroup.com/browse/SWS-12022)
- SWS-12022 add forgotten export [`#SWS-12022`](https://jira.skywindgroup.com/browse/SWS-12022)
- SWS-12022 fix [`#SWS-12022`](https://jira.skywindgroup.com/browse/SWS-12022)
- SWS-12022 rollback gulp file [`#SWS-12022`](https://jira.skywindgroup.com/browse/SWS-12022)
- SWS-12022 fix field name [`#SWS-12022`](https://jira.skywindgroup.com/browse/SWS-12022)
- SWS-12022 refundBet operation, RegquireRefundBetError - as a marker [`#SWS-12022`](https://jira.skywindgroup.com/browse/SWS-12022)
- SWS-11334 remove tilda [`#SWS-11334`](https://jira.skywindgroup.com/browse/SWS-11334)
- SWS-11334 move superagent to devDep [`#SWS-11334`](https://jira.skywindgroup.com/browse/SWS-11334)
- SWS-11920 provide games api [`#SWS-11920`](https://jira.skywindgroup.com/browse/SWS-11920)
- SWS-10952 export extra data container [`#SWS-10952`](https://jira.skywindgroup.com/browse/SWS-10952)
- SWS-11073 fix return async [`#SWS-11073`](https://jira.skywindgroup.com/browse/SWS-11073)
- SWS-11073 rename methods [`#SWS-11073`](https://jira.skywindgroup.com/browse/SWS-11073)
- SWS-11073 update version [`#SWS-11073`](https://jira.skywindgroup.com/browse/SWS-11073)
- SWS-10872 pass data object to error constructor [`#SWS-10872`](https://jira.skywindgroup.com/browse/SWS-10872)
- SWS-10592 tslint [`#SWS-10592`](https://jira.skywindgroup.com/browse/SWS-10592)
- SWS-10592 generalize playmode fields for start/game tokens [`#SWS-10592`](https://jira.skywindgroup.com/browse/SWS-10592)
- SWS-9783 http success 2.xx [`#SWS-9783`](https://jira.skywindgroup.com/browse/SWS-9783)
- SWS-9783 Export interfaces [`#SWS-9783`](https://jira.skywindgroup.com/browse/SWS-9783)
- SWS-9783 Update version [`#SWS-9783`](https://jira.skywindgroup.com/browse/SWS-9783)
- SWS-9781 specila flag "finalize" in payment request [`#SWS-9781`](https://jira.skywindgroup.com/browse/SWS-9781)
- SWS-9783 Expose error [`#SWS-9783`](https://jira.skywindgroup.com/browse/SWS-9783)
- SWS-9802 add providerDetails to baseHTTPService [`#SWS-9802`](https://jira.skywindgroup.com/browse/SWS-9802)
- SWS-9802 add return type [`#SWS-9802`](https://jira.skywindgroup.com/browse/SWS-9802)
- SWS-9802 increase version & merge [`#SWS-9802`](https://jira.skywindgroup.com/browse/SWS-9802)
- SWS-10236 add totalEventId in payment operation [`#SWS-10236`](https://jira.skywindgroup.com/browse/SWS-10236)
- SWS-9802 add providerDetails field [`#SWS-9802`](https://jira.skywindgroup.com/browse/SWS-9802)
- SWS-10207 Add internal auth option [`#SWS-10207`](https://jira.skywindgroup.com/browse/SWS-10207)
- SWS-9773 merchant session id [`#SWS-9773`](https://jira.skywindgroup.com/browse/SWS-9773)
- SWS-10192 declare interface for jackpot win details [`#SWS-10192`](https://jira.skywindgroup.com/browse/SWS-10192)
- SWS-10176 Add totalBet, totalWin to payment request [`#SWS-10176`](https://jira.skywindgroup.com/browse/SWS-10176)
- SWS-9764 Add ts to payment, transfer request [`#SWS-9764`](https://jira.skywindgroup.com/browse/SWS-9764)
- SWS-9764 Payment type [`#SWS-9764`](https://jira.skywindgroup.com/browse/SWS-9764)
- SWS-9769 Pass information about jackpotContributions/win by jackpotId in win and aggregated information by jackpotId in last round win [`#SWS-9769`](https://jira.skywindgroup.com/browse/SWS-9769)
- SWS-9324 Increase version for 4.8 [`#SWS-9324`](https://jira.skywindgroup.com/browse/SWS-9324)
- SWS-9324 Increase version [`#SWS-9324`](https://jira.skywindgroup.com/browse/SWS-9324)
- SWS-9324 Increase version [`#SWS-9324`](https://jira.skywindgroup.com/browse/SWS-9324)
- SWS-9324 Increase version [`#SWS-9324`](https://jira.skywindgroup.com/browse/SWS-9324)
- SWS-9324 Update adapter url interface [`#SWS-9324`](https://jira.skywindgroup.com/browse/SWS-9324)
- SWS-9675 implement new interface [`#SWS-9675`](https://jira.skywindgroup.com/browse/SWS-9675)
- SWS-9675 remove useless import [`#SWS-9675`](https://jira.skywindgroup.com/browse/SWS-9675)
- SWS-9675 add import & add Interface [`#SWS-9675`](https://jira.skywindgroup.com/browse/SWS-9675)
- SWS-9675 add new type of btn - link [`#SWS-9675`](https://jira.skywindgroup.com/browse/SWS-9675)
- SWS-9300 add rollback fix [`#SWS-9300`](https://jira.skywindgroup.com/browse/SWS-9300)
- SWS-9300 add timeout [`#SWS-9300`](https://jira.skywindgroup.com/browse/SWS-9300)
- SWS-9324 Add actionable response [`#SWS-9324`](https://jira.skywindgroup.com/browse/SWS-9324)
- SWS-9325 add proxy support [`#SWS-9325`](https://jira.skywindgroup.com/browse/SWS-9325)
- SWS-9055 Export MerchantAdapterAPITransientError [`#SWS-9055`](https://jira.skywindgroup.com/browse/SWS-9055)
- SWS-9055 Improve base SWError class [`#SWS-9055`](https://jira.skywindgroup.com/browse/SWS-9055)
- SWS-9052 Add npmignore [`#SWS-9052`](https://jira.skywindgroup.com/browse/SWS-9052)
- SWS-9303 add connection error to core [`#SWS-9303`](https://jira.skywindgroup.com/browse/SWS-9303)
- SWS-9055 Add escapeSomeHtmlChars [`#SWS-9055`](https://jira.skywindgroup.com/browse/SWS-9055)
- SWS-9303 add HTTPOptions to export [`#SWS-9303`](https://jira.skywindgroup.com/browse/SWS-9303)
- SWS-9052 Increase version [`#SWS-9052`](https://jira.skywindgroup.com/browse/SWS-9052)
- SWS-9052 Add transaction info to payment request [`#SWS-9052`](https://jira.skywindgroup.com/browse/SWS-9052)
- SWS-9052 Add balances export [`#SWS-9052`](https://jira.skywindgroup.com/browse/SWS-9052)
- SWS-9052 Improve process response [`#SWS-9052`](https://jira.skywindgroup.com/browse/SWS-9052)
- SWS-9052 Add errors for http service, remove logger [`#SWS-9052`](https://jira.skywindgroup.com/browse/SWS-9052)
- SWS-9052 Commit payment, get balance [`#SWS-9052`](https://jira.skywindgroup.com/browse/SWS-9052)
- SWS-9088 http auth, fix payment service [`#SWS-9088`](https://jira.skywindgroup.com/browse/SWS-9088)
- SWS-9088 Add ip on start game [`#SWS-9088`](https://jira.skywindgroup.com/browse/SWS-9088)
- SWS-9088 Add keepalive [`#SWS-9088`](https://jira.skywindgroup.com/browse/SWS-9088)
- SWS-9088 Refactor [`#SWS-9088`](https://jira.skywindgroup.com/browse/SWS-9088)
- SWS-9088 Split merchant wallet service [`#SWS-9088`](https://jira.skywindgroup.com/browse/SWS-9088)
- Initial commit [`c7eeb3e`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/c7eeb3e90741a4d6f88545e1fc34e82a4d03e981)
- SWs-11073 fix improper use of superagent [`423cada`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/423cadabc54dafc7f194e4744f708f3101847320)
- Package.json [`c6476b3`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/c6476b37db62d84852a1dc80bc43a46a545421b1)
- export startgame interfaces & add typings [`ba87266`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/ba872663b3236f31635d1a4dd2da71cd75a5189d)
- Protocol refactoring [`b67f0ed`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/b67f0ede71fabb919061b6cef1c2dd72b8b551b3)
- Gulp version [`376c4a1`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/376c4a19a3678bb35c1891e4238a3e5bf843002d)
- add exports & fix payment type [`10ef644`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/10ef644368dd2657febe4384f6ee116eb5b327bb)
- fix issue with slash [`0dbc849`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/0dbc84947cfd737f992df1ad64010d750b7a0bd8)
- rewrite to async/await [`e66ff00`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/e66ff0053ba4ccdcf72bb5727d2b4d0ffdaa4544)
- Protocol refactoring [`d36c4b6`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/d36c4b6e3ec4bd68fcf6626e36edc9af90189405)
- add rollback to payment type [`cb5aa04`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/cb5aa046cad905e5d1e5f625a3083fcfd668583c)
- fix error instance [`c42d0dd`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/c42d0dd35d67c6bc96216060c61979aac5d3e0cd)
- export interfaces & added typings [`46f3e73`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/46f3e7350c395c8a5d147b7b5361a106d3505877)
- Fix order of catch and then [`9a54090`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/9a54090b8909268406d67d88eab4a1bb56101c7f)
- fix typings [`7ff216b`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/7ff216be4b6d5576664293183bbe4d4c5f21c911)
- core version 0.4.0 [`927eb82`](https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-wallet-adapter-core/commits/927eb828ecee6797be1ef91b6bd887ff39aa790d)
