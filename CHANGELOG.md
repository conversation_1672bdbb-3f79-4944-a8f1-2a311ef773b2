### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

#### [v2.4.1](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.4.0&sourceBranch=refs%2Ftags%2Fv2.4.1)

- NJSMGRN-476: fixed the promise-resolving ordering if error happens [`#NJSMGRN-476`](https://jira.skywindgroup.com/browse/NJSMGRN-476)
- bump version [`a2fce55`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/a2fce559d4abbb96e7390e1aaa8fcbcd54c271cc)

#### [v2.4.0](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.15&sourceBranch=refs%2Ftags%2Fv2.4.0)

> 5 June 2025

- NJSMGRN-475: added kafkajs for logging [`#NJSMGRN-475`](https://jira.skywindgroup.com/browse/NJSMGRN-475)
- fix conflict [`f37248d`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/f37248dea29164f648d6594c3e6f05bb6846e782)
- fix lint [`0075840`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/0075840ae4fe8d9477dc4ef2e0511dc59f9addca)

#### [v2.3.15](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.14&sourceBranch=refs%2Ftags%2Fv2.3.15)

> 4 June 2025

- SWS-50457 Do only reconnection instead and add trailing commas [`#SWS-50457`](https://jira.skywindgroup.com/browse/SWS-50457)
- SWS-50457 Add reconnection and retransmission in case of a "READONLY" error [`#SWS-50457`](https://jira.skywindgroup.com/browse/SWS-50457)

#### [v2.3.14](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.13&sourceBranch=refs%2Ftags%2Fv2.3.14)

> 3 June 2025

- SWS-50311 fix setUpOutput func export [`#SWS-50311`](https://jira.skywindgroup.com/browse/SWS-50311)

#### [v2.3.13](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.12&sourceBranch=refs%2Ftags%2Fv2.3.13)

> 27 May 2025

- SWS-50120; Context variables are not added when getting player info from the lobby via socket [`#SWS-50120`](https://jira.skywindgroup.com/browse/SWS-50120)

#### [v2.3.12](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.11&sourceBranch=refs%2Ftags%2Fv2.3.12)

> 27 May 2025

- SWS-50120; Context variables are not added when getting player info from the lobby via socket [`#SWS-50120`](https://jira.skywindgroup.com/browse/SWS-50120)

#### [v2.3.11](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.10&sourceBranch=refs%2Ftags%2Fv2.3.11)

> 27 May 2025

- SWS-49716: add some improvments [`#SWS-49716`](https://jira.skywindgroup.com/browse/SWS-49716)
- fix lint [`316f612`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/316f612bc4cc774bffd429204ec2ffc2525b959e)
- fix index.d.ts [`07d51a4`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/07d51a41dc10ed448f272a48a84236a62bc9fdfc)

#### [v2.3.10](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.9&sourceBranch=refs%2Ftags%2Fv2.3.10)

> 23 May 2025

- rollback createRedisClient method and improve promise handling metrics [`64ace2f`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/64ace2fbf62fc68435ce988151d78d5954a5e3c7)
- fix lint [`44e9991`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/44e999107ad80081802d288e285c25bad28423c6)

#### [v2.3.9](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.8&sourceBranch=refs%2Ftags%2Fv2.3.9)

> 14 May 2025

- SWS-49716: connection is closed [`#SWS-49716`](https://jira.skywindgroup.com/browse/SWS-49716)
- fix lint [`c34aa12`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c34aa12e9989f66eb538d532f759dc06bced1f8c)

#### [v2.3.8](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.7&sourceBranch=refs%2Ftags%2Fv2.3.8)

> 14 April 2025

- SWS-49280: fix fastify handler [`#SWS-49280`](https://jira.skywindgroup.com/browse/SWS-49280)

#### [v2.3.7](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.6&sourceBranch=refs%2Ftags%2Fv2.3.7)

> 14 April 2025

- SWS-49280: No history records in response [`#SWS-49280`](https://jira.skywindgroup.com/browse/SWS-49280)
- bump version [`8cb3ad6`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/8cb3ad6035c052ce83be913fc6e1f6effe92ef97)

#### [v2.3.6](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.5&sourceBranch=refs%2Ftags%2Fv2.3.6)

> 11 April 2025

- SWS-49148: Trace-id from gameserver response to client is different from other related events [`#SWS-49148`](https://jira.skywindgroup.com/browse/SWS-49148)

#### [v2.3.5](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.4&sourceBranch=refs%2Ftags%2Fv2.3.5)

> 31 March 2025

- NJSMGRN-459 Fix instrumentation for fastify v5 [`#NJSMGRN-459`](https://jira.skywindgroup.com/browse/NJSMGRN-459)

#### [v2.3.4](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.3&sourceBranch=refs%2Ftags%2Fv2.3.4)

> 11 March 2025

- NJSMGRN-432: support nodejs 14: replace structuredClone to lodash.cloneDeep [`#NJSMGRN-432`](https://jira.skywindgroup.com/browse/NJSMGRN-432)

#### [v2.3.3](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.2&sourceBranch=refs%2Ftags%2Fv2.3.3)

> 18 February 2025

- SWS-48227 Fix lint [`#SWS-48227`](https://jira.skywindgroup.com/browse/SWS-48227)
- SWS-48227 Add normalization of Prometheus paths for release/v2 [`#SWS-48227`](https://jira.skywindgroup.com/browse/SWS-48227)

#### [v2.3.2](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.1&sourceBranch=refs%2Ftags%2Fv2.3.2)

> 10 February 2025

- SWS-48016 Clean up [`#SWS-48016`](https://jira.skywindgroup.com/browse/SWS-48016)
- SWS-48016 Fix spelling [`#SWS-48016`](https://jira.skywindgroup.com/browse/SWS-48016)
- SWS-48016 Add ability to filter out request path by allowedPrefixes env var for relase v2 [`#SWS-48016`](https://jira.skywindgroup.com/browse/SWS-48016)

#### [v2.3.1](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.3.0&sourceBranch=refs%2Ftags%2Fv2.3.1)

> 31 December 2024

- Update npm to v11 [`c32083a`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c32083a00721a0ae755cfd565fa42d92c502e30d)

#### [v2.3.0](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.2.0&sourceBranch=refs%2Ftags%2Fv2.3.0)

> 5 December 2024

- SWS-44925 Refactor deepCloneWithoutCircular [`#SWS-44925`](https://jira.skywindgroup.com/browse/SWS-44925)
- SWS-44925 Fix functionality [`#SWS-44925`](https://jira.skywindgroup.com/browse/SWS-44925)
- SWS-44925 Remove lodash usage [`#SWS-44925`](https://jira.skywindgroup.com/browse/SWS-44925)

#### [v2.2.0](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.1.0&sourceBranch=refs%2Ftags%2Fv2.2.0)

> 31 October 2024

- NJSMGRN-397: added sentinel auth, fixed typping and changed es2022 to 2021 to support nodejs14 [`#NJSMGRN-397`](https://jira.skywindgroup.com/browse/NJSMGRN-397)
- fix typing [`fbb7acb`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/fbb7acb9b16616fe387d343046aefabf7febd989)

#### [v2.1.0](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.64&sourceBranch=refs%2Ftags%2Fv2.1.0)

> 25 October 2024

- NJSMGRN-351: increased types control and fixed generating token if expiresIn = 0 [`#NJSMGRN-351`](https://jira.skywindgroup.com/browse/NJSMGRN-351)
- fix typping [`e731ba1`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/e731ba112f4ce9ff28d16242183c3fb8df385682)
- fix lint [`4fff246`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/4fff2467c8cdb8947ce98520f6db9136eeb76143)

#### [v2.0.64](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.63&sourceBranch=refs%2Ftags%2Fv2.0.64)

> 18 October 2024

- NJSMGRN-351: 'nodejs' version checker has been removed [`#NJSMGRN-351`](https://jira.skywindgroup.com/browse/NJSMGRN-351)

#### [v2.0.63](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.62&sourceBranch=refs%2Ftags%2Fv2.0.63)

> 5 September 2024

- OMG-141: [EU STG] Omega logging issue [`#OMG-141`](https://jira.skywindgroup.com/browse/OMG-141)

#### [v2.0.62](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.61&sourceBranch=refs%2Ftags%2Fv2.0.62)

> 2 September 2024

- OMG-141: [EU STG] Omega logging issue [`#OMG-141`](https://jira.skywindgroup.com/browse/OMG-141)
- OMG-141: [EU STG] Omega logging issue [`#OMG-141`](https://jira.skywindgroup.com/browse/OMG-141)

#### [v2.0.61](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.60&sourceBranch=refs%2Ftags%2Fv2.0.61)

> 30 August 2024

- OMG-141: [EU STG] Omega logging issue [`#OMG-141`](https://jira.skywindgroup.com/browse/OMG-141)

#### [v2.0.60](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.59&sourceBranch=refs%2Ftags%2Fv2.0.60)

> 30 August 2024

- OMG-141: [EU STG] Omega logging issue [`#OMG-141`](https://jira.skywindgroup.com/browse/OMG-141)

#### [v2.0.59](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.57&sourceBranch=refs%2Ftags%2Fv2.0.59)

> 17 July 2024

- Update Dev libraries [`441db71`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/441db71154a6ba16df4e42dcc2fe9b4b17d8e1ec)
- Update npm to v10.8.2 [`5c99d1a`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/5c99d1a1ef6202ee29a417d253e25ddc38ba7573)

#### [v2.0.57](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.56&sourceBranch=refs%2Ftags%2Fv2.0.57)

> 9 July 2024

- Update Dev libraries [`6dadf8f`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/6dadf8f24f977c25bb2c6a26975133f28f0905c9)

#### [v2.0.56](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.55&sourceBranch=refs%2Ftags%2Fv2.0.56)

> 5 July 2024

- Update Dev libraries [`c9eeb23`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c9eeb23209df41a095d4a7e49b163e9fe3e793f6)

#### [v2.0.55](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.54&sourceBranch=refs%2Ftags%2Fv2.0.55)

> 18 June 2024

- Update Dev libraries [`8a68c76`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/8a68c76e00a6e7295b0bdb4d705797d32d2b4109)

#### [v2.0.54](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.53&sourceBranch=refs%2Ftags%2Fv2.0.54)

> 14 June 2024

- fix tests [`f894326`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/f894326e898c0f0d68344e58a00efc6481563259)
- fix conflicts [`d9ea7b7`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/d9ea7b78144e47e09a44622d87094f0cee05752d)
- Update Dev libraries [`c4641bf`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c4641bf3862f3fd3c1f693b99431c51ce41ba210)
- Update Dev libraries [`c81e3bb`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c81e3bbcf45949587f34217feacc41664552a84c)
- update jenkinsfile [`e3d8792`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/e3d8792f1b218db31d85c6d5624acd71e268c382)
- Update Node.js to v20.14 [`c23cd93`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c23cd936c1e426d50b4ce51a03bb070eea66c10a)

#### [v2.0.53](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.52&sourceBranch=refs%2Ftags%2Fv2.0.53)

> 14 June 2024

- Update npm to v10.8.1 [`105ef8d`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/105ef8d06db7871767df657588a51202a7f8e09e)

#### [v2.0.52](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.51&sourceBranch=refs%2Ftags%2Fv2.0.52)

> 11 June 2024

- SWS-44887: add destroy method to RedisPool interface [`#SWS-44887`](https://jira.skywindgroup.com/browse/SWS-44887)
- changed destroy method to shutdown [`91789a7`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/91789a76be0fcbadac25ec905a6dfa2718295ce5)
- fix tests [`a609267`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/a609267663d24c455c34041f7711bc53e4bf924e)
- add comment [`622bcdf`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/622bcdf02e6ed037a5d4b724da4ad3fe6c6ef96e)

#### [v2.0.51](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.50&sourceBranch=refs%2Ftags%2Fv2.0.51)

> 17 May 2024

- Update jenkinsfile and libs [`ea2954c`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/ea2954c52c9ae0fbd43f7c25440e48fd563b559e)
- version is up [`ec4df02`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/ec4df02b43233e49d8af0dd7cc0b0cff266873da)
- Update Node.js to v20.13 [`dc72538`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/dc72538d636d957888b906b5bafa60f859603ed6)

#### [v2.0.50](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.49&sourceBranch=refs%2Ftags%2Fv2.0.50)

> 17 May 2024

- NJSMGRN-225: rollback [`#NJSMGRN-225`](https://jira.skywindgroup.com/browse/NJSMGRN-225)

#### [v2.0.49](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.48&sourceBranch=refs%2Ftags%2Fv2.0.49)

> 22 April 2024

- NJSMGRN-225: TypeError: this.instance.use is not a function [`#NJSMGRN-225`](https://jira.skywindgroup.com/browse/NJSMGRN-225)

#### [v2.0.48](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.45&sourceBranch=refs%2Ftags%2Fv2.0.48)

> 12 April 2024

- SW360BE-15271: fix eslint [`#BE-15271`](https://jira.skywindgroup.com/browse/BE-15271)
- SW360BE-15271: fix new tracing variables [`#BE-15271`](https://jira.skywindgroup.com/browse/BE-15271)
- SW360BE-15271: linter [`#BE-15271`](https://jira.skywindgroup.com/browse/BE-15271)
- SW360BE-15271: add additonal fields in logger [`#BE-15271`](https://jira.skywindgroup.com/browse/BE-15271)
- again [`c7c6b7a`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c7c6b7afa26acdc4c58131143e6e01dabd15e64a)
- revert back package.json [`5ace4ae`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/5ace4ae7159860145af76e717c551fc80151e8d0)

#### [v2.0.45](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.44&sourceBranch=refs%2Ftags%2Fv2.0.45)

> 14 February 2024

- NJSMGRN-218: fixed metrics after changing SWBaseError implementation [`#NJSMGRN-218`](https://jira.skywindgroup.com/browse/NJSMGRN-218)
- fixed lint [`539f24c`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/539f24cb5a9d7465b1a0150e9f333226c6136087)

#### [v2.0.44](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.43&sourceBranch=refs%2Ftags%2Fv2.0.44)

> 14 February 2024

- NJSMGRN-218: align declaration with implementation for SWBaseError [`#NJSMGRN-218`](https://jira.skywindgroup.com/browse/NJSMGRN-218)

#### [v2.0.43](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.42&sourceBranch=refs%2Ftags%2Fv2.0.43)

> 7 February 2024

- NJSMGRN-195: fixed collecting metrics for working for two or more services simultaneously [`#NJSMGRN-195`](https://jira.skywindgroup.com/browse/NJSMGRN-195)

#### [v2.0.42](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.41&sourceBranch=refs%2Ftags%2Fv2.0.42)

> 31 January 2024

- Update Dev libraries [`1b82389`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/1b82389fe6cf4ddad2490f4d739c63d59f270395)
- update renovate config and fix compiling [`8e30a52`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/8e30a52b6aafb4510ed029b15231cc30cef1044f)

#### [v2.0.41](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.40&sourceBranch=refs%2Ftags%2Fv2.0.41)

> 24 January 2024

- fix conflicts [`bac0f2f`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/bac0f2fdcdfbd6e031d9b9f25920dce94bb83b47)
- Update Dev libraries, update Nodejs and upgrade renovate config [`9a28e4d`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/9a28e4d8b41a771f24f35e81d7969abeab4e36ef)
- Update Dev libraries [`7fda6f7`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/7fda6f708c4792f724c4246b12edc291b2ddfb34)
- Update Dev libraries [`92d1afd`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/92d1afddf4485b7395d16f8923c222bb70c08627)

#### [v2.0.40](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.39&sourceBranch=refs%2Ftags%2Fv2.0.40)

> 5 December 2023

- NJSMGRN-145: version is up [`#NJSMGRN-145`](https://jira.skywindgroup.com/browse/NJSMGRN-145)
- NJSMGRN-146: added import/newline-after-import, @typescript-eslint/object-curly-spacing and @typescript-eslint/key-spacing rules [`#NJSMGRN-146`](https://jira.skywindgroup.com/browse/NJSMGRN-146)
- NJSMGRN-145: updated renovate config [`#NJSMGRN-145`](https://jira.skywindgroup.com/browse/NJSMGRN-145)
- Revert "added @typescript-eslint/consistent-type-imports rule" [`bd4f54f`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/bd4f54fcad3564454cc290e1b19e5667590d733b)
- added @typescript-eslint/consistent-type-imports rule [`61d23e0`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/61d23e0fad2f298b60fc97ed13a66df5934c7d7e)
- Update Node.js to v20.10 [`da7cff2`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/da7cff229e8da99fb7b47a1e899689f006f1b5d1)

#### [v2.0.39](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.38&sourceBranch=refs%2Ftags%2Fv2.0.39)

> 28 November 2023

- Update Dev libraries [`3d89627`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/3d896273a7abe5463ba99b5793b0e8dc9fc71bbc)

#### [v2.0.38](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.37&sourceBranch=refs%2Ftags%2Fv2.0.38)

> 20 November 2023

- Update Dev libraries [`b272cf4`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/b272cf417b6653803346b4347955aed5e51a478b)

#### [v2.0.37](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.36&sourceBranch=refs%2Ftags%2Fv2.0.37)

> 15 November 2023

- Update SWBaseError with extra methods [`5109633`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/5109633c6911517d2dcc48c256ebcfa120b8e758)
- Update SWBaseError with extra methods [`2c09006`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/2c0900696b7c7181783dbb23e63bc80a5aa4f071)

#### [v2.0.36](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.35&sourceBranch=refs%2Ftags%2Fv2.0.36)

> 13 November 2023

- Update Dev libraries [`0082941`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/0082941f86f0971fd034e97cb17a853200326739)

#### [v2.0.35](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.34&sourceBranch=refs%2Ftags%2Fv2.0.35)

> 13 November 2023

- Update Dev libraries [`be00eaf`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/be00eafc4c13f1231768e5f760a0e1300ad8454a)

#### [v2.0.34](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.33&sourceBranch=refs%2Ftags%2Fv2.0.34)

> 9 November 2023

- Update dependency prom-client to v15 [`6440f33`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/6440f33deeb9962953ad1ff5047cbaf26a5211c2)

#### [v2.0.33](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.32&sourceBranch=refs%2Ftags%2Fv2.0.33)

> 8 November 2023

- SWS-43157: The decorator 'use' has already been added! when execute baseInstrument() twice or more [`#SWS-43157`](https://jira.skywindgroup.com/browse/SWS-43157)

#### [v2.0.32](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.31&sourceBranch=refs%2Ftags%2Fv2.0.32)

> 7 November 2023

- NJSMGRN-118: fix test -&gt; reduce timeout value, attems #2 [`#NJSMGRN-118`](https://jira.skywindgroup.com/browse/NJSMGRN-118)

#### [v2.0.31](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.30&sourceBranch=refs%2Ftags%2Fv2.0.31)

> 7 November 2023

- NJSMGRN-118: renovate config is changed [`#NJSMGRN-118`](https://jira.skywindgroup.com/browse/NJSMGRN-118)
- NJSMGRN-118: fix tests [`#NJSMGRN-118`](https://jira.skywindgroup.com/browse/NJSMGRN-118)
- fixed test [`97c632a`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/97c632a9647b373f60b41f4032f56fa027f07d67)
- file rename [`0bb8025`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/0bb80256421feeccd143f2b180da162c960c326b)

#### [v2.0.30](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.29&sourceBranch=refs%2Ftags%2Fv2.0.30)

> 7 November 2023

- NJSMGRN-109: renovate config is reconfigured [`#NJSMGRN-109`](https://jira.skywindgroup.com/browse/NJSMGRN-109)
- version is up [`8c92d77`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/8c92d776d6f77f0e3281a86fddcfb59f25ae6c32)

#### [v2.0.29](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.28&sourceBranch=refs%2Ftags%2Fv2.0.29)

> 7 November 2023

- SW360CRM-310: fix toLowerCase call for http module when using URL as first arg [`#CRM-310`](https://jira.skywindgroup.com/browse/CRM-310)
- Increase test timeout [`a9076e0`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/a9076e0fb7acb6d3b07729dfff95849609561913)

#### [v2.0.28](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.27&sourceBranch=refs%2Ftags%2Fv2.0.28)

> 31 October 2023

- SWS-43081: version is up [`#SWS-43081`](https://jira.skywindgroup.com/browse/SWS-43081)
- SWS-43081: 'Throughput/Latency' charts have 'undefined' instead of POST/GET methods [`#SWS-43081`](https://jira.skywindgroup.com/browse/SWS-43081)

#### [v2.0.27](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.26&sourceBranch=refs%2Ftags%2Fv2.0.27)

> 13 October 2023

- NJSMGRN-101: fixed outputting Prometheus metrics [`#NJSMGRN-101`](https://jira.skywindgroup.com/browse/NJSMGRN-101)

#### [v2.0.26](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.25&sourceBranch=refs%2Ftags%2Fv2.0.26)

> 11 October 2023

- NJSMGRN-100: fixed error when use @nestjs/platform-fastify and fastify together [`#NJSMGRN-100`](https://jira.skywindgroup.com/browse/NJSMGRN-100)
- rollback fastify version to 4+ [`e3ed89d`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/e3ed89d54ace796b56fea459d2ae48d75ff89dce)
- increase timeout for test [`2286203`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/2286203713c4880bc03671ea7d1c265356937931)

#### [v2.0.25](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.24&sourceBranch=refs%2Ftags%2Fv2.0.25)

> 11 October 2023

- NJSMGRN-98: added support for measuring fastify v2 and v3/4 metrics [`#NJSMGRN-98`](https://jira.skywindgroup.com/browse/NJSMGRN-98)
- fixed lint [`72fecec`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/72fecec700f397cfc980fd7e47bc9dff4c143523)
- version is up [`cac2740`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/cac2740d1fa14c94dbff3e129babd6f7a8e632e0)

#### [v2.0.24](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.23&sourceBranch=refs%2Ftags%2Fv2.0.24)

> 10 October 2023

- NJSMGRN-86: fixed sonarqube api [`#NJSMGRN-86`](https://jira.skywindgroup.com/browse/NJSMGRN-86)
- NJSMGRN-86: added support for sonarqube [`#NJSMGRN-86`](https://jira.skywindgroup.com/browse/NJSMGRN-86)
- added a checker for token [`56ecfe4`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/56ecfe40a260b8c483f648fa724a4de6b88208e4)
- fixed Cannot read properties of undefined (reading 'status') [`d127959`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/d127959968c8a9d39d316dfe9c047f40b4b8e3a5)
- added env param to docker-compose [`5790fba`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/5790fba66d529f6330d38ef045ff8d41b86146ad)
- enable sonar v10+ in pipeline [`f66b32e`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/f66b32e542a5fc10cdb36e0062458044acd0d7f6)
- removd console.log(process.env) [`070d2f9`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/070d2f926992cb1bb868e7b0d50de167692b927a)

#### [v2.0.23](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.22&sourceBranch=refs%2Ftags%2Fv2.0.23)

> 7 October 2023

- NJSMGRN-95: fied ioredis metric when 'call' method is used [`#NJSMGRN-95`](https://jira.skywindgroup.com/browse/NJSMGRN-95)
- Add renovate.json [`7088881`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/708888186a079b9970a93a393fdd2469462c87e0)
- update renovate config [`6349daf`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/6349dafa906b5af4766776fa2b8c35b8afd24135)
- fixed ioredis metric when 'send_command' (legacy) method is used [`026aba0`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/026aba006dbefd3b19a473626690be80131ad0a5)
- version is up [`c658cd0`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c658cd0da8de880ff84b102abf70a5ae77b7a674)
- version is up [`11b22a4`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/11b22a4d7299bafc9309d8f50befc49eb0ed7590)

#### [v2.0.22](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.21&sourceBranch=refs%2Ftags%2Fv2.0.22)

> 5 October 2023

- NJSMGRN-87: generate traceId using crypto.randomUUID() instead of uuid [`#NJSMGRN-87`](https://jira.skywindgroup.com/browse/NJSMGRN-87)

#### [v2.0.21](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.20&sourceBranch=refs%2Ftags%2Fv2.0.21)

> 4 October 2023

- NJSMGRN-39: add changelog [`#NJSMGRN-39`](https://jira.skywindgroup.com/browse/NJSMGRN-39)
- updated pipeline [`0415f69`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/0415f69aca433ae89dfbb7bf01f7996e205e58a9)
- version is up [`331a7ca`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/331a7caf21a894a168cb50ff4319beebe7d79f74)
- fixed pipeline [`04c5c6d`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/04c5c6d1e9e514d6c5dfa99c7c9c7951bc16132a)

#### [v2.0.20](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.19&sourceBranch=refs%2Ftags%2Fv2.0.20)

> 3 October 2023

- NJSMGRN-84: 'kafka-node' must be optional for 'logging' namespace [`#NJSMGRN-84`](https://jira.skywindgroup.com/browse/NJSMGRN-84)
- fixed eslint [`54d921b`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/54d921bf3eaffc9955eee97e32f73c2672eab54b)

#### [v2.0.19](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.18&sourceBranch=refs%2Ftags%2Fv2.0.19)

> 3 October 2023

- NJSMGRN-83: version is up [`#NJSMGRN-83`](https://jira.skywindgroup.com/browse/NJSMGRN-83)
- NJSMGRN-83: updated readme [`#NJSMGRN-83`](https://jira.skywindgroup.com/browse/NJSMGRN-83)

#### [v2.0.18](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.17&sourceBranch=refs%2Ftags%2Fv2.0.18)

> 3 October 2023

- NJSMGRN-81: version is up [`#NJSMGRN-81`](https://jira.skywindgroup.com/browse/NJSMGRN-81)
- NJSMGRN-81: fixed redis command [`#NJSMGRN-81`](https://jira.skywindgroup.com/browse/NJSMGRN-81)
- fixed tslint [`6f3f70c`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/6f3f70ceb9cd2136a15ba78bb690354857522d37)

#### [v2.0.17](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.16&sourceBranch=refs%2Ftags%2Fv2.0.17)

> 3 October 2023

- NJSMGRN-80: replaced sha-1 lib to native sha1 node:crypto [`#NJSMGRN-80`](https://jira.skywindgroup.com/browse/NJSMGRN-80)
- NJSMGRN-12: updated some libs [`#NJSMGRN-12`](https://jira.skywindgroup.com/browse/NJSMGRN-12)
- NJSMGRN-12: updated nodejs to 18.18 [`#NJSMGRN-12`](https://jira.skywindgroup.com/browse/NJSMGRN-12)
- added new benchmarks and 'usege' description [`8f9a71d`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/8f9a71de5775ec3e78fe75022802b1ad84211780)
- updated nodejs to 20 [`14ecc8f`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/14ecc8f25aae47b72449036b751a8b53a2e23332)
- updated readme [`edfae55`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/edfae554a75bae0c060832387783d4fcd6f22feb)
- updated jenkinsfile [`e46cec6`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/e46cec6d39221ae8c37c7200a8ff748688e86c83)

#### [v2.0.16](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.15&sourceBranch=refs%2Ftags%2Fv2.0.16)

> 3 October 2023

- NJSMGRN-76: version is up [`#NJSMGRN-76`](https://jira.skywindgroup.com/browse/NJSMGRN-76)
- NJSMGRN-76: added 'semi-style' rule [`#NJSMGRN-76`](https://jira.skywindgroup.com/browse/NJSMGRN-76)
- NJSMGRN-76: added 'semi' rule [`#NJSMGRN-76`](https://jira.skywindgroup.com/browse/NJSMGRN-76)
- NJSMGRN-76: added eslint:recommended rules [`#NJSMGRN-76`](https://jira.skywindgroup.com/browse/NJSMGRN-76)
- added 'comma-dangle' rule [`78fd9b2`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/78fd9b2aa6e93c6f03f11ffdba111223fb207f98)
- added 'plugin:promise/recommended' and fixed 'import/no-cycle' rule [`5abb875`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/5abb875d40afd02e7c73034a7b767bd19af2dab8)
- added 'plugin:sonarjs/recommended' rules [`32a3696`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/32a36966350eb2e002a8e5c28489f414807906ed)
- added 'unicorn/prefer-ternary' rule [`7782885`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/778288576c78d2aad8ce38c6e782ee115e8075c9)
- reduce Cognitive Complexity [`161d589`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/161d58960f87ab2ced563d91607aef07ae0e67d7)
- added 'unicorn/no-array-for-each' rule [`9630706`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/96307061445c39a0c103505ccc8f4f779a17248f)
- added 'unicorn/filename-case' rule [`8118a7d`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/8118a7dc99d946f5b0f658a2436045ad2dc60740)
- added 'no-multi-spaces' rule [`b7f3af4`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/b7f3af4054d91c6847fee3386b395afab6256cff)
- added 'import/no-cycle' rule [`bda56d7`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/bda56d7b52c9b5b106d74d3b2fc19ac861724451)
- added plugin:import/recommended rules [`0abb28f`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/0abb28f7185f15480043051aff71e77ade3c13a7)
- added 'unicorn/prefer-set-has' rule [`b2fb361`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/b2fb3614243d77872bdfa6f421e008ac170d3771)
- added 'unicorn/import-style' rule [`58e8525`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/58e8525f942c0819bd72d656d5b86c17ea160693)
- added 'prefer-promise-reject-errors' rule [`009f265`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/009f2651fe08838895bb4d49cf7008354515a6f8)
- added 'unicorn/throw-new-error' rule [`b7bc2ea`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/b7bc2ea54b6d4f66d4d7ea69fee01ba7cf4f529c)
- added 'node/no-sync' rule [`5555da8`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/5555da810fee75e51fbaebbd6a3a40c72523302a)
- added 'quotes' rule [`c8ac32b`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c8ac32b934b5e0fe110d429b924cff969fcc1d3a)
- added 'semi-spacing' rule [`b96f988`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/b96f9881fb8dddef01add54b62e3bbf5a52a083e)
- changed 'option' to 'options' http method [`e8eab1b`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/e8eab1be065ba18228bc7e7894703f13ab87ae8a)
- added 'unicorn/no-lonely-if' rule [`c021615`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c0216155afee90247d4820342b190c903ac131b6)
- added 'node/no-callback-literal' rule [`a090d9b`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/a090d9b1b439184a96738144748008c1219d66f1)
- added 'unicorn/prefer-array-find' rule [`1fae94e`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/1fae94e911f3eeaeeb2303364706c8f86619fd18)
- added 'block-spacing' rule [`73d0b99`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/73d0b990a97acea4d2f97d0f1e7486a089ff7b63)
- added '@typescript-eslint/prefer-includes' rule [`35da237`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/35da237ef1315c82a992864a66f8d1b03d055fa8)
- added '@typescript-eslint/space-before-blocks' rule [`1beb0fd`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/1beb0fd443b50f3e0ab94828bfb903f1b988684d)
- added 'space-before-blocks' rule [`0393206`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/0393206e816080db28ccafb8fda93dd9f4ca6eaa)
- added 'no-duplicate-imports' rule [`91c5ac5`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/91c5ac52f9f59aba1ea6fcbaf597e776b68acef2)
- added 'arrow-spacing' rule [`ebd991f`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/ebd991f35424f5932b810767fcc879254b2b31eb)
- added 'no-extra-semi' rule [`5dfed8d`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/5dfed8d1191833d61f316cf86b3c75cd2d3151d6)

#### [v2.0.15](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.14&sourceBranch=refs%2Ftags%2Fv2.0.15)

> 3 October 2023

- NJSMGRN-20: fixed test [`#NJSMGRN-20`](https://jira.skywindgroup.com/browse/NJSMGRN-20)
- NJSMGRN-20: removed sequelize lib [`#NJSMGRN-20`](https://jira.skywindgroup.com/browse/NJSMGRN-20)
- fixed sequelize interface [`f6b82c6`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/f6b82c6229c72bf955bf40b9cd7a4bf6d65d5cdc)

#### [v2.0.14](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.13&sourceBranch=refs%2Ftags%2Fv2.0.14)

> 3 October 2023

- NJSMGRN-74: version is up [`#NJSMGRN-74`](https://jira.skywindgroup.com/browse/NJSMGRN-74)
- NJSMGRN-75: 'js-big-integer' should be moved to peerDependencies [`#NJSMGRN-75`](https://jira.skywindgroup.com/browse/NJSMGRN-75)
- NJSMGRN-74: update measured libs to the latest versions [`#NJSMGRN-74`](https://jira.skywindgroup.com/browse/NJSMGRN-74)
- fixed timers interface [`986ea96`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/986ea962e6492fbbcffdcc258ab70128b9ec577f)

#### [v2.0.13](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.12&sourceBranch=refs%2Ftags%2Fv2.0.13)

> 3 October 2023

- NJSMGRN-73: removed semver [`#NJSMGRN-73`](https://jira.skywindgroup.com/browse/NJSMGRN-73)
- NJSMGRN-71: version is up [`#NJSMGRN-71`](https://jira.skywindgroup.com/browse/NJSMGRN-71)
- NJSMGRN-71: removed newRelic [`#NJSMGRN-71`](https://jira.skywindgroup.com/browse/NJSMGRN-71)
- NJSMGRN-71: 'measured' has been replaced to 'measured-core' and moved to peerDependencies [`#NJSMGRN-71`](https://jira.skywindgroup.com/browse/NJSMGRN-71)
- removed @types/generic-pool, updated @types/lodash and @types/sequelize [`cfcc013`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/cfcc0139cbcae01e57d5dac7e1c5d197d5494fd8)

#### [v2.0.12](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.11&sourceBranch=refs%2Ftags%2Fv2.0.12)

> 3 October 2023

- NJSMGRN-70: version is up [`#NJSMGRN-70`](https://jira.skywindgroup.com/browse/NJSMGRN-70)
- NJSMGRN-70: sequelize should be moved to peerDependencies [`#NJSMGRN-70`](https://jira.skywindgroup.com/browse/NJSMGRN-70)

#### [v2.0.11](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.10&sourceBranch=refs%2Ftags%2Fv2.0.11)

> 3 October 2023

- NJSMGRN-17: version is up [`#NJSMGRN-17`](https://jira.skywindgroup.com/browse/NJSMGRN-17)
- NJSMGRN-17: bump ioredis to ^5.3.2, bump generic-pool to ^3.9.0 and moved to peerDependencies [`#NJSMGRN-17`](https://jira.skywindgroup.com/browse/NJSMGRN-17)
- updated readme file [`b8c0bae`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/b8c0baea9a3342dcf9016027a0ac980db42343fb)
- fixed redisClient type [`0e4dc99`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/0e4dc99c26c188c5f6fa1229ae342a6875c8d7a4)

#### [v2.0.10](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.9&sourceBranch=refs%2Ftags%2Fv2.0.10)

> 3 October 2023

- NJSMGRN-68: version is up [`#NJSMGRN-68`](https://jira.skywindgroup.com/browse/NJSMGRN-68)
- NJSMGRN-68: 'sha1' has been replaced to 'sha-1' and  moved to peerDependencies [`#NJSMGRN-68`](https://jira.skywindgroup.com/browse/NJSMGRN-68)

#### [v2.0.9](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.8&sourceBranch=refs%2Ftags%2Fv2.0.9)

> 3 October 2023

- NJSMGRN-69: version is up [`#NJSMGRN-69`](https://jira.skywindgroup.com/browse/NJSMGRN-69)
- SWS-42575: re-configured package.json for @skywind-group/gelf-stream [`#SWS-42575`](https://jira.skywindgroup.com/browse/SWS-42575)

#### [v2.0.8](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.7&sourceBranch=refs%2Ftags%2Fv2.0.8)

> 3 October 2023

- NJSMGRN-61: version is up [`#NJSMGRN-61`](https://jira.skywindgroup.com/browse/NJSMGRN-61)
- NJSMGRN-67: bump crc to ^4.3.2 [`#NJSMGRN-67`](https://jira.skywindgroup.com/browse/NJSMGRN-67)
- NJSMGRN-18: re-configurated package.json for kafka-node [`#NJSMGRN-18`](https://jira.skywindgroup.com/browse/NJSMGRN-18)
- NJSMGRN-66: bump bole to ^5.0.7 [`#NJSMGRN-66`](https://jira.skywindgroup.com/browse/NJSMGRN-66)
- NJSMGRN-65: bump benchmark to ^2.1.4 [`#NJSMGRN-65`](https://jira.skywindgroup.com/browse/NJSMGRN-65)
- NJSMGRN-64: bump @types/uuid to ^9.0.4 [`#NJSMGRN-64`](https://jira.skywindgroup.com/browse/NJSMGRN-64)
- NJSMGRN-63: removed compression [`#NJSMGRN-63`](https://jira.skywindgroup.com/browse/NJSMGRN-63)
- NJSMGRN-61: bump hashids to ^2.3.0 and reconfigured package.json [`#NJSMGRN-61`](https://jira.skywindgroup.com/browse/NJSMGRN-61)
- bump @types/node to 14.18.63 [`95f4026`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/95f402665c116447bb9f9b2195ce48725c774ec7)
- rollback redis in package.json [`8d67508`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/8d675087a6059b9fe021a4c90fc8fd526c14b470)

#### [v2.0.7](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.6&sourceBranch=refs%2Ftags%2Fv2.0.7)

> 3 October 2023

- NJSMGRN-60: version is up [`#NJSMGRN-60`](https://jira.skywindgroup.com/browse/NJSMGRN-60)
- NJSMGRN-60: bump agentkeepalive to ^4.5.0 and reconfigured package.json [`#NJSMGRN-60`](https://jira.skywindgroup.com/browse/NJSMGRN-60)

#### [v2.0.6](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.5&sourceBranch=refs%2Ftags%2Fv2.0.6)

> 3 October 2023

- NJSMGRN-56: version is up [`#NJSMGRN-56`](https://jira.skywindgroup.com/browse/NJSMGRN-56)
- NJSMGRN-59: bump reflect-metadata to ^0.1.13 [`#NJSMGRN-59`](https://jira.skywindgroup.com/browse/NJSMGRN-59)
- NJSMGRN-58: bump express to ^4.18.2 [`#NJSMGRN-58`](https://jira.skywindgroup.com/browse/NJSMGRN-58)
- NJSMGRN-57: bump superagent to ^8.1.2 [`#NJSMGRN-57`](https://jira.skywindgroup.com/browse/NJSMGRN-57)
- NJSMGRN-56: bump jsonwebtoken to ^9.0.2 and reconfigured package.json [`#NJSMGRN-56`](https://jira.skywindgroup.com/browse/NJSMGRN-56)

#### [v2.0.5](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.4&sourceBranch=refs%2Ftags%2Fv2.0.5)

> 3 October 2023

- NJSMGRN-52: version is up [`#NJSMGRN-52`](https://jira.skywindgroup.com/browse/NJSMGRN-52)
- NJSMGRN-55: replaced mocha-typescript to @testdeck/mocha [`#NJSMGRN-55`](https://jira.skywindgroup.com/browse/NJSMGRN-55)
- NJSMGRN-54: removed esprima [`#NJSMGRN-54`](https://jira.skywindgroup.com/browse/NJSMGRN-54)
- NJSMGRN-53: bump sinon to ^15.2.0 [`#NJSMGRN-53`](https://jira.skywindgroup.com/browse/NJSMGRN-53)
- NJSMGRN-52: bump ts-node to 10.9.1 [`#NJSMGRN-52`](https://jira.skywindgroup.com/browse/NJSMGRN-52)
- fixed tests [`30c5d1b`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/30c5d1bcba283169f38b1443287123e5a8aeb552)
- removed typescript-lit-html-plugin [`01b463c`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/01b463cc26ff99181b1851c870f5258c31c10653)
- removed fastify [`f2214ae`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/f2214aed9a132f5e81d907253d8ae44ca53a086b)

#### [v2.0.4](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.3&sourceBranch=refs%2Ftags%2Fv2.0.4)

> 3 October 2023

- NJSMGRN-14: version is up [`#NJSMGRN-14`](https://jira.skywindgroup.com/browse/NJSMGRN-14)
- NJSMGRN-14: gulp should be removed [`#NJSMGRN-14`](https://jira.skywindgroup.com/browse/NJSMGRN-14)

#### [v2.0.3](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.2&sourceBranch=refs%2Ftags%2Fv2.0.3)

> 3 October 2023

- NJSMGRN-16: version is up [`#NJSMGRN-16`](https://jira.skywindgroup.com/browse/NJSMGRN-16)
- NJSMGRN-16: removed tslint [`#NJSMGRN-16`](https://jira.skywindgroup.com/browse/NJSMGRN-16)
- NJSMGRN-16: tslint should be replaced to eslint [`#NJSMGRN-16`](https://jira.skywindgroup.com/browse/NJSMGRN-16)

#### [v2.0.2](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.1&sourceBranch=refs%2Ftags%2Fv2.0.2)

> 3 October 2023

- NJSMGRN-49: version is up [`#NJSMGRN-49`](https://jira.skywindgroup.com/browse/NJSMGRN-49)
- NJSMGRN-49: ecmascript changed from es2017 to es2021, updated nodejs to 14.21 and compile changed from gulp to tsc [`#NJSMGRN-49`](https://jira.skywindgroup.com/browse/NJSMGRN-49)
- NJSMGRN-49: bump ts to 5.1.6, bump chai to 4.3.7 and bump chai-as-promised to 7.1.1 [`#NJSMGRN-49`](https://jira.skywindgroup.com/browse/NJSMGRN-49)

#### [v2.0.1](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/compare/diff?targetBranch=refs%2Ftags%2Fv2.0.0&sourceBranch=refs%2Ftags%2Fv2.0.1)

> 3 October 2023

- NJSMGRN-50, NJSMGRN-51: updated mocha to 10+v, replaced istanbul to nyc [`#NJSMGRN-50`](https://jira.skywindgroup.com/browse/NJSMGRN-50) [`#NJSMGRN-51`](https://jira.skywindgroup.com/browse/NJSMGRN-51)
- NJSMGRN-13: 'safe/safeGet' method has been removed [`#NJSMGRN-13`](https://jira.skywindgroup.com/browse/NJSMGRN-13)

#### v2.0.0

> 3 October 2023

- NJSMGRN-4: added readme file [`#NJSMGRN-4`](https://jira.skywindgroup.com/browse/NJSMGRN-4)
- SW360BE-9609: fix package version [`#BE-9609`](https://jira.skywindgroup.com/browse/BE-9609)
- SW360BE-9609: metrics improvement [`#BE-9609`](https://jira.skywindgroup.com/browse/BE-9609)
- SWS-37629: Downgrade typescript to fix build failure [`#SWS-37629`](https://jira.skywindgroup.com/browse/SWS-37629)
- SWS-37629: Bump version [`#SWS-37629`](https://jira.skywindgroup.com/browse/SWS-37629)
- SWS-37629: Promisify setTimeout [`#SWS-37629`](https://jira.skywindgroup.com/browse/SWS-37629)
- SW360BE-4537: add more config for cluster [`#BE-4537`](https://jira.skywindgroup.com/browse/BE-4537)
- SW360BE-4537: add monitoring to redis cluster [`#BE-4537`](https://jira.skywindgroup.com/browse/BE-4537)
- SW360BE-4204: if the stack is undefined, console/graylog streams got [`#BE-4204`](https://jira.skywindgroup.com/browse/BE-4204)
- SWS-27970: skip double run transaction for nestjs integration [`#SWS-27970`](https://jira.skywindgroup.com/browse/SWS-27970)
- SWS-27970: bump version [`#SWS-27970`](https://jira.skywindgroup.com/browse/SWS-27970)
- SWS-27970: fix UNKNOWN in the path for the case when promBundle has [`#SWS-27970`](https://jira.skywindgroup.com/browse/SWS-27970)
- SW360BE-3871: add interface for redis node conig [`#BE-3871`](https://jira.skywindgroup.com/browse/BE-3871)
- SW360BE-3871: support redis cluster [`#BE-3871`](https://jira.skywindgroup.com/browse/BE-3871)
- SWS-30872 - rename parameter [`#SWS-30872`](https://jira.skywindgroup.com/browse/SWS-30872)
- SWS-30872 - reduce logs [`#SWS-30872`](https://jira.skywindgroup.com/browse/SWS-30872)
- feature/SWS-33405-phantom-protocol-update [`#SWS-33405`](https://jira.skywindgroup.com/browse/SWS-33405)
- feature/SWS-33405-phantom-protocol-update [`#SWS-33405`](https://jira.skywindgroup.com/browse/SWS-33405)
- feature/SWS-33405-phantom-protocol-update [`#SWS-33405`](https://jira.skywindgroup.com/browse/SWS-33405)
- SWS-32790: update version [`#SWS-32790`](https://jira.skywindgroup.com/browse/SWS-32790)
- SWS-32790: Add ability to disable transferable headers [`#SWS-32790`](https://jira.skywindgroup.com/browse/SWS-32790)
- SWS-27970 Update version because of broken build [`#SWS-27970`](https://jira.skywindgroup.com/browse/SWS-27970)
- SWS-27970 Bump version [`#SWS-27970`](https://jira.skywindgroup.com/browse/SWS-27970)
- SWS-27970 Fix metrics paths being set as "UNKNOWN" when the uri can't be resolved [`#SWS-27970`](https://jira.skywindgroup.com/browse/SWS-27970)
- SW360BE-2011: fix measures for http and sql queryies. [`#BE-2011`](https://jira.skywindgroup.com/browse/BE-2011)
- SWS-18662 Update tslint reference [`#SWS-18662`](https://jira.skywindgroup.com/browse/SWS-18662)
- SWS-25856 - fix logging [`#SWS-25856`](https://jira.skywindgroup.com/browse/SWS-25856)
- SWS-25856 - GS/GS-live does not write error logs to the correct index [`#SWS-25856`](https://jira.skywindgroup.com/browse/SWS-25856)
- SWS-25856 - GS/GS-live does not write error logs to the correct index in kibana [`#SWS-25856`](https://jira.skywindgroup.com/browse/SWS-25856)
- SWS-24980 [ES] Message on UI does not correspond to the one sent by POP if user is trying to open game while being self-excluded [`#SWS-24980`](https://jira.skywindgroup.com/browse/SWS-24980)
- SWS-24980 [ES] Message on UI does not correspond to the one sent by POP if user is trying to open game while being self-excluded [`#SWS-24980`](https://jira.skywindgroup.com/browse/SWS-24980)
- SWS-22620 fix tests [`#SWS-22620`](https://jira.skywindgroup.com/browse/SWS-22620)
- SWS-22620 missing trace-id for https protocol [`#SWS-22620`](https://jira.skywindgroup.com/browse/SWS-22620)
- SWS-18552: return x-sw-trace-id for the nestjs case [`#SWS-18552`](https://jira.skywindgroup.com/browse/SWS-18552)
- SWS-18552: fix traceId for nestjs [`#SWS-18552`](https://jira.skywindgroup.com/browse/SWS-18552)
- SWS-21314: revert file [`#SWS-21314`](https://jira.skywindgroup.com/browse/SWS-21314)
- SWS-21314 update lib [`#SWS-21314`](https://jira.skywindgroup.com/browse/SWS-21314)
- SWS-21314: fix dependency [`#SWS-21314`](https://jira.skywindgroup.com/browse/SWS-21314)
- SWS-21269 add lib [`#SWS-21269`](https://jira.skywindgroup.com/browse/SWS-21269)
- SWS-17730: lazy export, transferable context variables [`#SWS-17730`](https://jira.skywindgroup.com/browse/SWS-17730)
- SWS-20812: start support from 12.xxx [`#SWS-20812`](https://jira.skywindgroup.com/browse/SWS-20812)
- SWS-20395 update express-prom-bundle [`#SWS-20395`](https://jira.skywindgroup.com/browse/SWS-20395)
- SWS-20395 pass argument to trackTransacton result [`#SWS-20395`](https://jira.skywindgroup.com/browse/SWS-20395)
- SWS-20326 change default settings [`#SWS-20326`](https://jira.skywindgroup.com/browse/SWS-20326)
- SWS-20326 remove redundant line [`#SWS-20326`](https://jira.skywindgroup.com/browse/SWS-20326)
- SWS-20326  close and destroy stream after recreate [`#SWS-20326`](https://jira.skywindgroup.com/browse/SWS-20326)
- SWS-19945 fix docker-compose.yaml [`#SWS-19945`](https://jira.skywindgroup.com/browse/SWS-19945)
- SWS-19945 turn off sonar [`#SWS-19945`](https://jira.skywindgroup.com/browse/SWS-19945)
- SWS-19945 add debug info to sonar error [`#SWS-19945`](https://jira.skywindgroup.com/browse/SWS-19945)
- SWS-19945 adjust dependecies [`#SWS-19945`](https://jira.skywindgroup.com/browse/SWS-19945)
- SWS-19945 fix build [`#SWS-19945`](https://jira.skywindgroup.com/browse/SWS-19945)
- SWS-19945 fix emitter [`#SWS-19945`](https://jira.skywindgroup.com/browse/SWS-19945)
- SWS-19945 update version [`#SWS-19945`](https://jira.skywindgroup.com/browse/SWS-19945)
- SWS-19945 remove redundant clean script [`#SWS-19945`](https://jira.skywindgroup.com/browse/SWS-19945)
- SWS-19945 fix typings [`#SWS-19945`](https://jira.skywindgroup.com/browse/SWS-19945)
- SWS-19945 working on async context propogation [`#SWS-19945`](https://jira.skywindgroup.com/browse/SWS-19945)
- SWS-19945 remove redundant files [`#SWS-19945`](https://jira.skywindgroup.com/browse/SWS-19945)
- SWS-19681 fix possible unhandleRejection [`#SWS-19681`](https://jira.skywindgroup.com/browse/SWS-19681)
- SWS-18861 fix test [`#SWS-18861`](https://jira.skywindgroup.com/browse/SWS-18861)
- SWS-18861 remove redundant [`#SWS-18861`](https://jira.skywindgroup.com/browse/SWS-18861)
- SWS-18861 setup package version [`#SWS-18861`](https://jira.skywindgroup.com/browse/SWS-18861)
- SWS-18861 unit tests [`#SWS-18861`](https://jira.skywindgroup.com/browse/SWS-18861)
- SWS-18861 split into testable pieces [`#SWS-18861`](https://jira.skywindgroup.com/browse/SWS-18861)
- SWS-18861  develop a stream to pipe logs [`#SWS-18861`](https://jira.skywindgroup.com/browse/SWS-18861)
- SWS-19681 - ASIA | Stage: Broken DB transactions [`#SWS-19681`](https://jira.skywindgroup.com/browse/SWS-19681)
- SWS-18340 fix bug [`#SWS-18340`](https://jira.skywindgroup.com/browse/SWS-18340)
- SWS-18341 fix export [`#SWS-18341`](https://jira.skywindgroup.com/browse/SWS-18341)
- SWS-18341 generalize hilo generator [`#SWS-18341`](https://jira.skywindgroup.com/browse/SWS-18341)
- SWS-18340 instrument nestjs fastrify adapter to support traceId, [`#SWS-18340`](https://jira.skywindgroup.com/browse/SWS-18340)
- SWS-18340 enhance interfaces [`#SWS-18340`](https://jira.skywindgroup.com/browse/SWS-18340)
- SWS-17108 fix compilation [`#SWS-17108`](https://jira.skywindgroup.com/browse/SWS-17108)
- SWS-17107 add setRootLogger to the logging package [`#SWS-17107`](https://jira.skywindgroup.com/browse/SWS-17107)
- SWS-17107 update config of keepalive [`#SWS-17107`](https://jira.skywindgroup.com/browse/SWS-17107)
- SWS-17998 ws connection metrics [`#SWS-17998`](https://jira.skywindgroup.com/browse/SWS-17998)
- SWS-17107 fix dependencies [`#SWS-17107`](https://jira.skywindgroup.com/browse/SWS-17107)
- SWS-17107 generalize token utils [`#SWS-17107`](https://jira.skywindgroup.com/browse/SWS-17107)
- SWS-17107 add export to index [`#SWS-17107`](https://jira.skywindgroup.com/browse/SWS-17107)
- SWS-17107 fix build [`#SWS-17107`](https://jira.skywindgroup.com/browse/SWS-17107)
- SWS-17107 utils for keepaliva [`#SWS-17107`](https://jira.skywindgroup.com/browse/SWS-17107)
- SWS-17107 create keepalive namespace [`#SWS-17107`](https://jira.skywindgroup.com/browse/SWS-17107)
- SWS-17601 add tests [`#SWS-17601`](https://jira.skywindgroup.com/browse/SWS-17601)
- SWS-17601 add typings [`#SWS-17601`](https://jira.skywindgroup.com/browse/SWS-17601)
- SWS-17601 separate errAsObject method [`#SWS-17601`](https://jira.skywindgroup.com/browse/SWS-17601)
- SWS-17578 Add prometheus labels for gauge value [`#SWS-17578`](https://jira.skywindgroup.com/browse/SWS-17578)
- SWS-17015 Send message to Kafka with key [`#SWS-17015`](https://jira.skywindgroup.com/browse/SWS-17015)
- SWS-15979 fix mistype [`#SWS-15979`](https://jira.skywindgroup.com/browse/SWS-15979)
- SWS-15979 generalize redis pool [`#SWS-15979`](https://jira.skywindgroup.com/browse/SWS-15979)
- SWS-15978 to make things more simple [`#SWS-15978`](https://jira.skywindgroup.com/browse/SWS-15978)
- SWS-15978 extract publicId [`#SWS-15978`](https://jira.skywindgroup.com/browse/SWS-15978)
- SWS-15977 amend implementation [`#SWS-15977`](https://jira.skywindgroup.com/browse/SWS-15977)
- SWS-15977 code style [`#SWS-15977`](https://jira.skywindgroup.com/browse/SWS-15977)
- SWS-15977 extract base error from mapi [`#SWS-15977`](https://jira.skywindgroup.com/browse/SWS-15977)
- SWS-14763 amend building [`#SWS-14763`](https://jira.skywindgroup.com/browse/SWS-14763)
- SWS-14763 add ability to see waitings count [`#SWS-14763`](https://jira.skywindgroup.com/browse/SWS-14763)
- SWS-15975 generalize latch [`#SWS-15975`](https://jira.skywindgroup.com/browse/SWS-15975)
- SWS-13186 fix let to const [`#SWS-13186`](https://jira.skywindgroup.com/browse/SWS-13186)
- SWS-13186 fix undefined in setHeader [`#SWS-13186`](https://jira.skywindgroup.com/browse/SWS-13186)
- SWS-9431 add typings [`#SWS-9431`](https://jira.skywindgroup.com/browse/SWS-9431)
- SWS-9431 add calculation module [`#SWS-9431`](https://jira.skywindgroup.com/browse/SWS-9431)
- SWS-11334 fix import [`#SWS-11334`](https://jira.skywindgroup.com/browse/SWS-11334)
- SWS-11334 fix mock delete method [`#SWS-11334`](https://jira.skywindgroup.com/browse/SWS-11334)
- SWS-11990 refactor generation of traceId [`#SWS-11990`](https://jira.skywindgroup.com/browse/SWS-11990)
- SWS-11334 add agent to d.ts [`#SWS-11334`](https://jira.skywindgroup.com/browse/SWS-11334)
- SWS-11334 add tests [`#SWS-11334`](https://jira.skywindgroup.com/browse/SWS-11334)
- SWS-11334 add superagent mocker [`#SWS-11334`](https://jira.skywindgroup.com/browse/SWS-11334)
- AQA-9 change loop exit approach [`#AQA-9`](https://jira.skywindgroup.com/browse/AQA-9)
- AQA-9 last await timeout adjust to maxTimeout [`#AQA-9`](https://jira.skywindgroup.com/browse/AQA-9)
- SWS-11707 protect async context from leaking [`#SWS-11707`](https://jira.skywindgroup.com/browse/SWS-11707)
- SWS-11115 remove redundant log [`#SWS-11115`](https://jira.skywindgroup.com/browse/SWS-11115)
- SWS-11690 prevent redundant query [`#SWS-11690`](https://jira.skywindgroup.com/browse/SWS-11690)
- SWS-11204 Fix mapping of inherited db columns [`#SWS-11204`](https://jira.skywindgroup.com/browse/SWS-11204)
- SWS-10807 expose setTraceId [`#SWS-10807`](https://jira.skywindgroup.com/browse/SWS-10807)
- SWS-10948 add trace header [`#SWS-10948`](https://jira.skywindgroup.com/browse/SWS-10948)
- SWS-9796 Support sequelize 3.x [`#SWS-9796`](https://jira.skywindgroup.com/browse/SWS-9796)
- SWS-9018 declare Lazy interface to be able to pass variable [`#SWS-9018`](https://jira.skywindgroup.com/browse/SWS-9018)
- [DEVOPS-4881] Fix for Java installation issue [`#DEVOPS-4881`](https://jira.skywindgroup.com/browse/DEVOPS-4881)
- SWS-9318 fix build [`#SWS-9318`](https://jira.skywindgroup.com/browse/SWS-9318)
- [DEVOPS-4881] Fix for Java installation issue [`#DEVOPS-4881`](https://jira.skywindgroup.com/browse/DEVOPS-4881)
- SWS-9318 fix test [`#SWS-9318`](https://jira.skywindgroup.com/browse/SWS-9318)
- SW-9318 fix exports [`#SW-9318`](https://jira.skywindgroup.com/browse/SW-9318)
- SWS-9318 update version [`#SWS-9318`](https://jira.skywindgroup.com/browse/SWS-9318)
- SWS-9318 unit tests [`#SWS-9318`](https://jira.skywindgroup.com/browse/SWS-9318)
- SWS-9318 generalize redis proc [`#SWS-9318`](https://jira.skywindgroup.com/browse/SWS-9318)
- SWS-9303 udpate typings [`#SWS-9303`](https://jira.skywindgroup.com/browse/SWS-9303)
- SWS-9303 increase version - 0.8.4 [`#SWS-9303`](https://jira.skywindgroup.com/browse/SWS-9303)
- SWS-9303 improve retry mechanism [`#SWS-9303`](https://jira.skywindgroup.com/browse/SWS-9303)
- SWS-8446 Reconnect kafka producer in case of socket error [`#SWS-8446`](https://jira.skywindgroup.com/browse/SWS-8446)
- SWS-9139 fix loadash dev dep [`#SWS-9139`](https://jira.skywindgroup.com/browse/SWS-9139)
- SWS-9139 check instrumentation more carefully [`#SWS-9139`](https://jira.skywindgroup.com/browse/SWS-9139)
- SWS-8856 Use error constructor name [`#SWS-8856`](https://jira.skywindgroup.com/browse/SWS-8856)
- SWS-8480 measure http errors [`#SWS-8480`](https://jira.skywindgroup.com/browse/SWS-8480)
- SWS-8329 fix for native promise [`#SWS-8329`](https://jira.skywindgroup.com/browse/SWS-8329)
- SWS-8329 declare sequelize operators aliases [`#SWS-8329`](https://jira.skywindgroup.com/browse/SWS-8329)
- SWS-8216 return back 'finish' event listener [`#SWS-8216`](https://jira.skywindgroup.com/browse/SWS-8216)
- SWS-8215 fix gauge of http latency [`#SWS-8215`](https://jira.skywindgroup.com/browse/SWS-8215)
- SWS-8802 fix bug [`#SWS-8802`](https://jira.skywindgroup.com/browse/SWS-8802)
- SWS-8082 bypass context through request [`#SWS-8082`](https://jira.skywindgroup.com/browse/SWS-8082)
- SWS-7762 fix method signature [`#SWS-7762`](https://jira.skywindgroup.com/browse/SWS-7762)
- SWS-7584 Remove sonar failing build [`#SWS-7584`](https://jira.skywindgroup.com/browse/SWS-7584)
- SWS-7762 correct method name [`#SWS-7762`](https://jira.skywindgroup.com/browse/SWS-7762)
- SWS-7776 generalize gauge metrics & saveError method [`#SWS-7776`](https://jira.skywindgroup.com/browse/SWS-7776)
- SWS-7762 define generic kafka writer [`#SWS-7762`](https://jira.skywindgroup.com/browse/SWS-7762)
- SWS-7584 Sonar to wallet [`#SWS-7584`](https://jira.skywindgroup.com/browse/SWS-7584)
- SWS-7228 remove redundant lines [`#SWS-7228`](https://jira.skywindgroup.com/browse/SWS-7228)
- SWS-7228 construct error object for logging [`#SWS-7228`](https://jira.skywindgroup.com/browse/SWS-7228)
- SWS-7182 unknown path [`#SWS-7182`](https://jira.skywindgroup.com/browse/SWS-7182)
- SWS-7141 loosing original error [`#SWS-7141`](https://jira.skywindgroup.com/browse/SWS-7141)
- SWS-6894 use uri from the hooks [`#SWS-6894`](https://jira.skywindgroup.com/browse/SWS-6894)
- SWS-6342 fix safeGet with one argument [`#SWS-6342`](https://jira.skywindgroup.com/browse/SWS-6342)
- SWS-6342 fix return value from safeGet [`#SWS-6342`](https://jira.skywindgroup.com/browse/SWS-6342)
- SWS-3694 add field in case we have an object as a first arg [`#SWS-3694`](https://jira.skywindgroup.com/browse/SWS-3694)
- SWS-6137 update version [`#SWS-6137`](https://jira.skywindgroup.com/browse/SWS-6137)
- SWS-6137 instrument base http module [`#SWS-6137`](https://jira.skywindgroup.com/browse/SWS-6137)
- SWS-3694 tests [`#SWS-3694`](https://jira.skywindgroup.com/browse/SWS-3694)
- SWS-3694 test logger [`#SWS-3694`](https://jira.skywindgroup.com/browse/SWS-3694)
- SWS-3694 instrument express/fastify [`#SWS-3694`](https://jira.skywindgroup.com/browse/SWS-3694)
- SWS-6137 instrument fastify [`#SWS-6137`](https://jira.skywindgroup.com/browse/SWS-6137) [`#SWS-3694`](https://jira.skywindgroup.com/browse/SWS-3694)
- SWS-3694 get traceId from the request header [`#SWS-3694`](https://jira.skywindgroup.com/browse/SWS-3694)
- SWS-6137 instrument fastify [`#SWS-6137`](https://jira.skywindgroup.com/browse/SWS-6137)
- SWS-6137  instrument express, fastify [`#SWS-6137`](https://jira.skywindgroup.com/browse/SWS-6137)
- SWS-6104 fix overloading safeGet issue [`#SWS-6104`](https://jira.skywindgroup.com/browse/SWS-6104)
- SWS-6104 get baseUrl correctly [`#SWS-6104`](https://jira.skywindgroup.com/browse/SWS-6104)
- SWS-5960 rename test file [`#SWS-5960`](https://jira.skywindgroup.com/browse/SWS-5960)
- SWS-5960 rename file [`#SWS-5960`](https://jira.skywindgroup.com/browse/SWS-5960)
- SWS-5960 rename function and declare typings [`#SWS-5960`](https://jira.skywindgroup.com/browse/SWS-5960)
- SWS-5960 add health functions to exports [`#SWS-5960`](https://jira.skywindgroup.com/browse/SWS-5960)
- SWS-5960 add health helper function [`#SWS-5960`](https://jira.skywindgroup.com/browse/SWS-5960)
- SWS-3627 fix compilation [`#SWS-3627`](https://jira.skywindgroup.com/browse/SWS-3627)
- SWS-3627  check condition for retry [`#SWS-3627`](https://jira.skywindgroup.com/browse/SWS-3627)
- SWS-3627 refactoring [`#SWS-3627`](https://jira.skywindgroup.com/browse/SWS-3627)
- SWS-3627 calculate total spent time [`#SWS-3627`](https://jira.skywindgroup.com/browse/SWS-3627)
- sWS-3627 unit tests [`#WS-3627`](https://jira.skywindgroup.com/browse/WS-3627)
- SWS-3627 repeatUntilBackOff function [`#SWS-3627`](https://jira.skywindgroup.com/browse/SWS-3627)
- SWS-4887  safetGet utility method [`#SWS-4887`](https://jira.skywindgroup.com/browse/SWS-4887)
- SWS-4820 fix multi instrumentation issue [`#SWS-4820`](https://jira.skywindgroup.com/browse/SWS-4820)
- SWS-4309 fix the case when we don't have active context [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4309 fix detailsExtractor condition [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4309 fix bug with redis instrumentation [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4309 parse sql to compose operation details [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4309 provider name [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4308 fixes [`#SWS-4308`](https://jira.skywindgroup.com/browse/SWS-4308)
- SWS-4309 add comments [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4309 testing [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4309 [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4309 custom metrics with details [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4309 instrument promises to track context fairly [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4309 add custom metrics [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4309 instrumentation of a single funciton [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4309 fix continuations [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-4309 measure providers/prometheus/continuation context [`#SWS-4309`](https://jira.skywindgroup.com/browse/SWS-4309)
- SWS-3628 fix compilation errors [`#SWS-3628`](https://jira.skywindgroup.com/browse/SWS-3628)
- SWS-3628 fix package.version [`#SWS-3628`](https://jira.skywindgroup.com/browse/SWS-3628)
- SWS-3670 amend tracer [`#SWS-3670`](https://jira.skywindgroup.com/browse/SWS-3670)
- SWS-2210 es2017 [`#SWS-2210`](https://jira.skywindgroup.com/browse/SWS-2210)
- SWS-1814 Add newrelic logging for offloaders [`#SWS-1814`](https://jira.skywindgroup.com/browse/SWS-1814)
- SWS-1867 Newrelic problem with fu-fish [`#SWS-1867`](https://jira.skywindgroup.com/browse/SWS-1867)
- SWS-1813 Add newrelic logging for websocket transactions [`#SWS-1813`](https://jira.skywindgroup.com/browse/SWS-1813)
- SWS-1316 Add Node.js VM measurements [`#SWS-1316`](https://jira.skywindgroup.com/browse/SWS-1316)
- SWS-1536 Adjust newrelic so we know which merchant is having performance problems on the external calls [`#SWS-1536`](https://jira.skywindgroup.com/browse/SWS-1536)
- SWS-1536 Adjust newrelic so we know which merchant is having performance problems on the external calls [`#SWS-1536`](https://jira.skywindgroup.com/browse/SWS-1536)
- SWS-1536 Adjust newrelic so we know which merchant is having performance problems on the external calls [`#SWS-1536`](https://jira.skywindgroup.com/browse/SWS-1536)
- SWS-1536 Adjust newrelic so we know which merchant is having performance problems on the external calls [`#SWS-1536`](https://jira.skywindgroup.com/browse/SWS-1536)
- SWS-1536 Adjust newrelic so we know which merchant is having performance problems on the external calls [`#SWS-1536`](https://jira.skywindgroup.com/browse/SWS-1536)
- SWS-1536 Adjust newrelic so we know which merchant is having performance problems on the external calls [`#SWS-1536`](https://jira.skywindgroup.com/browse/SWS-1536)
- SWS-1536 Adjust newrelic so we know which merchant is having performance problems on the external calls [`#SWS-1536`](https://jira.skywindgroup.com/browse/SWS-1536)
- Initial commit [`15902a2`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/15902a29d4bd0f2567ec500b76aee5ee649684a5)
- typigs, namespaces [`4ce3705`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/4ce370534967dffe35e8b6d22f9e5c0a48111065)
- working on nodejs-14 port [`be0b3ea`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/be0b3ea95cfd47708af33f7f05a9cfa711ebef0a)
- compare two types of transpiller [`f4bfb7a`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/f4bfb7a76776780da2ca2bb07ad09f29efe2f8b4)
- Basic try on newrelic logging decorator [`78f627b`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/78f627bf877167a1368cb885ecb58700afa048ba)
- unit tests for belongTo [`0b741d0`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/0b741d0418cd053b3bf34d53115b7c7b16fbe0c9)
- Mappings - finish with unit tests [`b4867c6`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/b4867c60f2e62e653744dcca33ccbc7e668f3e4d)
- fix the final implementation [`e29d579`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/e29d57972f0792652eee7e854ee76073f74f5666)
- Ability to get just properties values from managed DBItem [`9f1620d`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/9f1620d619c4a626708052e3807654405825e02f)
- adapt to sequelize 3 and 4 [`287df4e`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/287df4efa6958b746b3442162a00a05339a8778a)
- working with hierarchy of entities [`a229156`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/a229156bc189e0c1e7c5fffa8aa92a4e7c84be69)
- Add measures [`ee2d0d0`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/ee2d0d0cd263380112c233a9f4be99196c2346e7)
- Fix measure utils: arrow function has broken the decorator method [`b6fee7f`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/b6fee7f5ee76ff1a4a293acb4109964f368a0db1)
- Continue working with relations [`e12a775`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/e12a77593cbe42f034a91bb681c69e8d2935e338)
- prepare gulp [`6dccd5b`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/6dccd5bff449454d8660ca71d2d48300dd240586)
- Working on relations [`61c71c6`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/61c71c6ee88709dfac5c929914b487c12bb56fd5)
- Adding Jenkinsfile [`aea4ceb`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/aea4ceb53eb09b5d17f0c010f0097eda0b74761c)
- working with hierarchical structures [`2c30cea`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/2c30cea3f1ca873c978be69a8eb88c4f6427d9b3)
- config streams [`f6116eb`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/f6116eb135cd2dc52dd2b6de665f00121ff01812)
- fix p;ublish and generalize build config [`68c2eec`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/68c2eec9681f91806022aab9032828be87c43d28)
- amend docker files [`58ccb4a`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/58ccb4a83e6caddae6a189f76d599d05652728d6)
- SWS~-9318 config dependencies [`e4acecb`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/e4acecbab74a6bfa81cc70e0144e29eb08709591)
- fix circular dependencies problem [`c2e425c`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c2e425c14f33020759bb6ac58f764fc32738fdde)
- Add lazy and time utils [`b9075dd`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/b9075dde293c68e8b44c141d9fd24c164d2948e7)
- Adding Jenkinsfile [`bea21e2`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/bea21e23136783a5343b4adc5525abfff122a507)
- remove redundand files [`6bbef9d`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/6bbef9d770dbb39191b8cebfb1196f07a54ecea0)
- add npmignore [`a722523`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/a72252392c7fb65ca4d24cab0ed1794952a27582)
- Initial commit [`c1b47b6`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c1b47b6bbef1f0f62f99e684f023da0eace1e19e)
- Working on relations [`7a75ad5`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/7a75ad5e58d1bd08ee26c8260cfe27755e01d5e8)
- Refactoring [`3d3e0a7`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/3d3e0a74797f23db0d9eae39ccd2948206877cd7)
- Refactoring [`ea4d8fa`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/ea4d8fae9599008cf1f744fb73438ac9b3c94744)
- Better typing description for safeGet [`64d9b2d`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/64d9b2d345ed3befde40bacc155555d4f505d6eb)
- Removinf image push steps [`92cb0ee`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/92cb0ee3d3a9583feed1436e144a18f9a594dfd7)
- fix export [`c20f61a`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c20f61a0eaf962fce5e5c241a2b5291c97bf57da)
- code cleanup [`d69b5f5`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/d69b5f5c247456a9a909c03dd06b565437dae436)
- Fixing vuild since the debian stretch moved to archive [`7944a64`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/7944a64e31bf5e2aa00ff20078bd9a46b32c005f)
- fix sonar [`0916089`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/09160891fa6efc9412920aea031f8df908ab46da)
- remove redundant method [`93e3955`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/93e3955741c54f80bd5582f81b0b4a9948523d1e)
- remove 'private' marker [`90411c6`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/90411c694afa47545d43e4599c5ae50327d44041)
- SWs-4309 fix conditions [`2ef4415`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/2ef44156904fd10109959ff4df9c4bf1ff8adad9)
- move reflect-metadata to devdependancies [`64f17ae`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/64f17ae8c9af1f725a81c8863d5a8402b83c37dc)
- SWS34414 Fix prometheus middleware transactions [`67dbc41`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/67dbc418e2861a99a4d6f208cb5da28c87c1d47f)
- simplify conditions [`e6800f8`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/e6800f8507cdfc6fd88c154ccf1e0d74d88eef38)
- fix chai as promised types version [`c149769`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c149769626c9c64bd67a6f2ddd9a6636bbdc462d)
- fix typings in testing module [`ee3ba07`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/ee3ba070fbc5eab0d97025e3aed04e264c7588d1)
- fix version [`35341f0`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/35341f0d2187daf46a40a2526c49165479becb59)
- fix build [`3330630`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/3330630d795a4b00fca8e16d0f75ff8c7abdbcd1)
- release/v2 [`4d89bf0`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/4d89bf072a82092d0b0daa0c5aafd1bcc841de9f)
- bump version [`b7325be`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/b7325be7a1ef4150b39eaa9baff33ef5e9a92eb1)
- nodeversion added [`ee43838`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/ee438382da4193bdb50040609a3ef7970a2fe481)
- change node version [`ba53eee`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/ba53eeec08ef831a96bc14198847d9e2db3ea58b)
- update version [`912a9a2`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/912a9a2f50958941f3a8d4c0c55bc020b9103259)
- resolved conflicts for Dockerfile [`291ef89`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/291ef898783358503752add2f15a06b61193bf3a)
- fix name service [`e397971`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/e397971ab27fad9e5346c31e877ac9ca1d6e0475)
- Update version test [`4bd1fcb`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/4bd1fcb149d1f147727393233681a45c9d92dbde)
- Adding Jenkinsfile [`c41d0e3`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/c41d0e3510055ed976357161d8e5c6c8cd10e4ac)
- Adding sleep step [`4a77371`](https://bitbucket.skywindgroup.com/projects/SBEP/repos/sw-utils/commits/4a77371709867ecc0a962850cb4bbfa9cbe46943)
