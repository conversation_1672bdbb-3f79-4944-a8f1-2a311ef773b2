module.exports = {
    extends: [
        "eslint:recommended",
        "plugin:node/recommended",
        "plugin:@typescript-eslint/recommended",
    ],
    rules: {
        "no-prototype-builtins": "off",
        "prefer-rest-params": "warn",
        "prefer-spread": "warn",
        "semi": ["warn", "always", { "omitLastInOneLineBlock": false}],
        "semi-style": ["warn", "last"],
        "no-extra-semi": ["warn"],
        "semi-spacing": ["warn", { "before": false, "after": true }],
        "comma-dangle": ["error", {
            "arrays": "never",
            "objects": "never",
            "imports": "never",
            "exports": "never",
            "functions": "never"
        }],
        "object-curly-spacing": ["warn", "always"],
        "@typescript-eslint/ban-ts-comment": "warn",
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-unused-vars": "off",
        "@typescript-eslint/no-var-requires": "off",
        "@typescript-eslint/no-namespace": "warn",
        "@typescript-eslint/no-loss-of-precision": "warn",
        "@typescript-eslint/no-empty-function": "warn",
        "@typescript-eslint/ban-types": "warn",
        "@typescript-eslint/no-this-alias": "warn",
        "@typescript-eslint/no-inferrable-types": "warn",
        "@typescript-eslint/no-empty-interface": "warn",
        "node/exports-style": ["error", "module.exports"],
        "node/file-extension-in-import": ["off", "always"],
        "node/prefer-global/buffer": ["error", "always"],
        "node/prefer-global/console": ["error", "always"],
        "node/prefer-global/process": ["error", "always"],
        "node/prefer-global/url-search-params": ["error", "always"],
        "node/prefer-global/url": "off",
        "node/prefer-promises/dns": "error",
        "node/prefer-promises/fs": "error",
        "node/no-missing-import": "off",
        "node/no-unsupported-features/es-syntax":"off",
        "node/no-unpublished-import": "off"
    },
    ignorePatterns: [
        "./node_modules/",
    ],
    parserOptions: {
        ecmaVersion: 2021,
        sourceType: "module",
        project: [
            require.resolve('./tsconfig.json')
        ]
    },
    plugins: [
        "@typescript-eslint"
    ]
}
