class LazyInstance<T> {

    private instance: T;

    constructor(private readonly initializer: (options?) => T) { }

    public get(options?): T {
        return this.instance || (this.instance = this.initializer(options));
    }
}

/**
 * Creates a new instance of <T> getter that uses the specified initializer function.
 * The first call to get() executes the lambda passed to lazy() and remembers the result,
 * subsequent calls to get() simply return the remembered result.
 *
 * If the initialization of a value throws an exception, it will attempt to reinitialize the value at next access.
 *
 * @param {(options?) => T} initializer
 * @returns {{get(options?): T}}  Returns property accessor to lazy instance.
 */
export function lazy<T>(initializer: (options?) => T): { get(options?): T } {
    return new LazyInstance<T>(initializer);
}
