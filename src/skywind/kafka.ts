import { KafkaClient, Producer, ProduceRequest } from "kafka-node";
import { logging } from "./logging/logging";
import { measures } from "./measures/measures";
import { sleep } from "./time";
import measure = measures.measure;

export namespace kafka {
    import Logger = logging.Logger;

    class InitKafkaProducerTimeoutError extends Error {
        constructor() {
            super("Error init kafka producer");
        }
    }

    export interface KafkaConfiguration {
        kafkaBrokerHostnames: string;
        topicName: string;
        requireAck?: number;
        ackTimeoutMs?: number;
        clientCreationTimeout?: number;
        requestTimeout?: number;
        partitionerType?: number;
        maxSendAttemptTimeout?: number;
        noAckBatchSize?: number;
        noAckBatchAge?: number;
    }

    export interface KafkaWriter {
        sendMessages(messages: string[], key?: string): Promise<void>;
    }

    export class KafkaWriterIpml implements KafkaWriter {
        private kafkaProducer: Producer;

        constructor(private readonly config: KafkaConfiguration, private readonly logger: Logger) {
        }

        @measure({ name: "KafkaWriter.sendMessages", isAsync: true })
        public async sendMessages(messages: string[], key?: string): Promise<void> {
            const payloads: ProduceRequest[] = [{ topic: this.config.topicName, messages, attributes: 1, key }];
            return this.sendPayloads(payloads);
        }

        private async sendPayloads(payloads: ProduceRequest[]): Promise<void> {
            if (!this.kafkaProducer) {
                await this.initProducer();
            }
            let attemptTimeout = 100;
            let attempt = 0;
            let lastError;
            while (attemptTimeout < this.config.maxSendAttemptTimeout) {
                try {
                    return await this.doSend(payloads);
                } catch (err) {
                    lastError = err;
                    measures.measureProvider.saveError(err);
                    this.logger.warn(err, "Failed to send data to %s(%s), attempt %d",
                        this.config.kafkaBrokerHostnames,
                        this.config.topicName,
                        ++attempt);
                }

                await sleep(attemptTimeout);
                attemptTimeout *= 2;
            }

            // enforce reconnect;
            await this.closeProducer();

            return Promise.reject(lastError || new Error("Max attempts acceded!"));
        }

        @measure({ name: "KafkaWriter.initProducer", isAsync: true })
        public async initProducer(): Promise<void> {
            this.logger.info("Init Kafka producer for %s(%s)",
                this.config.kafkaBrokerHostnames,
                this.config.topicName);

            this.kafkaProducer = await new Promise<Producer>((resolve, reject) => {
                const noAckBatchOptions = this.config.noAckBatchSize ? {
                    noAckBatchSize: this.config.noAckBatchSize,
                    noAckBatchAge: this.config.noAckBatchAge,
                } : undefined;

                const client = new KafkaClient({
                    kafkaHost: this.config.kafkaBrokerHostnames,
                    requestTimeout: this.config.requestTimeout,
                    noAckBatchOptions,
                } as any);
                client.on("error", (err) => {
                    measures.measureProvider.saveError(err);
                    this.logger.error(err, "Kafka client error");
                    reject(err);
                    return this.closeProducer();
                });
                client.on("socket_error", (err) => {
                    measures.measureProvider.saveError(err);
                    this.logger.error(err, "Kafka client socket error");
                    reject(err);
                    return this.closeProducer();
                });

                const producer = new Producer(client, {
                    requireAcks: this.config.requireAck,
                    ackTimeoutMs: this.config.ackTimeoutMs,
                    partitionerType: this.config.partitionerType,
                });

                setTimeout(() => reject(new InitKafkaProducerTimeoutError()), (this.config.clientCreationTimeout));
                producer.on("ready", () => {
                    resolve(producer);
                });
                producer.on("error", (err) => {
                    measures.measureProvider.saveError(err);
                    reject(err);
                });
            });

            this.logger.info("Kafka producer for %s(%s)is ready",
                this.config.kafkaBrokerHostnames,
                this.config.topicName);
        }

        private async closeProducer() {
            if (this.kafkaProducer) {
                try {
                    // do not wait for closing current connection
                    this.kafkaProducer.close();
                } finally {
                    this.kafkaProducer = undefined;
                }
            }
        }

        private async doSend(payloads: ProduceRequest[]): Promise<void> {
            return new Promise<void>((resolve, reject) => {
                if (!this.kafkaProducer) {
                    return reject(new Error("Kafka producer is not initialized!"));
                }
                try {
                    this.kafkaProducer.send(payloads, (err) => {
                        return err ? reject(err) : resolve();
                    });
                } catch (err) {
                    return reject(err);
                }
            });
        }
    }

    const defaultConfigOptions: KafkaConfiguration = {
        kafkaBrokerHostnames: undefined,
        topicName: undefined,
        requireAck: -1,
        noAckBatchSize: 0,
        noAckBatchAge: 0,
        ackTimeoutMs: 1000,
        clientCreationTimeout: 6000,
        requestTimeout: 5000,
        partitionerType: 2,
        maxSendAttemptTimeout: 10000,
    };

    export async function createWriter(config: KafkaConfiguration, logger: Logger): Promise<KafkaWriter> {
        const result = new KafkaWriterIpml({ ...defaultConfigOptions, ...config }, logger);
        try {
            await result.initProducer();
        } catch (err) {
            logger.error(err);
        }

        return result;
    }
}
