import type { CurrencyDatasource, CurrencyProvider } from "./provider";
import type { ProviderExchangeRate } from "../entities/exchangeRates";
import { getRateKey, mapProviderRates } from "./utils";
import { SWSCurrencyDatasource } from "../datasources/SWSCurrencyDatasource";

export class SWSCurrencyProvider implements CurrencyProvider {
    private readonly dataSource: CurrencyDatasource;

    constructor() {
        this.dataSource = new SWSCurrencyDatasource();
    }

    public async getRates(providerDate: Date, baseCurrencies: string[]): Promise<ProviderExchangeRate[]> {
        const data = new Map<string, ProviderExchangeRate>();
        const rates = await this.dataSource.load(providerDate, baseCurrencies);
        mapProviderRates(rates).forEach((rate) => {
            data.set(getRateKey(rate), rate);
        });
        return Array.from(data.values());
    }
}
