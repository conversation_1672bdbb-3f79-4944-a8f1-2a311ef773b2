import { Currencies } from "@skywind-group/sw-currency-exchange";
import type { CurrencyDatasource, CurrencyProvider} from "./provider";
import { Provider } from "./provider";
import type { ProviderExchangeRate } from "../entities/exchangeRates";
import { OANDACurrencyDatasource } from "../datasources/OANDACurrencyDatasource";
import { OXRCurrencyDatasource } from "../datasources/OXRCurrencyDatasource";
import config from "../config";
import { getRateKey, mapProviderRates } from "./utils";

export class MergeCurrencyProvider implements CurrencyProvider {
    private readonly sources: Record<string, CurrencyDatasource>;

    constructor() {
        this.sources = Array.from(new Set(Currencies.values().map(({ provider }) => provider as Provider || config.provider)))
            .map<[Provider, CurrencyDatasource]>((provider) => {
                switch (provider) {
                    case Provider.OANDA:
                        return [Provider.OANDA, new OANDACurrencyDatasource()];
                    case Provider.OXR:
                        return [Provider.OXR, new OXRCurrencyDatasource()];
                    default:
                        return undefined;
                }
            })
            .filter(Boolean)
            .reduce<Record<string, CurrencyDatasource>>((result, [name, provider]) => {
                return ({
                    ...result,
                    [name]: provider
                });
            }, {});
    }

    public async getRates(providerDate: Date, baseCurrencies: string[]): Promise<ProviderExchangeRate[]> {
        const data = new Map<string, ProviderExchangeRate>();
        const baseProvider = this.sources[config.provider];
        if (baseProvider) {
            const rates = await baseProvider.load(providerDate, baseCurrencies);
            mapProviderRates(rates).forEach((rate) => {
                data.set(getRateKey(rate), rate);
            });
        }
        const extraProviders = Object.entries(this.sources)
            .filter(([name]) => name !== config.provider)
            .map(([, provider]) => provider)
            .filter(Boolean);
        if (extraProviders.length) {
            const results = await Promise.all(extraProviders
                .map((provider) => provider.load(providerDate, baseCurrencies)));
            const rates = results.reduce((result, values) => [
                ...result,
                ...mapProviderRates(values)
            ], []);
            rates.forEach((rate) => {
                const key = getRateKey(rate);
                if (!data.has(key)) {
                    data.set(key, rate);
                }
            });
        }
        return Array.from(data.values());
    }
}
