import type { CurrencyProvider} from "./provider";
import { Provider } from "./provider";
import type { ProviderExchangeRate } from "../entities/exchangeRates";
import { mapProviderRates } from "./utils";

const currencyResponses = require("./default_currency_rates.json");

export class DefaultCurrencyProvider implements CurrencyProvider {
    private readonly rates: ProviderExchangeRate[];

    constructor() {
        const today = new Date();
        this.rates = mapProviderRates({
            provider: Provider.DEFAULT,
            ts: today,
            bidRates: {
                [currencyResponses.base]: currencyResponses.rates
            },
            askRates: {
                [currencyResponses.base]: currencyResponses.rates
            }
        });
    }

    public getRates(providerDate: Date): Promise<ProviderExchangeRate[]> {
        return Promise.resolve(this.rates.map((rate) => ({
            ...rate,
            providerDate,
        })));
    }
}
