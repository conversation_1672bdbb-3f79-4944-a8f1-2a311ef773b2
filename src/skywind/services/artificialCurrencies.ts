import { Currencies } from "@skywind-group/sw-currency-exchange";
import { findRates } from "../models/exchangeRates";
import config from "../config";
import { getTimestamp } from "../providers/utils";
import { Op } from "sequelize";
import type { ExchangeRate, ExchangeRateFilter } from "../entities/exchangeRates";

const artificialCurrencies = Currencies.artificialValues();

const baseCodes = new Set(config.baseCurrencies);
const targetCodes = new Set(artificialCurrencies.map(({ originCurrency }) => originCurrency.currency));
const availableCodes = new Set([
    ...config.baseCurrencies,
    ...artificialCurrencies.map(({ code }) => code)
]);

interface BaseRatesRange {
    [date: string]: {
        [base: string]: {
            [to: string]: ExchangeRate
        };
    };
}

export async function getArtificialRatesRange(
    startDate: Date,
    endDate: Date,
    filter: ExchangeRateFilter = {},
    hideGGR = false
) {
    if (filter.from && !availableCodes.has(filter.from)) {
        return [];
    }
    if (filter.to && !availableCodes.has(filter.to)) {
        return [];
    }

    function filterExchangeRate(exchangeRate: ExchangeRate): boolean {
        if (filter.from && exchangeRate.from !== filter.from) {
            return false;
        }
        return !filter.to || exchangeRate.to === filter.to;
    }

    const exchangeRates = await findRates({
        rateDate: {
            [Op.gte]: startDate,
            [Op.lte]: endDate
        },
        from: { [Op.in]: Array.from(baseCodes) },
        to: { [Op.in]: Array.from(targetCodes) },
        ...(filter.type ? { type: filter.type } : {})
    });
    const baseRatesRange: BaseRatesRange = {};
    for (const exchangeRate of exchangeRates) {
        const timestamp = getTimestamp(exchangeRate.rateDate);
        baseRatesRange[timestamp] = {
            ...(baseRatesRange[timestamp] ?? {}),
            [exchangeRate.from]: {
                ...(baseRatesRange[timestamp]?.[exchangeRate.from] ?? {}),
                [exchangeRate.to]: exchangeRate
            }
        };
    }

    const rates: ExchangeRate[] = [];
    for (const rangeRates of Object.values(baseRatesRange)) {
        for (const baseRates of Object.values(rangeRates)) {
            for (const { code, disableGGR, originCurrency: { currency, multiplier } } of artificialCurrencies) {
                const exchangeRate = baseRates[currency];
                if (exchangeRate) {
                    const rate: ExchangeRate = {
                        ...exchangeRate,
                        to: code,
                        rate: exchangeRate.rate / multiplier,
                        artificial: true,
                        ...(disableGGR ? { disableGGR: true } : {})
                    };
                    if (hideGGR && disableGGR) {
                        rate.rate = 0;
                    }
                    rates.push(rate);
                }
            }
        }
    }

    return rates.filter(filterExchangeRate);
}
