import { BaseHttpService } from "./baseHTTPService";
import { SWJackpotTicker } from "../definitions/game";

export interface TickerService {
    getTickers(jackpotIds: string[], currency: string, skipError?: boolean): Promise<SWJackpotTicker[]>;
}

export class SWTickerService extends BaseHttpService implements TickerService {
    public static TICKER_API_URL = "v1/ticker/";

    public getTickers(jackpotIds: string[], currency: string, skipError = true): Promise<SWJackpotTicker[]> {
        return this.get(SWTickerService.TICKER_API_URL, {
            jackpotIds: jackpotIds.join(),
            currency,
            skipError
        });
    }
}
