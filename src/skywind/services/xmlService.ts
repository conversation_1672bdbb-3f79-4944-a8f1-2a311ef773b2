import { logging } from "@skywind-group/sw-utils";
import { ConvertToXmlError, XmlParseError } from "../errors";
import { XMLBuilder, XMLParser, XmlBuilderOptions, X2jOptions } from "fast-xml-parser";
const log = logging.logger("xml-service");

export class XmlService {
    private xmlToObjOptions: X2jOptions = {
        ignoreAttributes: false,
        parseAttributeValue: true,
        attributeNamePrefix: "_"
    };

    private objToXmlOptions: XmlBuilderOptions = {
        attributeNamePrefix: "@_",
        textNodeName: "#text",
        ignoreAttributes: true,
        format: false,
        indentBy: "    "
    };

    private readonly parser: XMLParser;
    private readonly builder: XMLBuilder;

    constructor(xmlToObjOptions?: X2jOptions, objToXmlOptions?: XmlBuilderOptions) {
        this.xmlToObjOptions = xmlToObjOptions || this.xmlToObjOptions;
        this.objToXmlOptions = objToXmlOptions || this.objToXmlOptions;
        this.parser = new XMLParser(this.xmlToObjOptions);
        this.builder = new XMLBuilder(this.objToXmlOptions);
    }

    public convertToXML(objectData: any = {}): string {
        try {
            return this.builder.build(objectData);
        } catch (error) {
            const convertError = new ConvertToXmlError(objectData);
            log.error(convertError, "convert to XML error");
            throw convertError;
        }
    }

    public convertToObject(xml: string): any {
        try {
            return this.parser.parse(xml, true);
        } catch (error) {
            const xmlError = new XmlParseError(xml);
            log.error(xmlError, "XML parse error");
            throw xmlError;
        }
    }
}
