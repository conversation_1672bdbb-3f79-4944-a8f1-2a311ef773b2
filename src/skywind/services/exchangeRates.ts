import * as ExchangeRatesModel from "../models/exchangeRates";
import type { ExchangeRate, ExchangeRateFilter } from "../entities/exchangeRates";
import { getNextDay, getTimestamp } from "../providers/utils";
import * as Errors from "../errors";
import { initUpdateJob } from "./updateJob";
import { getArtificialRatesRange } from "./artificialCurrencies";
import { ExchangeRateType } from "@skywind-group/sw-currency-exchange";

interface ExchangeRateInfo {
    from: string;
    to: string;
    rate: number;
    artificial?: true;
    disableGGR?: true;
}

export interface ExchangeRatesInfo {
    date: string;
    source: {
        date: string;
        provider: string;
        type: ExchangeRateType;
    };
    rates: ExchangeRateInfo[];
}

export async function getRates(date: Date, filter: ExchangeRateFilter = {}): Promise<ExchangeRatesInfo> {
    filter.type = filter.type || ExchangeRateType.BID;
    const rates = await ExchangeRatesModel.getRates(date, filter);
    if (!rates || !rates.length) {
        return Promise.reject(new Errors.ExchangeRatesNotFoundError());
    }
    return {
        date: getTimestamp(date),
        source: {
            date: getTimestamp(rates[0].providerDate),
            provider: rates[0].provider,
            type: rates[0].type
        },
        rates: rates.map((rate) => ({
            from: rate.from,
            to: rate.to,
            rate: +rate.rate
        }))
    };
}

export async function getRatesRange(
    startDate: Date,
    endDate: Date,
    filter: ExchangeRateFilter = {},
    includeArtificial = false,
    hideGGR = false
): Promise<ExchangeRatesInfo[]> {
    filter.type = filter.type || ExchangeRateType.BID;
    const rates = await ExchangeRatesModel.getRatesRange(startDate, endDate, filter);
    if (includeArtificial) {
        const artificialRates = await getArtificialRatesRange(startDate, endDate, filter, hideGGR);
        artificialRates.forEach((rate) => {
            rates.push(rate);
        });
    }
    return mapExchangeRates(rates);
}

export async function loadRates(startDate: Date, endDate: Date, baseCurrencies?: string[]): Promise<ExchangeRatesInfo[]> {
    const rates: ExchangeRate[] = [];
    const updateJob = await initUpdateJob();
    let date: Date = startDate;
    while (date <= endDate) {
        const loadedRates = await updateJob.load(date, baseCurrencies);
        rates.push(...loadedRates);

        date = getNextDay(date);
    }
    return mapExchangeRates(rates);
}

function mapExchangeRates(rates: ExchangeRate[]): ExchangeRatesInfo[] {
    rates.sort((r1, r2) => {
        return new Date(r1.rateDate).getTime() - new Date(r2.rateDate).getTime();
    });

    const bidRates = new Map<Date, ExchangeRatesInfo>();
    const askRates = new Map<Date, ExchangeRatesInfo>();
    for (const rate of rates) {
        const rateDates = rate.type === ExchangeRateType.BID ? bidRates : askRates;
        if (!rateDates.has(rate.rateDate)) {
            rateDates.set(rate.rateDate, {
                date: getTimestamp(rate.rateDate),
                source: {
                    date: getTimestamp(rate.providerDate),
                    provider: rate.provider,
                    type: rate.type
                },
                rates: []
            });
        }
        rateDates.get(rate.rateDate).rates.push({
            from: rate.from,
            to: rate.to,
            rate: +rate.rate,
            ...(rate.artificial ? { artificial: true } : {}),
            ...(rate.disableGGR ? { disableGGR: true } : {})
        });
    }

    return [...bidRates.values(), ...askRates.values()];
}
