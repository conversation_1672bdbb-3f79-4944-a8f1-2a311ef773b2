import { PlayerInfo } from "../definitions/player";
import {
    MerchantGameInitRequest,
    MerchantGameTokenData, MerchantGameTokenInfo,
    MerchantGameURLInfo,
    MerchantStartGameTokenData, LoginTerminalRequest, LoginTerminalResponse, AdditionalInfo
} from "../definitions/startGame";
import { MerchantInfo } from "../definitions/common";
import { BaseHttpService } from "./baseHTTPService";
import { MERCHANT_REGULATION } from "../..";

export interface StartGameService<GI extends MerchantGameInitRequest = MerchantGameInitRequest,
    SGT extends MerchantStartGameTokenData = MerchantStartGameTokenData,
    AUTHT extends MerchantGameTokenData = MerchantGameTokenData,
    GUI extends MerchantGameURLInfo = MerchantGameURLInfo> {

    getGameTokenInfo(merchant: MerchantInfo,
                     startToken: SGT,
                     currency: string,
                     transferEnabled: boolean,
                     additionalInfo?: AdditionalInfo): Promise<MerchantGameTokenInfo<AUTHT>>;

    createGameUrl(merchant: MerchantInfo,
                  gameCode: string,
                  providerCode: string,
                  providerGameCode: string,
                  initRequest: GI,
                  player?: PlayerInfo): Promise<GUI>;

    getStartGameTokenData(merchant: MerchantInfo,
                          gameCode: string,
                          providerCode: string,
                          providerGameCode: string,
                          initRequest: GI): Promise<SGT>;

    loginTerminalPlayer(merchant: MerchantInfo,
                        initRequest: LoginTerminalRequest): Promise<LoginTerminalResponse<SGT>>;

    keepAlive(merchant: MerchantInfo, gameToken: AUTHT): Promise<void>;
}

export abstract class DefaultMerchantStartGameService<GI extends MerchantGameInitRequest = MerchantGameInitRequest,
    SGT extends MerchantStartGameTokenData = MerchantStartGameTokenData,
    AUTHT extends MerchantGameTokenData = MerchantGameTokenData,
    GUI extends MerchantGameURLInfo = MerchantGameURLInfo> implements StartGameService<GI, SGT, AUTHT, GUI> {

    public abstract getGameTokenInfo(merchant: MerchantInfo,
                                     startGameToken: SGT,
                                     currency: string,
                                     transferEnabled: boolean,
                                     additionalInfo?: AdditionalInfo): Promise<MerchantGameTokenInfo<AUTHT>>;

    public abstract createGameUrl(merchant: MerchantInfo,
                                  gameCode: string,
                                  providerCode: string,
                                  providerGameCode: string,
                                  initRequest: GI): Promise<GUI>;

    public abstract getStartGameTokenData(merchant: MerchantInfo,
                                          gameCode: string,
                                          providerCode: string,
                                          providerGameCode: string,
                                          initRequest: GI): Promise<SGT>;

    public abstract loginTerminalPlayer(merchant: MerchantInfo,
                                        initRequest: LoginTerminalRequest): Promise<LoginTerminalResponse<SGT>>;

    public abstract keepAlive(merchant: MerchantInfo, gameToken: AUTHT): Promise<void>;
}

export class HTTPMerchantStartGameService<GI extends MerchantGameInitRequest = MerchantGameInitRequest,
    SGT extends MerchantStartGameTokenData = MerchantStartGameTokenData,
    AUTHT extends MerchantGameTokenData = MerchantGameTokenData,
    GUI extends MerchantGameURLInfo = MerchantGameURLInfo>

    extends BaseHttpService implements StartGameService<GI, SGT, AUTHT, GUI> {

    constructor(adapterUrl: string, merchantInfo: MerchantInfo, regulation?: MERCHANT_REGULATION) {
        super(adapterUrl, { merchantInfo, regulation });
    }

    public async getGameTokenInfo(merchant: MerchantInfo,
                                  startGameToken: SGT,
                                  currency: string,
                                  transferEnabled: boolean,
                                  additionalInfo?: AdditionalInfo): Promise<MerchantGameTokenInfo<AUTHT>> {

        return this.post<MerchantGameTokenInfo<AUTHT>>(`games/${startGameToken.gameCode}/token`, {
            startGameToken,
            currency,
            transferEnabled,
            additionalInfo
        });
    }

    public async createGameUrl(merchant: MerchantInfo,
                               gameCode: string,
                               providerCode: string,
                               providerGameCode: string,
                               initRequest: GI): Promise<GUI> {
        return this.post<GUI>(`games/${gameCode}/url`, {
            initRequest,
            providerCode,
            providerGameCode
        });
    }

    public async getStartGameTokenData(merchant: MerchantInfo,
                                       gameCode: string,
                                       providerCode: string,
                                       providerGameCode: string,
                                       initRequest: GI): Promise<SGT> {
        return this.post<SGT>(`games/${gameCode}/start-token`, {
            initRequest,
            gameCode,
            providerCode,
            providerGameCode
        });
    }

    public loginTerminalPlayer(merchant: MerchantInfo,
                               initRequest: LoginTerminalRequest): Promise<LoginTerminalResponse<SGT>> {
        return this.post<LoginTerminalResponse<SGT>>(`games/login-terminal`, { initRequest });
    }

    public keepAlive(merchant: MerchantInfo, gameToken: AUTHT): Promise<void> {
        return this.post<void>(`games/keep-alive`, { gameToken });
    }
}
