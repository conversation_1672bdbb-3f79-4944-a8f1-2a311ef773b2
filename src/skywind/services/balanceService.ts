import { Balance, BalanceRequestWithoutToken, Balances } from "../definitions/balance";
import { MerchantGameTokenData } from "../definitions/startGame";
import { MerchantInfo } from "../definitions/common";
import { BaseHttpService } from "./baseHTTPService";
import { MERCHANT_REGULATION } from "../../";

export interface MerchantBalanceService <AUTHT extends MerchantGameTokenData = MerchantGameTokenData> {

    getBalances(merchant: MerchantInfo, gameToken: AUTHT): Promise<Balances>;
    getBalanceWithoutToken?(merchant: MerchantInfo, request: BalanceRequestWithoutToken): Promise<Balance>;
}

export abstract class DefaultMerchantBalanceService <AUTHT extends MerchantGameTokenData = MerchantGameTokenData>
    implements MerchantBalanceService <AUTHT> {

    public abstract getBalances(merchant: MerchantInfo, gameToken: AUTHT): Promise<Balances>;
    public abstract getBalanceWithoutToken(merchant: MerchantInfo,
                                           request: BalanceRequestWithoutToken): Promise<Balance>;
}

export class HTTPMerchantBalanceService <AUTHT extends MerchantGameTokenData = MerchantGameTokenData>
    extends BaseHttpService implements MerchantBalanceService <AUTHT> {

    constructor(adapterUrl: string, merchantInfo: MerchantInfo, regulation?: MERCHANT_REGULATION) {
        super(adapterUrl, { merchantInfo, regulation });
    }

    public async getBalances(merchant: MerchantInfo, gameTokenData: AUTHT): Promise<Balances> {
        return this.post<Balances>("balances", { gameTokenData });
    }

    public async getBalanceWithoutToken(merchant: MerchantInfo, request: BalanceRequestWithoutToken): Promise<Balance> {
        return this.post<Balance>("balances", { request });
    }
}
