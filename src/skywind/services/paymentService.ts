import { MerchantTransferRequest } from "../definitions/transfer";
import { Balance } from "../definitions/balance";
import { RollbackBetRequest, RollbackWithoutTokenRequest } from "../definitions/rollback";
import { MerchantGameTokenData } from "../definitions/startGame";
import {
    OfflineBonusPaymentRequest,
    GameState,
    GameStatus,
    GameType,
    PaymentRequest,
    PaymentRequestWithoutToken, OfflineBonusInfo, RegisterRoundRequest, RegisterRoundResponse
} from "../definitions/payment";
import { MerchantInfo, PLATFORM_TYPE } from "../definitions/common";
import { BaseHttpService } from "./baseHTTPService";
import { RefundBetRequest } from "../definitions/refund";
import { MERCHANT_REGULATION } from "../../";

export interface MerchantPaymentService <AUTHT extends MerchantGameTokenData = MerchantGameTokenData> {

    commitPayment(merchant: MerchantInfo, gameToken: AUTHT, request: PaymentRequest): Promise<Balance>;

    commitBetPayment(merchant: MerchantInfo, gameToken: AUTHT, request: PaymentRequest): Promise<Balance>;

    commitWinPayment(merchant: MerchantInfo, gameToken: AUTHT, request: PaymentRequest): Promise<Balance>;

    refundBetPayment(merchant: MerchantInfo, gameToken: AUTHT, request: RefundBetRequest): Promise<Balance>;

    commitWinWithoutToken?(merchant: MerchantInfo, request: PaymentRequestWithoutToken): Promise<Balance>;

    transfer(merchant: MerchantInfo, gameToken: AUTHT, request: MerchantTransferRequest): Promise<Balance>;

    rollbackBetPayment?(merchant: MerchantInfo, gameToken: AUTHT, request: RollbackBetRequest): Promise<Balance>;

    rollbackBetPaymentWithoutToken?(merchant: MerchantInfo, request: RollbackWithoutTokenRequest): Promise<Balance>;

    registerRound?(merchant: MerchantInfo,
                   request: RegisterRoundRequest): Promise<RegisterRoundResponse>;

    commitBonusPayment(merchant: MerchantInfo, gameToken: AUTHT, request: PaymentRequest): Promise<Balance>;

    commitOfflineBonusPayment?(merchant: MerchantInfo, request: OfflineBonusPaymentRequest): Promise<OfflineBonusInfo>;
}

export abstract class DefaultMerchantPaymentService <AUTHT extends MerchantGameTokenData = MerchantGameTokenData>
    implements MerchantPaymentService <AUTHT> {

    public abstract commitPayment(merchant: MerchantInfo, gameToken: AUTHT,
                                  request: PaymentRequest): Promise<Balance>;
    public abstract commitBetPayment(merchant: MerchantInfo, gameToken: AUTHT,
                                     request: PaymentRequest): Promise<Balance>;
    public abstract commitWinPayment(merchant: MerchantInfo, gameToken: AUTHT,
                                     request: PaymentRequest): Promise<Balance>;
    public abstract refundBetPayment(merchant: MerchantInfo, gameToken: AUTHT,
                                     request: RefundBetRequest): Promise<Balance>;
    public abstract transfer(merchant: MerchantInfo, gameToken: AUTHT,
                             request: MerchantTransferRequest): Promise<Balance>;
    public abstract commitBonusPayment(merchant: MerchantInfo, gameToken: AUTHT,
                                       request: PaymentRequest): Promise<Balance>;

    protected getPlatform(deviceId: string): string {
        // In our system we have "mobile" and "Web"
        if (deviceId && deviceId.toLowerCase() === PLATFORM_TYPE.MOBILE) {
            return PLATFORM_TYPE.MOBILE;
        } else {
            return PLATFORM_TYPE.WEB;
        }
    }

    protected getGameType(request: GameState): GameType {
        if (request.gameType) {
            return request.gameType;
        }
        if (request.spinType) {
            switch (request.spinType) {
                case "freeGame":
                case "reSpin":
                    return "freegame";
                case "bonus":
                    return "bonusgame";
                case "main":
                default:
                    return "normal";
            }
        }
        if (request.currentScene) {
            switch (request.currentScene) {
                case "freeSpins":
                    return "freegame";
                case "bonusGame":
                    return "bonusgame";
                case "main":
                default:
                    return "normal";
            }
        }

        return "normal";
    }

    protected getGameStatus(request: GameState): GameStatus {
        if (request.roundEnded) {
            return "settled";
        }
        if (request.gameStatus) {
            return request.gameStatus;
        }
        if (request.spinType) {
            if (request.spinType === "bonus") {
                return "bonusgame";
            }
            return "freegame";
        }
        if (request.currentScene && request.currentScene === "bonusGame") {
            return "bonusgame";
        }
        return "freegame";
    }
}

export class HTTPMerchantPaymentService <AUTHT extends MerchantGameTokenData = MerchantGameTokenData>

    extends BaseHttpService
    implements MerchantPaymentService <AUTHT> {

    constructor(adapterUrl: string, merchantInfo: MerchantInfo, regulation?: MERCHANT_REGULATION) {
        super(adapterUrl, { merchantInfo, regulation });
    }

    public async commitPayment(merchant: MerchantInfo,
                               gameTokenData: AUTHT, request: PaymentRequest): Promise<Balance> {
        return this.post<Balance>("payments", { gameTokenData, request });
    }

    public async commitBetPayment(merchant: MerchantInfo,
                                  gameTokenData: AUTHT, request: PaymentRequest): Promise<Balance> {
        return this.post<Balance>("payments/bet", { gameTokenData, request });
    }

    public async commitWinPayment(merchant: MerchantInfo,
                                  gameTokenData: AUTHT, request: PaymentRequest): Promise<Balance> {
        return this.post<Balance>("payments/win", { gameTokenData, request });
    }

    public async commitWinWithoutToken(merchant: MerchantInfo,
                                       request: PaymentRequestWithoutToken): Promise<Balance> {
        return this.post<Balance>("payments/win", { request });
    }

    public async transfer(merchant: MerchantInfo,
                          gameTokenData: AUTHT, request: MerchantTransferRequest): Promise<Balance> {
        return this.post<Balance>("payments/transfer", { gameTokenData, request });
    }

    public async rollbackBetPayment(merchant: MerchantInfo, gameTokenData: AUTHT,
                                    request: RollbackBetRequest): Promise<Balance> {
        return this.post<Balance>("payments/bet/rollback", { gameTokenData, request });
    }

    public async rollbackBetPaymentWithoutToken(merchant: MerchantInfo,
                                                request: RollbackWithoutTokenRequest): Promise<Balance> {
        return this.post<Balance>("payments/bet/rollback", { request });
    }
    public async registerRound(merchant: MerchantInfo, request: RegisterRoundRequest): Promise<RegisterRoundResponse> {
        return this.put<RegisterRoundResponse>("register_round", { request });
    }
    public async refundBetPayment(merchant: MerchantInfo, gameTokenData: AUTHT,
                                  request: RefundBetRequest): Promise<Balance> {
        return this.post<Balance>("payments/bet/refund", { gameTokenData, request });
    }

    public commitBonusPayment(merchant: MerchantInfo, gameTokenData: AUTHT, request: PaymentRequest): Promise<Balance> {
        return this.post<Balance>("payments/bonus", { gameTokenData, request });
    }

    public commitOfflineBonusPayment(merchant: MerchantInfo,
                                     request: OfflineBonusPaymentRequest): Promise<OfflineBonusInfo> {
        return this.post<OfflineBonusInfo>("payments/bonus/offline", { request });
    }
}
