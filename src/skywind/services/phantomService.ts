import { BaseHttpService } from "./baseHTTPService";

export interface PhantomJackpot {
    jackpotId: string;
    games: string[];
}

interface PhantomJackpotResponse {
    jackpots: PhantomJackpot[];
}

export interface PhantomJackpotService {
    getPhantomJackpots(brandId: number,
                       currency: string,
                       playerId: string,
                       gameCode: string,
                       customerId: string): Promise<PhantomJackpot[]>;
}

export class PhantomService extends BaseHttpService implements PhantomJackpotService {
    private static PHANTOM_JACKPOT_API_URL = "features/jackpots";

    constructor(private phantomUrl: string) {
        super(phantomUrl);
    }

    public async getPhantomJackpots(brandId: number,
                                    currency: string,
                                    playerId: string,
                                    gameCode: string,
                                    customerId: string): Promise<PhantomJackpot[]> {
        const response: PhantomJackpotResponse =
            await this.get<PhantomJackpotResponse>(PhantomService.PHANTOM_JACKPOT_API_URL,
                {
                    brandId,
                    currency,
                    playerId,
                    gameCode,
                    customerId: customerId || "skywindgroup"
                });
        return response && response.jackpots ? response.jackpots : [];
    }
}
