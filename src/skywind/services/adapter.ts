import { StartGameService } from "./startGameService";
import { MerchantBrokenGameService } from "./brokenGameService";
import { MerchantBalanceService } from "./balanceService";
import { MerchantInfoService } from "./merchantInfoService";
import { DefaultMerchantPaymentService, MerchantPaymentService } from "./paymentService";
import { MerchantRegulationsService } from "./regulationsService";
import {
    ReportCriticalFilesToMerchantRequest,
    PlayerRegulatoryActionRequest,
    RollbackWithoutTokenRequest,
    BalanceRequestWithoutToken,
    MerchantStartGameTokenData,
    PaymentRequestWithoutToken,
    MerchantGameInitRequest,
    MerchantTransferRequest,
    MerchantGameTokenData,
    MerchantGameTokenInfo,
    MerchantPageResponse,
    MerchantGameURLInfo,
    FinalizeGameRequest,
    MerchantPageRequest,
    FreeBetInfoRequest,
    GameLogoutResponse,
    RollbackBetRequest,
    GameLogoutRequest,
    RefundBetRequest,
    GameLoginRequest,
    PaymentRequest,
    MerchantInfo,
    FreeBetInfo,
    PlayerInfo,
    Balances,
    Balance,
    OfflineBonusPaymentRequest,
    OfflineBonusInfo, LoginTerminalRequest,
    RegisterRoundRequest, RegisterRoundResponse
} from "../..";
import { AdditionalInfo, LoginTerminalResponse } from "../definitions/startGame";

export interface MerchantAdapter<GI extends MerchantGameInitRequest = MerchantGameInitRequest,
    SGT extends MerchantStartGameTokenData = MerchantStartGameTokenData,
    AUTHT extends MerchantGameTokenData = MerchantGameTokenData>
    extends StartGameService<GI, SGT, AUTHT>,
        MerchantBrokenGameService<AUTHT>,
        MerchantBalanceService<AUTHT>,
        MerchantInfoService<AUTHT>,
        MerchantPaymentService<AUTHT>,
        MerchantRegulationsService<AUTHT> {
}

export class MerchantAdapterDecorator<GI extends MerchantGameInitRequest = MerchantGameInitRequest,
    SGT extends MerchantStartGameTokenData = MerchantStartGameTokenData,
    AUTHT extends MerchantGameTokenData = MerchantGameTokenData,
    ADT extends MerchantAdapter<GI, SGT, AUTHT> = MerchantAdapter<GI, SGT, AUTHT>>
    extends DefaultMerchantPaymentService
    implements MerchantAdapter<GI, SGT, AUTHT> {

    constructor(protected readonly adapter: ADT) {
        super();
    }

    public commitBetPayment(merchant: MerchantInfo, authToken: AUTHT, request: PaymentRequest): Promise<Balance> {
        return this.adapter.commitBetPayment(merchant, authToken, request);
    }

    public commitPayment(merchant: MerchantInfo, authToken: AUTHT, request: PaymentRequest): Promise<Balance> {
        return this.adapter.commitPayment(merchant, authToken, request);
    }

    public commitWinPayment(merchant: MerchantInfo, authToken: AUTHT, request: PaymentRequest): Promise<Balance> {
        return this.adapter.commitWinPayment(merchant, authToken, request);
    }

    public commitWinWithoutToken(merchant: MerchantInfo, request: PaymentRequestWithoutToken): Promise<Balance> {
        return this.adapter.commitWinWithoutToken(merchant, request);
    }

    public createGameUrl(merchant: MerchantInfo,
                         gameCode: string,
                         providerCode: string,
                         providerGameCode: string,
                         initRequest: GI): Promise<MerchantGameURLInfo> {
        return this.adapter.createGameUrl(merchant, gameCode, providerCode, providerGameCode, initRequest);
    }

    public getStartGameTokenData(merchant: MerchantInfo,
                                 gameCode: string,
                                 providerCode: string,
                                 providerGameCode: string,
                                 initRequest: GI): Promise<SGT> {
        return this.adapter.getStartGameTokenData(merchant, gameCode, providerCode, providerGameCode, initRequest);
    }

    public getBalances(merchant: MerchantInfo, authToken: AUTHT): Promise<Balances> {
        return this.adapter.getBalances(merchant, authToken);
    }

    public getFreeBetInfo(merchant: MerchantInfo,
                          authToken: AUTHT,
                          freeBetRequest: FreeBetInfoRequest): Promise<FreeBetInfo> {
        return this.adapter.getFreeBetInfo(merchant, authToken, freeBetRequest);
    }

    public getPage(merchant: MerchantInfo, request: MerchantPageRequest): Promise<MerchantPageResponse> {
        return this.adapter.getPage(merchant, request);
    }

    public getBalanceWithoutToken(merchant: MerchantInfo, req: BalanceRequestWithoutToken): Promise<Balance> {
        return this.adapter.getBalanceWithoutToken(merchant, req);
    }

    public getPlayerInfo(merchant: MerchantInfo, authToken: AUTHT): Promise<PlayerInfo> {
        return this.adapter.getPlayerInfo(merchant, authToken);
    }

    public performRegulatoryAction(merchant: MerchantInfo,
                                   gameTokenData: AUTHT,
                                   request: PlayerRegulatoryActionRequest): Promise<any> {
        return this.adapter.performRegulatoryAction(merchant, gameTokenData, request);
    }

    public rollbackBetPayment(merchant: MerchantInfo, authToken: AUTHT, request: RollbackBetRequest): Promise<Balance> {
        return this.adapter.rollbackBetPayment(merchant, authToken, request);
    }

    public async refundBetPayment(merchant: MerchantInfo,
                                  authToken: AUTHT, request: RefundBetRequest): Promise<Balance> {
        return this.adapter.refundBetPayment(merchant, authToken, request);
    }

    public rollbackBetPaymentWithoutToken(merchant: MerchantInfo,
                                          request: RollbackWithoutTokenRequest): Promise<Balance> {
        return this.adapter.rollbackBetPaymentWithoutToken(merchant, request);
    }
    public registerRound(merchant: MerchantInfo,
                         request: RegisterRoundRequest): Promise<RegisterRoundResponse> {
        return this.adapter.registerRound(merchant, request);
    }
    public transfer(merchant: MerchantInfo, authToken: AUTHT, request: MerchantTransferRequest): Promise<Balance> {
        return this.adapter.transfer(merchant, authToken, request);
    }

    public getGameTokenInfo(merchant: MerchantInfo,
                            startToken: SGT,
                            currency: string,
                            transferEnabled: boolean,
                            additionalInfo?: AdditionalInfo): Promise<MerchantGameTokenInfo<AUTHT>> {
        return this.adapter.getGameTokenInfo(merchant, startToken, currency, transferEnabled, additionalInfo);
    }

    public async keepAlive(merchant: MerchantInfo,
                           gameTokenData: AUTHT): Promise<void> {
        return this.adapter.keepAlive(merchant, gameTokenData);
    }

    public async loginGame(merchant: MerchantInfo,
                           gameTokenData: AUTHT,
                           request: GameLoginRequest): Promise<void> {
        return this.adapter.loginGame(merchant, gameTokenData, request);
    }

    public async logoutGame(merchant: MerchantInfo,
                            gameTokenData: AUTHT,
                            request: GameLogoutRequest): Promise<GameLogoutResponse> {
        return this.adapter.logoutGame(merchant, gameTokenData, request);
    }

    public async finalizeGame(merchant: MerchantInfo,
                              gameTokenData: AUTHT,
                              request: FinalizeGameRequest): Promise<Balance | void> {
        return this.adapter.finalizeGame(merchant, gameTokenData, request);
    }

    public async reportCriticalFiles(merchant: MerchantInfo,
                                     req: ReportCriticalFilesToMerchantRequest): Promise<void> {
        return this.adapter.reportCriticalFiles(merchant, req);
    }

    public async commitBonusPayment(merchant: MerchantInfo,
                                    authToken: AUTHT, request: PaymentRequest): Promise<Balance> {
        return this.adapter.commitBonusPayment(merchant, authToken, request);
    }

    public commitOfflineBonusPayment(merchant: MerchantInfo,
                                     request: OfflineBonusPaymentRequest): Promise<OfflineBonusInfo> {
        return this.adapter.commitOfflineBonusPayment(merchant, request);
    }

    public loginTerminalPlayer(merchant: MerchantInfo,
                               initRequest: LoginTerminalRequest): Promise<LoginTerminalResponse<SGT>> {
        return this.adapter.loginTerminalPlayer(merchant, initRequest);
    }
}
