import { FreeBetInfo, FreeBetInfoRequest } from "../definitions/freeBet";
import { PlayerInfo } from "../definitions/player";
import { MerchantGameTokenData } from "../definitions/startGame";
import { BaseHttpService } from "./baseHTTPService";
import { MerchantInfo } from "../definitions/common";
import { MerchantPageRequest, MerchantPageResponse } from "../..";
import { MERCHANT_REGULATION } from "../..";

export interface MerchantInfoService <AUTHT extends MerchantGameTokenData = MerchantGameTokenData> {
    getPlayerInfo(merchant: MerchantInfo, gameToken: AUTHT): Promise<PlayerInfo>;
    getFreeBetInfo(merchant: MerchantInfo, gameToken: AUTHT, freeBetRequest: FreeBetInfoRequest): Promise<FreeBetInfo>;
    getPage(merchantInfo: MerchantInfo, pageRequest: MerchantPageRequest): Promise<MerchantPageResponse>;
}

export abstract class DefaultMerchantInfoService <AUTHT extends MerchantGameTokenData = MerchantGameTokenData>
    implements MerchantInfoService <AUTHT> {

    public abstract getPlayerInfo(merchant: MerchantInfo, gameToken: AUTHT): Promise<PlayerInfo>;

    public abstract getFreeBetInfo(merchant: MerchantInfo, gameToken: AUTHT,
                                   freeBetRequest: FreeBetInfoRequest): Promise<FreeBetInfo>;

    public abstract getPage(merchantInfo: MerchantInfo,
                            pageRequest: MerchantPageRequest): Promise<MerchantPageResponse>;
}

export class HTTPMerchantInfoService <AUTHT extends MerchantGameTokenData = MerchantGameTokenData>
    extends BaseHttpService implements MerchantInfoService <AUTHT> {

    constructor(adapterUrl: string, merchantInfo: MerchantInfo, regulation?: MERCHANT_REGULATION) {
        super(adapterUrl, { merchantInfo, regulation });
    }

    public async getPlayerInfo(merchant: MerchantInfo, gameToken: AUTHT): Promise<PlayerInfo> {
        return this.post<PlayerInfo>("get-player", { gameToken });
    }

    public async getFreeBetInfo(merchant: MerchantInfo,
                                gameToken: AUTHT,
                                freeBetRequest: FreeBetInfoRequest): Promise<FreeBetInfo> {
        return this.post<FreeBetInfo>("get-freebet", { gameToken, freeBetRequest });
    }

    public async getPage(merchantInfo: MerchantInfo, pageRequest: MerchantPageRequest): Promise<MerchantPageResponse> {
        return this.post<MerchantPageResponse>("get-page", { request: pageRequest });
    }
}
