import { BaseHttpService } from "./baseHTTPService";
import { HTTPOptions } from "../definitions/common";
import { generateInternalToken } from "../utils/token";
import { PlayerPromotionInfo, PromoInfo } from "../definitions/promo";
import {
    SWGameCriticalFilesRequest,
    SWPlatformCriticalFilesRequest,
    ModulesListWithHashes,
    GamesListWithHashes
} from "../definitions/criticalFiles";
import { SWGameInfo } from "../definitions/game";
import { RoundImageResponse, SpinImageResponse } from "../definitions/history";
import { SmResultExtraData } from "../definitions/payment";

export interface GameQueryParams {
    shortInfo?: boolean; // without limits
    code?: string;
    addAggregatedFinalLimits?: boolean;
    skipJurisdictionFiltering?: boolean;
    currency?: string;
    currencies?: string[];
    appendJackpots?: boolean;

    [field: string]: any;
}

export interface PromotionQueryParams {
    externalId?: string;
    includePlayers?: boolean;
    includeGames?: boolean;
}

export interface HistoryQueryParams {
    ttl?: number;
    language?: string;
}

export interface HistoryDetailsQueryParams {
    language?: string;
    timezone?: string;
    gameCode?: string;
}

export interface ForceFinishRequest {
    roundId: string | number;
    playerCode?: string;
    gameProviderCode?: string;
}

export interface RoundsQueryParams {
    roundId?: string;
    roundId__in?: string; // csv array
    includeSpins?: boolean;
    includeSmResultExtraData?: boolean;
}

export interface JackpotHistory {
    totalJpContribution?: number;
    totalJpWin?: number;
}

export interface RoundHistory extends JackpotHistory {
    roundId: string;
    gameId?: string;
    brandId: number;
    playerCode: string;
    device?: string;
    gameCode: string;
    currency: string;
    isTest: boolean;
    bet: number;
    win: number;
    revenue?: number;
    totalEvents?: number;
    balanceBefore?: number;
    balanceAfter?: number;
    broken?: boolean;
    firstTs?: Date;
    ts?: Date;
    finished?: boolean;
    internalRoundId?: string;
    credit?: number;
    debit?: number;
    ctrl?: number;
    extraData?: any;
    operator_site_id?: number;
}

export interface SpinDetails {
    roundId: number;
    spinNumber: number;
    device: string;
    type: string;
    currency: string;
    bet: number;
    win: number;
    balanceBefore: number;
    balanceAfter: number;
    endOfRound: boolean;
    ts: string;
    test: boolean;
    isPayment: boolean;
    details: string;
    gameId: string;
    gameVersion: string;
    gameCode?: string;
    totalJpContribution?: number;
    totalJpWin?: number;
    credit?: number;
    debit?: number;
    insertedAt?: string;
    playerCode?: string;
    trxId?: string;
    extraData?: any;
}

export type RoundHistoryExtended = RoundHistory
    & { spins?: SpinDetails[], smResult?: string, smResultExtraData?: SmResultExtraData };

export interface EntityInfo {
    id?: number | string;
    type: string;
    name: string;
    description?: string;

    status: string;

    key: string;

    defaultCurrency?: string;
    defaultCountry?: string;
    defaultLanguage?: string;

    countries?: Array<string>;
    currencies?: Array<string>;
    languages?: Array<string>;

    domains?: string[];
    parentDomains?: string[];

    child?: Array<EntityInfo>;
    path?: string;
    dynamicDomainId?: number;
    staticDomainId?: number;
    environment?: string;

    isTest?: boolean;
    isMerchant?: boolean;
    title?: string;
    merchantTypes?: string[];
    staticDomainTags?: string[];
    deploymentGroup?: any;
}

export interface MerchantInfo {
    brandId: number;
    type: string;
    code: string;
    params: any;
    isTest?: boolean;
    proxy?: any;
    proxyId?: number;
    lastTestsPassing?: Date;
    brandTitle?: string;
}

export interface MerchantEntityInfo extends EntityInfo {
    merchant: MerchantInfo;
    path: string;
}

interface EntityShortInfo {
    id: string;
    child?: Array<EntityShortInfo>;
    path?: string;
    name?: string;
    title?: string;
    type?: string;
    decryptedBrand?: number;
}

export interface HistoryService {

    getRoundImage(code: string, type: string, roundId: string,
                  params?: HistoryQueryParams): Promise<RoundImageResponse>;

    getSpinImage?(code: string, type: string, roundId: string, spinNumber: number,
                  params?: HistoryDetailsQueryParams): Promise<SpinImageResponse>;

    getMerchantGameRoundDetails(code: string, type: string, roundId: string): Promise<RoundHistoryExtended>;

    getMerchantGameRounds(code: string, type: string, params: RoundsQueryParams): Promise<RoundHistoryExtended[]>;

    forceFinish(code: string, type: string, body: ForceFinishRequest): Promise<void>;
}

export interface GameService {
    getGames(code: string, type: string, params?: GameQueryParams): Promise<SWGameInfo[]>;

    getGamesInfo(gameCode: string, code: string,
                 type: string | string[], params?: GameQueryParams): Promise<SWGameInfo>;
}

export interface MerchantService {
    getMerchantEntityInfo(code: string, type: string): Promise<MerchantEntityInfo>;

    searchMerchants(partialCode: string): Promise<MerchantEntityInfo[]>;

    getShortStructure(code: string, type: string): Promise<EntityShortInfo>;

    getMerchantEntitySettings(code: string, type: string): Promise<any>;
}

export interface PromoService {

    createPromo(code: string, type: string, promo: PromoInfo): Promise<PromoInfo>;

    upsertPromo(code: string, type: string, promo: PromoInfo): Promise<PromoInfo>;

    addPromoToPlayer(code: string, type: string, promoId: string, playerCode: string): Promise<PlayerPromotionInfo>;

    getPromotions(code: string, type: string, params?: PromotionQueryParams): Promise<PromoInfo[]>;

    getPlayerPromotions(code: string, type: string, playerCode: string): Promise<PlayerPromotionInfo[]>;

    removePromoFromPlayer(code: string, type: string,
                          promoId: string, playerCode: string): Promise<PlayerPromotionInfo>;

    removePromoFromPlayers(code: string, type: string, promo: PromoInfo): Promise<PromoInfo>;

    removePromo(code: string, type: string, promoId: string): Promise<PromoInfo>;
}

export interface CriticalFilesService {

    getGameCriticalFilesInfo(code: string, type: string,
                             body: SWGameCriticalFilesRequest): Promise<GamesListWithHashes>;

    getPlatformCriticalFilesInfo(code: string, type: string,
                                 body: SWPlatformCriticalFilesRequest): Promise<ModulesListWithHashes>;
}

export class InternalAPIService extends BaseHttpService implements MerchantService, GameService, PromoService,
    HistoryService, CriticalFilesService {

    public static MERCHANT_API_URL = `v1/merchants`;

    private static buildMerchantAPIURL(code: string, types: string | string[]): string {
        const typeList = Array.isArray(types) ? types.join(",") : types;
        return `${InternalAPIService.MERCHANT_API_URL}/${typeList}/${code}`;
    }

    protected async getOptions(): Promise<HTTPOptions> {
        return {
            auth: {
                internalToken: await generateInternalToken({})
            }
        };
    }

    public async getGames(code: string, type: string | string[], params: GameQueryParams = {}): Promise<SWGameInfo[]> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.get<SWGameInfo[]>(`${url}/games`, params, options);
    }

    public async getGamesInfo(gameCode: string, code: string,
                              type: string | string[], params: GameQueryParams = {}): Promise<SWGameInfo> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        if (params.currencies && Array.isArray(params.currencies)) {
            params.currency = params.currencies.join(",");
            delete params.currencies;
        }
        return this.get<SWGameInfo>(`${url}/games/${gameCode}/info`, params, options);
    }

    public async searchMerchants(partialCode: string): Promise<MerchantEntityInfo[]> {
        const params = { code: partialCode };
        const options = await this.getOptions();
        return this.get<MerchantEntityInfo[]>(`${InternalAPIService.MERCHANT_API_URL}/search`, params, options);
    }

    public async getShortStructure(code: string, type: string): Promise<EntityShortInfo> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.get<EntityShortInfo>(`${url}/short-structure`, {}, options);
    }

    public async createPromo(code: string, type: string, promo: PromoInfo): Promise<PromoInfo> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.post<PromoInfo>(`${url}/promo`, promo, options);
    }

    public async upsertPromo(code: string, type: string, promo: PromoInfo): Promise<PromoInfo> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.put<PromoInfo>(`${url}/promo`, promo, options);
    }

    public async addPromoToPlayer(code: string, type: string,
                                  promoId: string, playerCode: string): Promise<PlayerPromotionInfo> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.put<PlayerPromotionInfo>(`${url}/promo/${promoId}/players/${playerCode}`, {}, options);
    }

    public async getPromotions(code: string, type: string, params: PromotionQueryParams = {}): Promise<PromoInfo[]> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.get<PromoInfo[]>(`${url}/promo`, params, options);
    }

    public async getPlayerPromotions(
        code: string,
        type: string,
        playerCode: string,
        skipEGPPromotions?: boolean
    ): Promise<PlayerPromotionInfo[]> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.get<PlayerPromotionInfo[]>(`${url}/players/${playerCode}/promo`, { skipEGPPromotions }, options);
    }

    public async removePromoFromPlayer(code: string,
                                       type: string,
                                       promoId: string,
                                       playerCode: string): Promise<PlayerPromotionInfo> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.delete<PlayerPromotionInfo>(`${url}/promo/${promoId}/players/${playerCode}`, {}, options);
    }

    public async removePromoFromPlayers(code: string, type: string, promo: PromoInfo): Promise<PromoInfo> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.delete<PromoInfo>(`${url}/promo`, promo, options);
    }

    public async removePromo(code: string, type: string, promoId: string): Promise<PromoInfo> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.delete<PromoInfo>(`${url}/promo/${promoId}`, {}, options);
    }

    public async getRoundImage(code: string,
                               type: string,
                               roundId: string,
                               params?: HistoryQueryParams): Promise<RoundImageResponse> {
        const options = await this.getOptions();
        const data = {
            merchantCode: code,
            merchantType: type,
            roundId,
            ttl: params.ttl,
            language: params.language
        };

        return this.post<RoundImageResponse>(`${InternalAPIService.MERCHANT_API_URL}/history/image`, data, options);
    }

    public async getSpinImage(code: string, type: string, roundId: string, spinNumber: number,
                              params?: HistoryDetailsQueryParams): Promise<SpinImageResponse> {
        const options = await this.getOptions();
        const data = {
            merchantCode: code,
            merchantType: type,
            roundId,
            spinNumber,
            timezone: params.timezone,
            gameCode: params.gameCode,
            language: params.language
        };

        return this.post<SpinImageResponse>(`${InternalAPIService.MERCHANT_API_URL}/history/details/image`,
            data,
            options);
    }

    public async getMerchantEntityInfo(code: string, type: string): Promise<MerchantEntityInfo> {
        const options = await this.getOptions();
        const url = `v1/merchantentities/${type}/${code}`;
        return this.get<MerchantEntityInfo>(url, {}, options);
    }

    public async getMerchantEntitySettings(code: string, type: string): Promise<any> {
        const options = await this.getOptions();
        const url = `v1/merchantentities/${type}/${code}/settings/`;
        return this.get(url, {}, options);
    }

    public async getMerchantGameRoundDetails(code: string,
                                             type: string,
                                             roundId: string): Promise<RoundHistoryExtended> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.get<RoundHistoryExtended>(`${url}/history/rounds/${roundId}`, {}, options);
    }

    public async getMerchantGameRounds(code: string,
                                       type: string,
                                       params: RoundsQueryParams): Promise<RoundHistoryExtended[]> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.get<RoundHistoryExtended[]>(`${url}/history/rounds`, params, options);
    }

    public async getGameCriticalFilesInfo(code: string, type: string,
                                          body: SWGameCriticalFilesRequest): Promise<GamesListWithHashes> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.post<GamesListWithHashes>(`${url}/critical-files/games/info`, body, options);
    }

    public async getPlatformCriticalFilesInfo(code: string, type: string,
                                              body: SWPlatformCriticalFilesRequest): Promise<ModulesListWithHashes> {
        const options = await this.getOptions();
        const url = InternalAPIService.buildMerchantAPIURL(code, type);
        return this.post<ModulesListWithHashes>(`${url}/critical-files/platform/info`, body, options);
    }

    public async forceFinish(code: string, type: string, body: ForceFinishRequest): Promise<void> {
        const options = await this.getOptions();
        const payload = {
            merchantCode: code,
            merchantType: type,
            ...body
        };
        return this.post<void>(`${InternalAPIService.MERCHANT_API_URL}/force-finish`, payload, options);
    }
}
