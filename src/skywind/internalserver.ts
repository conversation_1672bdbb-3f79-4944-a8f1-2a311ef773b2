import { create } from "./fastify";
import type * as http from "http";
import logger from "./logger";
import measuresApi from "./api/measures";

const log = logger();

export function start(port: number) {
    const app = create();
    app.register(measuresApi);

    return new Promise<http.Server>((resolve, reject) => {
        app.listen({ port, host: "::" }, (err) => {
            if (err) {
                return reject(err);
            }
            resolve(app.server);
            log.info("Currency Exchange Internal API listening on " + port);
        });
    });
}
