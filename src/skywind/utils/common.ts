import config from "../config";
import { EventEmitter } from "events";
import { XmlService } from "../services/xmlService";

export function getSecuredObjectData(dataObject: object = {}, secureKeys: string[] = config.logParams.secureKeys) {
    let securedData = {};
    if (!dataObject.hasOwnProperty) {
        return dataObject;
    }
    if (dataObject instanceof EventEmitter) {
        return securedData;
    }

    if (Array.isArray(dataObject)) {
        securedData = [];
    }

    for (const key in dataObject) {
        if (!dataObject.hasOwnProperty(key)) {
            continue;
        }
        const value = dataObject[key];
        if (value !== null && typeof value === "object") {
            securedData[key] = getSecuredObjectData(value, secureKeys);
        } else {
            if (isSecurityProperty(key, secureKeys) && shouldReplaceValue(value)) {
                securedData[key] = "***";
            } else {
                securedData[key] = value;
            }
        }
    }
    return securedData;
}

function shouldReplaceValue(value: boolean | number | string) {
    // boolean values will be replaced
    // undefined, null, empty string, NaN will not be replaced
    return typeof value === "boolean" || !!value;
}

const isSecurityProperty = (key: string, secureKeys: string[]) => {
    return secureKeys.some((secureKey) => secureKey === key);
};

const defaultXmlService = new XmlService();

export function getSecuredXmlData(
    xml: string,
    secureKeys?: string[],
    xmlService: XmlService = defaultXmlService
) {
    if (!xml) {
        return xml;
    }
    const objectData = xmlService.convertToObject(xml);
    const securedObjectData = getSecuredObjectData(objectData, secureKeys);
    return xmlService.convertToXML(securedObjectData);
}
