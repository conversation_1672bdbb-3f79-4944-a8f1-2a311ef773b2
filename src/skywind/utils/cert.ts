import { MerchantCertSettings } from "../definitions/common";
import { AdapterCertConfig } from "../definitions/configOptions";

function getEnvValue(init: string): string {
    return init ? init
        .replace(/\\n/g, "\n")
        .replace(/"/g, "") : init;
}

function getUseCert(merchantCode: string): boolean {
    return process.env[`${merchantCode.toUpperCase()}_USE_CERT`] === "true";
}

function getCert(merchantCode: string): string {
    return getEnvValue(process.env[`${merchantCode.toUpperCase()}_CERT`]);
}

function getCertKey(merchantCode: string): string {
    return getEnvValue(process.env[`${merchantCode.toUpperCase()}_CERT_KEY`]);
}

function getCertPassword(merchantCode: string): string {
    return process.env[`${merchantCode.toUpperCase()}_CERT_PASSWORD`];
}

function getCA(merchantCode: string): string {
    return getEnvValue(process.env[`${merchantCode.toUpperCase()}_CA`]);
}

export function getCertConfig(code: string, configCert: AdapterCertConfig): MerchantCertSettings {
    return {
        useCert: getUseCert(code) || configCert.useCert,
        settings: {
            cert: getCert(code) || getEnvValue(configCert.cert),
            key: getCertKey(code) || getEnvValue(configCert.key),
            password: getCertPassword(code) || configCert.password,
            ca: getCA(code) || getEnvValue(configCert.ca)
        }
    };
}
