import { InterruptSocket } from "../errors";
import { PaymentRequest } from "../definitions/payment";
import { GameTokenData } from "../definitions/startGame";

/**
 * @param request
 * @param error
 * @param gameToken
 * @private
 * Interrupt the socket for live game to prevent any other errors and popups
 * caused with retries or something else.
 */
export const interruptSocketForLiveGame = (
    request: PaymentRequest,
    error,
    gameToken: GameTokenData
): void => {
    if (!request.offlineRetry && gameToken.isLiveGame) {
        throw new InterruptSocket(error.message);
    }
};
