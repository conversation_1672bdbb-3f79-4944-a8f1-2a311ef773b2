import { getCertConfig } from "./cert";
import { MerchantInfo, HTTPOptions } from "../definitions/common";
import { AdapterConfig } from "../definitions/configOptions";

export function getOptions(merchant: MerchantInfo, config: AdapterConfig, onlySSL= false): HTTPOptions {
    const options: HTTPOptions = {};

    if (!onlySSL) {
        options.proxy = merchant?.proxy?.url || config.proxy;
        options.timeout = config.timeout;
    }

    const cert = getCertConfig(merchant.code, config.cert);

    if (cert.useCert) {
        options.ssl = cert.settings;
    }

    return options;
}
