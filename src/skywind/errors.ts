import { ErrorResponse, ExtraData } from "./definitions/common";
import { errors } from "@skywind-group/sw-utils";

export enum ERROR_LEVEL {
    ERROR,
    WARN,
    INFO,
    DEBUG,
}

export enum ErrorSpecialFlag {
    GAME_MODULE_ERROR = "game_module_error",
    HACK_ATTEMPT_ERROR = "hack_attempt_error"
}

interface ErrorData {
    [field: string]: string | string[];
}

/**
 * Intended for cases when we need to use merchant's error message - ensure that no html injection can reach client
 * @param stringToEscape
 */
export function escapeSomeHtmlChars(stringToEscape: string): string {
    if (stringToEscape && typeof stringToEscape === "string") {
        return stringToEscape.replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("&", "&amp;")
            .replace("\"", "&quot;")
            .replace("'", "&#39;");
    }
    return stringToEscape;
}

export class SWError extends Error {
    public responseStatus: number;
    public code: number;
    public errorLevel: number;
    public data: ErrorData;
    public extraData: ExtraData;
    public translateMessage: boolean;
    public providerDetails: object;
    public specialFlag: ErrorSpecialFlag;
    public forwardToWrapper?: boolean;

    constructor(status: number,
                code: number,
                message: string,
                level: number = ERROR_LEVEL.WARN,
                extraData?: ExtraData,
                data: ErrorData = {}) {
        super(message);
        this.responseStatus = status;
        this.code = code;
        this.errorLevel = level;
        this.extraData = extraData;
        this.translateMessage = true;
        this.data = data;
    }

    public static isSWError(err) {
        return err.code && err.message;
    }

    public getErrorLevel() {
        switch (+this.errorLevel) {
            case ERROR_LEVEL.ERROR:
                return "error";
            case ERROR_LEVEL.WARN:
                return "warn";
            case ERROR_LEVEL.INFO:
                return "info";
            case ERROR_LEVEL.DEBUG:
                return "debug";
            default:
                return "warn";
        }
    }

    /**
     * Marks the error so that it's message will be sent as is without translation
     */
    public dontTranslate(): SWError {
        this.translateMessage = false;
        return this;
    }

    public setExtraData(extra: ExtraData): SWError {
        this.extraData = extra;
        return this;
    }

    public setProviderDetails(details: object): SWError {
        this.providerDetails = details;
        return this;
    }

    public setSpecialFlag(specialFlag: ErrorSpecialFlag): SWError {
        this.specialFlag = specialFlag;
        return this;
    }

    public setForwardToWrapper(forwardToWrapper: boolean): void {
        this.forwardToWrapper = forwardToWrapper;
    }

    /**
     * Method to help return some additional data along with regular code and message fields of error response object.
     * Override this method to add error-specific data to error response json.
     */
    public decorateResponseWithData(errorResponse: ErrorResponse): ErrorResponse {
        return errorResponse;
    }
}

export class MerchantAdapterAPIError extends SWError {
    constructor(merchantAdapterUrl: string, err?: SWError, statusCode?: number) {
        const status = statusCode || 400;
        const code = err && err.code || 2000;
        const message = err && err.message || "Adapter API respond with error";
        super(status, code, message);
    }
}

export class MerchantAdapterAPITransientError extends SWError {
    constructor(merchantAdapterUrl: string, err?: SWError, statusCode?: number) {
        const status = statusCode || 500;
        const code = err && err.code || 2001;
        const message = err && err.message || "Adapter API transient error";
        super(status, code, message, ERROR_LEVEL.ERROR);
    }
}

export class ConnectionError extends SWError {
    constructor(merchantAdapterUrl: string, err?: SWError, statusCode?: number) {
        const status = statusCode || 500;
        const code = err && err.code || 2002;
        const message = err && err.message || "Connection error";
        super(status, code, message, ERROR_LEVEL.ERROR);
    }
}

export class RequireRefundBetError extends SWError {
    constructor(message = "Require to refund bet") {
        super(400, 800, message);
    }
}

export class CannotCompletePayment extends SWError {
    constructor(message = "Cannot complete payment!") {
        super(500, 806, message);
    }
}

export class InterruptSocket extends SWError {
    constructor(message= "Interrupt socket!") {
        super(500, 850, message);
    }
}

export class InsufficientFreebet extends SWError {
    constructor() {
        super(400, 685, "Insufficient free bets balance");
    }
}

export class InvalidFreebet extends SWError {
    constructor() {
        super(400, 686, "Invalid free bet");
    }
}

export class AnotherGameInProgress extends SWError {
    constructor() {
        super(400, 835, "Another game is currently in progress");
    }
}

export class ValidationError extends SWError {
    constructor(message: string) {
        super(400, 40, `Validation error: ${message}`, errors.ERROR_LEVEL.WARN);
        this.data.messages = message;
    }
}

export class XmlParseError extends ValidationError {
    constructor(xmlData) {
        super("Invalid xml data");
        this.data.xmlData = xmlData;
    }
}

export class ConvertToXmlError extends ValidationError {
    constructor(data) {
        super("Invalid object data. Can't be converted to xml");
        this.data.xmlObjectData = data;
    }
}
