export namespace errors {
    export enum ERROR_LEVEL {
        ERROR,
        WARN,
        INFO,
        DEBUG,
    }

    interface ErrorData {
        [field: string]: string | string[];
    }

    export class SWBaseError extends Error {
        public data: ErrorData = {};
        public translateMessage?: boolean;

        constructor(public responseStatus: number,
                    public code: number,
                    message: string,
                    public level: number = ERROR_LEVEL.WARN,
                    public extraData?: any,
                    public external = false,
                    public providerDetails?: object) {
            super(message);
        }

        public getErrorLevel() {
            switch (+this.level) {
                case ERROR_LEVEL.ERROR:
                    return "error";
                case ERROR_LEVEL.WARN:
                    return "warn";
                case ERROR_LEVEL.INFO:
                    return "info";
                case ERROR_LEVEL.DEBUG:
                    return "debug";
                default:
                    return "warn";
            }
        }

        public dontTranslate(): SWBaseError {
            this.translateMessage = false;
            return this;
        }

        public setProviderDetails(details: object): SWBaseError {
            this.providerDetails = details;
            return this;
        }

        public static isSWError(err): err is SWBaseError {
            return err.code && err.message;
        }
    }
}
