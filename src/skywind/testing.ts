import { cloneDeep } from "lodash";
import { lazy } from "./lazy";

const superAgentMocker = lazy(() => require("superagent-mocker"));

export namespace testing {

    export type RequestMockHandler = (req: { qs?: any, body?: any, url: string }) =>
        { status: number, body?: any, headers?: any };

    export interface RequestMockHandlerOnCall extends RequestMockHandler {
        onCall(handler: RequestMockHandler): RequestMockHandlerOnCall;
    }

    export interface RequestMock {
        post(path: RegExp | string, handler: RequestMockHandler);

        put(path: RegExp | string, handler: RequestMockHandler);

        delete(path: RegExp | string, handler: RequestMockHandler);

        patch(path: RegExp | string, handler: RequestMockHandler);

        get(path: RegExp | string, handler: RequestMockHandler);

        clearRoutes(): void;

        clearRoute(method: string, url): void;

        unmock(agent: any): void;

        readonly args: any[];
    }

    export function status(statusCode: number, body?: any): RequestMockHandler {
        return () => {
            return {
                status: statusCode,
                body,
            };
        };
    }

    export function status200(body?: any): RequestMockHandler {
        return status(200, body);
    }

    export function status500(body?: any): RequestMockHandler {
        return status(500, body);
    }

    export function status400(body?: any): RequestMockHandler {
        return status(400, body);
    }

    /*
    Return response endless times
     */
    export function onCall(handler: RequestMockHandler): RequestMockHandlerOnCall {
        const handlers: RequestMockHandler[] = [
            handler,
        ];
        let count = 0;

        const result: RequestMockHandlerOnCall = function(req) {
            const response = handlers[count](req);

            if (handlers.length - 1 !== count) {
                count++;
            }

            return response;
        } as RequestMockHandlerOnCall;

        result.onCall = function(h: RequestMockHandler) {
            handlers.push(h);
            return result;
        };
        return result;
    }

    export function requestMock(superAgent): RequestMock {
        if (process.env.NODE_ENV === "production") {
            throw new Error("You cannot mock superagent in production environment!");
        }

        const result = superAgentMocker.get()(superAgent);
        result.delete = result.del;
        const methods = ["post", "get", "put", "del", "patch", "delete"];
        for (const methodName of methods) {
            const method = result[methodName];
            if (typeof method !== "function") {
                continue;
            }
            result[methodName] = function(path, func: (req) => any) {
                const wrapper = function(req) {
                    if (!result.args) {
                        result.args = [];
                    }
                    const data = cloneDeep(req);
                    if (data.params) {
                        delete data.params;
                    }
                    data.method = methodName.toUpperCase();
                    result.args.push(data);
                    return func.apply(this, arguments);
                };
                arguments[1] = wrapper;
                return method.apply(this, arguments);
            };
            result[methodName].method = method;
        }

        const clearRoutes = result.clearRoutes;
        result.clearRoutes = function() {
            clearRoutes.apply(this, arguments);
            delete result.args;
        };

        const unmock = result.unmock;
        result.unmock = function(agent) {
            for (const methodName of methods) {
                result[methodName] = result[methodName].method;
            }
            result.unmock = unmock;
            result.clearRoutes = clearRoutes;
            delete result.args;
            unmock.call(this, agent || superAgent);
        };

        return result;
    }
}
