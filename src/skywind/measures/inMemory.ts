import { measures } from "./measures";
import { AbstractMeasureProvider } from "./provider";
import { Readable } from "stream";

import * as Measured from "measured-core";

class InMemoryMeasureProvider extends AbstractMeasureProvider<any, any, any> {

    public async getMeasuresStream(): Promise<Readable> {
        const provider = this;
        const measures = Object.keys(this.getMeasures());
        let index = 0;
        return new Readable({
            highWaterMark: 1024,
            async read() {
                if (index === 0) {
                    this.push("{");
                }
                if (index < measures.length) {
                    const name = measures[index];
                    const buffer = index > 0 ? "," : "";
                    this.push(buffer.concat(JSON.stringify({ name, data: await provider.getMeasure(name) })));
                    index++;
                } else {
                    this.push("}");
                    this.push(null);
                }
            },
        });
    }

    protected createProviderGauge(name: string): any {
        return new Measured.Counter();
    }

    protected incrementProviderGauge(counter: any, value: number, details?: string) {
        counter.inc(value);
    }

    protected setProviderGauge(counter: any, value: number, details?: string) {
        counter.reset(value);
    }

    protected createGauge(info: measures.MeasureInfo): any {
        return new Measured.Timer();
    }

    protected startTimer(gauge: any): any {
        return gauge.start();
    }

    protected endTimer(gauge: any, timer: any) {
        timer.end();
    }
    // tslint:disable-next-line:no-empty
    protected setUpExpressMiddleware(instance) {
    }

    // tslint:disable-next-line:no-empty
    protected setUpFastifyMiddleware(instance) {
    }
}

export default new InMemoryMeasureProvider();
