import "reflect-metadata";
import config from "../config";
import { EventEmitter } from "events";

export namespace measures {

    export enum Metrics {
        WEBSOCKET_CONNECTION_COUNT = "websocket_connection_count",
        WEBSOCKET_REQUESTS_DURATION = "websocket_request_duration_seconds_sum",
        WEBSOCKET_REQUESTS_COUNT = "websocket_request_duration_seconds_count"
    }

    export interface MeasureInfo {
        /**
         * Measure name
         */
        name: string;
        /**
         * Should marked method be process asynchronously
         * This flag incluences on the way how we invoke measured method and waite the result to be complete.
         */
        isAsync?: boolean;
        /**
         * Is this metrics used only in debug mode.
         * Any debugMode metric has no influenced on production mode. Their calculation are eliminated.
         */
        debugOnly?: boolean;
    }

    const providers = {
        memory: () => {
            return require("./inMemory").default;
        },
        prometheus: () => {
            return require("./prometheus").default;
        },
    };

    export const providerName = config.measures.provider || "memory";

    process.stdout.write(`Using ${providerName} provider for measuring performance\n`);
    // eslint-disable-next-line prefer-const
    export let measureProvider = providers[providerName]();

    /**
     * Decorator to mark method to be measured
     *
     */
    export function measure(info: MeasureInfo) {
        return measureProvider.getMeasureDecorator(info);
    }

    export const MetadataLogParamKey = "measureParam";

    export type MeasurableFunction<T> = (...args) => T | EventEmitter;

    export function measureParam() {
        return function(target: Object, propertyKey: string | symbol, parameterIndex: number) {
            const existingParameters: number[] = Reflect.getOwnMetadata(MetadataLogParamKey, target, propertyKey) || [];
            existingParameters.push(parameterIndex);
            Reflect.defineMetadata(MetadataLogParamKey, existingParameters, target, propertyKey);
        };
    }

    /**
     *  Get measures
     */
    export async function getMeasures(): Promise<any> {
        return measureProvider.getMeasures();
    }

    /**
     *  Get measure by name
     */
    export function getMeasure(name: string): Promise<any> {
        return measureProvider.getMeasure(name);
    }

    export interface Measurable {
        getMeasureKey(): string;
    }
}
