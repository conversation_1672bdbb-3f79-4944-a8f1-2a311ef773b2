import { Lazy } from "../../../resources";
import { lazy } from "../lazy";
import { randomBytes, randomUUID, UUID } from "node:crypto";
import config from "../config";

const lazyCrc = lazy(() => require("crc"));

const node: Lazy<number[]> = lazy(() => {
    const hostName = process.env["HOSTNAME"];
    if (!hostName) {
        return [...randomBytes(6)];
    } else {
        const crc = lazyCrc.get();
        const part1 = crc.crc32(hostName);
        const part2 = crc.crc16(hostName);
        const buff = Buffer.alloc(6);
        buff.writeUInt16BE(part2, 0);
        buff.writeUInt32BE(part1, 2);
        return [...buff];
    }
});

export function generateTraceId(): UUID {
    if (!config.measures.useOldVersionOfGeneratingTraceId) {
        return randomUUID();
    }
    const { v1 } = require("uuid");
    return v1({ node: node.get() });
}
