import { measures } from "../measures";
import MeasurableFunction = measures.MeasurableFunction;
import { type TransactionRunner } from "./definition";

export class DisabledTransactionRunner implements TransactionRunner {

    public runInTransaction<T>(action: (...args) => T): T {
        return action();
    }

    public trackTransaction<T>(action: MeasurableFunction<T>): MeasurableFunction<T> {
        return action;
    }

    public runOutOfTransaction<T>(action: (...args) => T): T {
        return action();
    }

    public setContextVariable<T>(name: string, value: T, transferable?: boolean): boolean {
        return false;
    }

    public getContextVariable<T>(name: string, transferable?: boolean): T {
        return undefined;
    }

    public getContext(transferable?: boolean): any {
        return undefined;
    }

    public instrumentModule(name: string, module: any): any {
        return undefined;
    }

    public getTransferableContextVariable<T>(name: string): T {
        return undefined;
    }

    public setTransferableContextVariable<T>(name: string, value: T): boolean {
        return false;
    }
}
