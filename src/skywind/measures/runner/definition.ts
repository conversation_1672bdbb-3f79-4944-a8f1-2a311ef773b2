import { measures } from "../measures";
import MeasurableFunction = measures.MeasurableFunction;

export interface TransactionRunner {
    runInTransaction<T>(action: (...args) => T): T;

    runOutOfTransaction<T>(action: (...args) => T): T;

    trackTransaction<T>(action: MeasurableFunction<T>): MeasurableFunction<T>;

    setContextVariable<T>(name: string, value: T, transferable?: boolean): boolean;

    getContextVariable<T>(name: string, transferable?: boolean);

    getContext(transferable?: boolean): any;

    instrumentModule(name: string, module: any): any;
}
