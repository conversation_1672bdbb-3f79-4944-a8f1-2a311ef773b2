import config from "../../config";
import { ClsHookedTransactionRunner } from "./clsHookedRunner";
import { DisabledTransactionRunner } from "./disabledRunner";
import { AsyncStorageTransactionRunner } from "./asyncStorageRunner";
import { type TransactionRunner } from "./definition";

export function createTransactionRunner(): TransactionRunner {
    if (config.measures.trackTransaction) {
        const checkNode = (nodeVersion: string, expectedVersion: number) => {
            const version = nodeVersion.split(".");
            return +(version?.[0]) < expectedVersion;
        };
        return (process && checkNode(process.versions.node, 12)) ?
            new ClsHookedTransactionRunner() :
            new AsyncStorageTransactionRunner();
    }

    return new DisabledTransactionRunner();
}
