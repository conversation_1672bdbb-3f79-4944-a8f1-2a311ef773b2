import { EventEmitter } from "events";
import config from "../../config";
import { measures } from "../measures";
import { TRANSFERABLE } from "./common";
import MeasurableFunction = measures.MeasurableFunction;
import { type TransactionRunner } from "./definition";

/**
 * @deprecated this was necessary for nodejs < 12
 */
export class ClsHookedTransactionRunner implements TransactionRunner {
    private readonly namespace;

    constructor() {
        const hook = require("cls-hooked");
        this.namespace = hook.getNamespace("SW-TRX") || hook.createNamespace("SW-TRX");
        this.setUpLeakProtection();
    }

    public runInTransaction<T>(action: (...args) => T): T {
        return this.namespace.runAndReturn(action);
    }

    public trackTransaction<T>(action: MeasurableFunction<T>): MeasurableFunction<T> {
        if (action instanceof EventEmitter) {
            this.namespace.bindEmitter(action as EventEmitter);
            return action;
        } else {
            return this.namespace.bind(action) as any;
        }
    }

    public runOutOfTransaction<T>(action: (...args) => T): T {
        return action();
    }

    public setContextVariable<T>(name: string, value: T, transferable: boolean = false): boolean {
        const ctx = !transferable ? this.namespace.active : this.getTransferabe();
        if (ctx) {
            ctx[name] = value;
            return true;
        } else {
            return false;
        }
    }

    public getContextVariable<T>(name: string, transferable: boolean = false): T {
        const ctx = !transferable ? this.namespace.active : this.getTransferabe();
        if (ctx) {
            return ctx[name];
        }
    }

    public getContext(transferable: boolean = false): any {
        return !transferable ? this.namespace.active : this.getTransferabe();
    }

    private getTransferabe(): any {
        let result;
        if (this.namespace.active) {
            result = this.namespace.active[TRANSFERABLE];
            if (!result) {
                result = this.namespace.active[TRANSFERABLE] = {};
            }
        }

        return result;
    }

    private setUpLeakProtection() {
        setInterval(async () => {
            const now = Date.now();
            const threshold = now + config.measures.leakProtectionInterval;
            const it = this.namespace._contexts.entries();
            // cleanup in batches
            while (await this.cleanContexts(it, now, threshold)) {
                // empty line
            }
        }, config.measures.leakProtectionInterval / 2);
    }

    private async cleanContexts(it: IterableIterator<[string, any]>,
                                now: number,
                                threshold: number) {
        let count = config.measures.leakCleanupBatchSize;
        while (count > 0) {
            const { done, value } = it.next();
            if (done) {
                return false;
            }
            const [key, context] = value;
            if (context.ts === undefined) {
                // mark if no date;
                context.ts = now;
            } else if (context.ts < threshold) {
                this.namespace._contexts.delete(key);
            }
            count--;
        }

        return true;
    }

    public instrumentModule(name: string, module: any): boolean {
        if (name === "bluebird") {
            const clsBluebird = require("cls-bluebird");
            clsBluebird(this.namespace, module);
            return module;
        }

        return undefined;
    }
}
