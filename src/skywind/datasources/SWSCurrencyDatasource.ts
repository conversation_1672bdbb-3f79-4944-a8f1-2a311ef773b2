import type { CurrencyDatasource, ProviderRates } from "../providers/provider";
import { Provider } from "../providers/provider";
import type { CurrencyProvider } from "@skywind-group/sw-currency-exchange";
import { ExchangeRateType, getCurrencyProvider } from "@skywind-group/sw-currency-exchange";

export class SWSCurrencyDatasource implements CurrencyDatasource {
    private readonly provider: CurrencyProvider;

    constructor() {
        this.provider = getCurrencyProvider();
    }

    public async load(date: Date, baseCurrencies: string[]): Promise<ProviderRates> {
        const askRates = await this.provider.getRates(date, baseCurrencies, ExchangeRateType.ASK);
        const bidRates = await this.provider.getRates(date, baseCurrencies, ExchangeRateType.BID);
        return {
            provider: Provider.SWS,
            ts: date,
            askRates: askRates.rates,
            bidRates: bidRates.rates
        };
    }
}
