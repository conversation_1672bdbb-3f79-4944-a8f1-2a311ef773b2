import type { CurrencyDatasource, ProviderRates } from "../providers/provider";
import { Provider } from "../providers/provider";
import { getTimestamp, logRequest } from "../providers/utils";
import config from "../config";
import logger from "../logger";
import { OANDAServerError } from "../errors";

const log = logger();

interface OANDASuccessResponse {
    quotes: OANDAQuote[];
}

interface OANDAErrorResponse {
    message: string;
    code: number;
}

interface OANDAQuote {
    base_currency: string;
    quote_currency: string;
    average_bid: string;
    average_ask: string;
}

export class OANDACurrencyDatasource implements CurrencyDatasource {

    public async load(date: Date, baseCurrencies: string[]): Promise<ProviderRates> {
        const url = new URL(config.oanda.url);
        url.searchParams.set("api_key", config.oanda.apiKey);
        url.searchParams.set("base", baseCurrencies.join(","));
        url.searchParams.set("date_time", getTimestamp(date));
        url.searchParams.set("fields", "averages");

        logRequest("GET", url.href, log, ["api_key"]);
        const res = await fetch(url);
        if (!res.ok) {
            if ([400, 401, 403, 404].includes(res.status)) {
                const error: OANDAErrorResponse = await res.json();
                throw new OANDAServerError(error.code, error.message);
            }
            const text = await res.text();
            throw new Error(`OANDA internal error: ${text}, status=${res.status}`);
        }
        const body = await res.json();
        return this.parseResponse(date, body);
    }

    private parseResponse(date: Date, data: OANDASuccessResponse): ProviderRates {
        const result: ProviderRates = {
            provider: Provider.OANDA,
            ts: date,
            bidRates: {},
            askRates: {}
        };
        for (const rate of data.quotes) {
            const base = rate.base_currency;
            const target = rate.quote_currency;
            const bidRate = +rate.average_bid;
            const askRate = +rate.average_ask;

            if (!result.bidRates[base]) {
                result.bidRates[base] = {};
            }
            if (!result.askRates[base]) {
                result.askRates[base] = {};
            }
            if (!result.bidRates[target]) {
                result.bidRates[target] = {};
            }
            if (!result.askRates[target]) {
                result.askRates[target] = {};
            }

            if (bidRate) {
                result.bidRates[base][target] = bidRate;
                // calculate symmetric rate for backward conversion
                if (!result.askRates[target][base]) {
                    result.askRates[target][base] = +((1 / bidRate).toFixed(5));
                }
            }

            if (askRate) {
                result.askRates[base][target] = askRate;
                // calculate symmetric rate for backward conversion
                if (!result.bidRates[target][base]) {
                    result.bidRates[target][base] = +((1 / askRate).toFixed(5));
                }
            }
            if (!askRate || !bidRate) {
                log.warn(rate, "Zero rate from oanda is ignored");
            }
        }
        return result;
    }
}
