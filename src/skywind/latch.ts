import { clearTimeout, setTimeout } from "timers";

type ResolveHandler = (result: boolean) => void;

export class Latch {
    public awaitingQueue: Map<ResolveHandler, NodeJS.Timeout> = new Map();

    public async awaitFor(ms: number): Promise<boolean> {
        return new Promise<boolean>((resolve) => {
            const timer = setTimeout(() => {
                this.awaitingQueue.delete(resolve);
                resolve(false);
            }, ms);
            this.awaitingQueue.set(resolve, timer);
        });
    }

    public notify() {
        this.notifyHandler(this.awaitingQueue.keys().next().value);
    }

    public notifyAll() {
        for (const handler of this.awaitingQueue.keys()) {
            this.notify<PERSON><PERSON><PERSON>(handler);
        }
    }

    public get waitingCount(): number {
        return this.awaitingQueue.size;
    }

    private notifyHandler(handler: ResolveHandler) {
        if (handler) {
            const timer = this.awaitingQueue.get(handler);
            if (timer) {
                clearTimeout(timer);
                this.awaitingQueue.delete(handler);
                handler(true);
            }
        }
    }
}
