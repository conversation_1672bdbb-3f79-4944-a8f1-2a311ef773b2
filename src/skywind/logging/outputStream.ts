import { Transform, Writable } from "stream";
import { sleep } from "../time";
import type { LoggingStream, MessageMapper, StreamFactory, StreamOutputConfig } from "./definitions";

/**
 *  Logging output stream.
 *  It transforms data from by using mapper and ouputs to the underlying stream
 */
export class LoggingOutput extends Transform {
    private stream: LoggingStream;

    constructor(private readonly streamFactory: StreamFactory,
                private readonly outputConfig: StreamOutputConfig,
                private readonly messageMapper: MessageMapper) {
        super({ objectMode: true, highWaterMark: outputConfig.highWaterMark });
        this.initStream();
    }

    public initStream() {
        if (this.stream) {
            if (this.stream.close) {
                this.stream.close((err) => {
                    if (err) {
                        console.error(err);
                    }
                });
            }
            this.stream.destroy();
        }
        this.stream = this.streamFactory(this.outputConfig);
        this.pipe(this.stream);
        this.stream.on("error", async (error) => {
            console.error(error);
            this.unpipe(this.stream);
            await sleep(this.outputConfig.recreateStreamTimeout);
            this.initStream();
        });
    }

    public _transform(chunk: any,
                      encoding: BufferEncoding,
                      cb?: (error?: Error | null | undefined,
                            msg?: { topic: string, messages: any }) => void) {
            let rows = this.messageMapper.map(chunk);
            if (!Array.isArray(rows)) {
                rows = [rows];
            }
            for (const row of rows) {
                this.push(row, encoding);
            }
            cb();
    }
}
