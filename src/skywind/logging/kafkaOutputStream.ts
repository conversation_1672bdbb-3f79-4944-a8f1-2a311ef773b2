import { LoggingOutput } from "./outputStream";
import { GelfMessageMapper } from "./gelfMapper";
import type { KafkaOutputConfig, LogRecord, MessageMapper } from "./definitions";
import { lazy } from "../lazy";
import type * as kafkaNode from "kafka-node";

const lazyKafka = lazy<typeof kafkaNode>(() => require("kafka-node"));

/**
 *  Create kafka producer stream
 * @param outputConfig
 */
export function createStream(outputConfig: KafkaOutputConfig): any /*Writable*/ {
    return new (lazyKafka.get()).ProducerStream({
        highWaterMark: outputConfig.highWaterMark,
        kafkaClient: {
            kafkaHost: outputConfig.kafkaHost,
            autoConnect: true,
            connectRetryOptions: { forever: true, minTimeout: 100, maxTimeout: 10000 } as any,
            requestTimeout: outputConfig.requestTimeout,
            connectTimeout: outputConfig.connectionTimeout,
        },
        producer: {
            requireAcks: outputConfig.requireAcks,
            ackTimeoutMs: outputConfig.ackTimeoutMs,
            partitionerType: outputConfig.partitionerType,
        },
    });
}

/**
 * The record that should finally go to the kafka producer stream
 */
export interface KafkaLogRecord {
    topic: string;
    messages: any;
}

/**
 * Kafka output record, to map message to specific topic to send
 */
export class KafkaMessageMapper implements MessageMapper<KafkaLogRecord[]> {
    constructor(private readonly config: KafkaOutputConfig,
                private readonly origin: MessageMapper) {
    }

    public map(log: LogRecord): KafkaLogRecord[] {
        const message = JSON.stringify(this.origin.map(log));
        let records = [{ topic: this.config.topic, messages: message }];
        if (this.config.filterFacility && (log.name || "").includes(this.config.filterFacility)) {
            if (this.config.isReplaceMainTopicEnabled) {
                records = [{ topic: this.config.additionalTopic, messages: message }];
            } else {
                records.push({ topic: this.config.additionalTopic, messages: message });
            }
        }
        return records;
    }
}

export function createKafkaOutput(config: KafkaOutputConfig) {
    return new LoggingOutput(createStream, config, new KafkaMessageMapper(config, new GelfMessageMapper()));
}
