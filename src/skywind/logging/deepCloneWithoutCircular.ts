export function deepCloneWithoutCircular<T = any>(obj: T): T {
    const seen = new WeakSet();

    function clone(value: any): any {
        if (value === null || value === undefined || typeof value !== "object") {
            return value;
        }
        if (seen.has(value)) {
            return "[Circular]";
        }
        seen.add(value);
        if (value instanceof Date || value instanceof RegExp || value instanceof Error) {
            return value;
        }
        if (Array.isArray(value)) {
            return value.map(item => clone(item));
        }
        const result: any = {};
        for (const key in value) {
            if (Object.prototype.hasOwnProperty.call(value, key)) {
                result[key] = clone(value[key]);
            }
        }
        return result;
    }
    return clone(obj);
}
