import { <PERSON><PERSON><PERSON>, Producer, KafkaConfig, RetryOptions, ProducerRecord } from "kafkajs";
import { Writable } from "node:stream";
import type { KafkaOutputConfig, LogRecord, MessageMapper } from "./definitions";
import { LoggingOutput } from "./outputStream";
import { GelfMessageMapper } from "./gelfMapper";

interface KafkaWritableOptions {
    brokers: string[];
    topic?: string;
    highWaterMark?: number;
    requestTimeout?: number;
    connectionTimeout?: number;
    ackTimeoutMs?: number;
    requireAcks?: number;
    retry?: RetryOptions;
}

export class KafkaJsWritableStream extends Writable {
    private kafka: Kafka;
    private producer: Producer;
    private connected: boolean = false;

    constructor(options: KafkaWritableOptions) {
        super({
            objectMode: true,
            decodeStrings: false,
            highWaterMark: options.highWaterMark || 1024,
        });

        const kafkaConfig: KafkaConfig = {
            brokers: options.brokers,
            connectionTimeout: options.connectionTimeout,
            requestTimeout: options.requestTimeout,
            retry: options.retry,
        };

        this.kafka = new Kafka(kafkaConfig);
        this.producer = this.kafka.producer({
            transactionTimeout: options.ackTimeoutMs,
        });
    }

    async _construct(clb: (error?: Error | null) => void): Promise<void> {
        try {
            await this.producer.connect();
            this.connected = true;
            clb();
        } catch (error) {
            clb(error as Error);
        }
    }

    async _write(
        message: ProducerRecord,
        encoding: BufferEncoding,
        clb: (error?: Error | null) => void,
    ): Promise<void> {
        try {
            await this.producer.send(message);

            clb();
        } catch (error) {
            clb(error as Error);
        }
    }

    async _destroy(error: Error | null, clb: (error?: Error | null) => void): Promise<void> {
        try {
            if (this.connected) {
                await this.producer.disconnect();
                this.connected = false;
            }
            clb(error);
        } catch (disconnectError) {
            clb(disconnectError as Error);
        }
    }
}

/**
 * Kafka output record, to map message to specific topic to send
 */
export class KafkaJsMessageMapper implements MessageMapper<ProducerRecord[]> {
    constructor(private readonly config: KafkaOutputConfig,
                private readonly origin: MessageMapper) {
    }

    public map(log: LogRecord): ProducerRecord[] {
        const message = JSON.stringify(this.origin.map(log));
        let records: ProducerRecord[] = [
            {
                acks: this.config.requireAcks,
                topic: this.config.topic,
                messages: [
                    {
                        value: message,
                    },
                ],
            },
        ];
        if (this.config.filterFacility && (log.name || "").includes(this.config.filterFacility)) {
            if (this.config.isReplaceMainTopicEnabled) {
                records = [
                    {
                        acks: this.config.requireAcks,
                        topic: this.config.additionalTopic,
                        messages: [
                            {
                                value: message,
                            },
                        ],
                    },
                ];
            } else {
                records.push(
                    {
                        acks: this.config.requireAcks,
                        topic: this.config.additionalTopic,
                        messages: [
                            {
                                value: message,
                            },
                        ],
                    },
                );
            }
        }
        return records;
    }
}

/**
 *  Create kafka producer stream
 * @param outputConfig
 */
function createStream(outputConfig: KafkaOutputConfig): Writable {
    return new KafkaJsWritableStream({
        highWaterMark: outputConfig.highWaterMark,
        brokers: outputConfig.kafkaHost.split(","),
        requestTimeout: outputConfig.requestTimeout,
        connectionTimeout: outputConfig.connectionTimeout,
        retry: {
            retries: Number.MAX_SAFE_INTEGER,
            initialRetryTime: 100,
            maxRetryTime: 10000,
        },
        requireAcks: outputConfig.requireAcks,
        ackTimeoutMs: outputConfig.ackTimeoutMs,
    });
}

export function createKafkaJsonOutput(config: KafkaOutputConfig) {
    return new LoggingOutput(createStream, config, new KafkaJsMessageMapper(config, new GelfMessageMapper()));
}
