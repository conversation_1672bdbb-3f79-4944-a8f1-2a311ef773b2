import { Writable } from "stream";

export interface OutputConfig {
    type: "console" | "graylog" | "kafka" | "kafkajs";
    logLevel: string;
}

export interface GrayLogOutputConfig extends OutputConfig {
    type: "graylog";
    host: string;
    port: number;
    filterFacility?: string;
}

export interface StreamOutputConfig extends OutputConfig {
    highWaterMark?: number;
    recreateStreamTimeout?: number;
}

export interface KafkaOutputConfig extends StreamOutputConfig {
    kafkaHost?: string;
    requestTimeout?: number;
    connectionTimeout?: number;
    requireAcks?: number;
    ackTimeoutMs?: number;
    partitionerType?: number;
    topic?: string;
    type: "kafka";
    isReplaceMainTopicEnabled?: boolean;
    filterFacility?: string;
    additionalTopic?: string;
}

export interface KafkaJsOutputConfig extends Omit<KafkaOutputConfig, "type"> {
    type: "kafkajs";
}

/**
 * The structure that bole emits to the outputStream
 */
export interface LogRecord {
    name: string;
    hostname: string;
    time: number;
    message: any;
    level: string;
    err?: Error;
}

/**
 * Utility interface to map the output record to the actual representation that must be written to the outputstream
 */
export interface MessageMapper<T = any> {
    map(log: LogRecord): T;
}

export type LoggingStream = Writable & { close?(cb: (err?: Error) => void) };

/**
 * Logging stream factory
 */
export type StreamFactory = (config: OutputConfig) => Writable;
