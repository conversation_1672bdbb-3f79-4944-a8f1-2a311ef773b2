import { promisify } from "util";

/**
 * Using util.promisify is the recommended way of dealing with some situations in which the current store is lost
 * when working with AsyncLocalStorage. See the `troubleshooting: Context loss section` of the API documentation:
 * https://nodejs.org/api/async_context.html#class-asynclocalstorage
 */
export const sleep = promisify(setTimeout);

/**
 *  Parse string representation of Date in iso8601 format and return Date object, throws specified error on fail
 *
 * @param {string} iso8601Date
 * @param {() => any} errorSupplier
 * @returns {Date}
 *
 * @throws ValidationError
 */
export function parseDateOrThrow(iso8601Date: string, errorSupplier: () => any = () => "Invalid date format"): Date {
    const ts = Date.parse(iso8601Date);
    if (Number.isNaN(ts)) {
        throw errorSupplier() || "Invalid date format";
    }
    return new Date(ts);
}
