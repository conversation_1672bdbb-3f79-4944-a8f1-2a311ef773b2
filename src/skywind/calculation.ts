export namespace calculation {
    export function normalizeAmountByPrecision(precision: number, value: number) {

        if (!Number.isFinite(value)) {
            return value;
        }

        if (!precision) {
            return value;
        }

        return +(value.toFixed(precision));
    }

    export function safeAddWithPrecision(precision: number, a: number, b: number) {

        if (!Number.isFinite(a)) {
            return normalizeAmountByPrecision(precision, b);
        }

        if (!Number.isFinite(b)) {
            return normalizeAmountByPrecision(precision, a);
        }

        if (!precision) {
            return a + b;
        }

        const normalizedA = normalizeAmountByPrecision(precision, a);
        const normalizedB = normalizeAmountByPrecision(precision, b);

        return normalizeAmountByPrecision(precision, normalizedA + normalizedB);
    }
}
