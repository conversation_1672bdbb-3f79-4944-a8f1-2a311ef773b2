import config from "./config";

const Hashids = require("hashids");

export namespace publicId {
    export interface Options {
        password: string;
        minLength: number;
    }

    export class PCID {
        private hashId: any;

        constructor(public readonly options: Options) {
            this.hashId = new Hashids(options.password, options.minLength);
        }

        public encode(value: number | string) {
            return this.hashId.encode(+value);
        }

        public decode(value: number | string) {
            const decodedId = this.hashId.decode(value);
            if (decodedId.length) {
                return decodedId[0];
            }
            return value;
        }
    }

    export const instance: PCID = new PCID({
        password: config.publicId.password,
        minLength: config.publicId.minLength,
    });
}
