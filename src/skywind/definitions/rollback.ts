import { PlayMode } from "./startGame";
import { ITrxId } from "./common";
import { GameStatus, GameType, SpinType } from "./payment";

export interface RollbackRequest {
    originalTransactionId: ITrxId;
    extTransactionId: string;
    roundId: number;
    roundPID?: string;
    roundEnded?: boolean;
    gameStatus?: GameStatus;
    gameType?: GameType;
    spinType?: SpinType;
}

export interface RollbackBetRequest extends RollbackRequest {
    gameToken: string;
    amount?: number;
}

export interface RollbackWithoutTokenRequest extends RollbackRequest {
    brandId: number;
    gameCode: string;
    playmode?: PlayMode;
}
