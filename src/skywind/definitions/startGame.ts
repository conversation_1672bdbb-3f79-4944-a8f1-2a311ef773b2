import { MerchantKeyData } from "./common";
import { Balances } from "./balance";
import { DeferredPaymentMethod } from "@skywind-group/sw-deferred-payment";

export enum PlayMode {
    REAL = "real",
    FUN = "fun",
    BNS = "bns",
    PLAY_MONEY = "play_money",
    FUN_BONUS = "fun_bonus",
}

export interface MerchantGameInitRequest {
    /**
     * Type of merchant integration adapter (IPM, Playthech, etc)
     */
    merchantType: string;

    /**
     *  Merchant identifier. Should be unique for specified integration type
     */
    merchantCode: string;

    /**
     * Game code
     *
     */
    gameCode: string;

    /**
     * Playmode - fun or real
     */
    playmode?: PlayMode;

    /**
     * to be compatible with brand game init request
     */
    playMode?: PlayMode;

    language?: string;

    ticket?: string;
    customerSessionId?: string;

    previousStartTokenData?: MerchantStartGameTokenData;

    lobby?: string;
    cashier?: string;

    ip?: string;

    referrer?: string;

    //  previousGameToken used to switch between games and provide game token of previous game
    previousGameTokenData?: MerchantGameTokenData;

    // italian regulator data for presenting to player and storing in history
    aamsSessionId?: string;
    aamsParticipationCode?: string;
    externalGameId?: string;
    /**
     * if set to true MAPI will return jackpot ids in the "jackpotIds" token field
     * ex: jackpotIds: "jackpot1,jackpot2" isb DK need
     */
    isReturnJackpotIdsEnabled?: boolean;
    isLive?: boolean;
}

export interface AAMSTokenData {
    regulatoryData?: { // regulator data of Italian jurisdiction,
        aamsSessionCode: string; // M4E302013A22D0RG
        participationStartDate?: string;
        ropCode?: string; // id assigned by regulator on buy-in, // N4E34201024737JY
    };
}

export interface BaseGameTokenData {
    playerCode: string;
    gameCode: string;
    brandId: number;
    currency: string;
    test?: boolean;
    envId?: string;
    playmode?: PlayMode;
    deviceId?: string;
    country?: string;
    externalGameId?: string;
    nickname?: string;
    disablePlayerPhantomFeatures?: boolean;
    rci?: number;
    rce?: number;
}

export interface GameTokenData extends BaseGameTokenData {
    transferEnabled?: boolean;
    defaultBalance?: number;
    // Free bet balance should never be returned for this game
    freeBetsDisabled?: boolean;
    isMultibet?: boolean;
    isLiveGame?: boolean;
    forbidOnlineRetries?: boolean;
    balances?: Balances; // Used when startGame if no balance request supported by operator
    bonusPaymentMethod?: DeferredPaymentMethod;
    isPromoInternal?: boolean;
    forwardToWrapper?: boolean; // Tells external games which responses / errors should be forwarded to the wrapper
}

export interface MerchantGameTokenData extends GameTokenData, MerchantKeyData, AAMSTokenData, SegmentInfo {
    isPromoInternal: boolean;
    merchantSessionId?: string;
    ggrCalculation?: GGRCalculationType;
    dynamicMaxTotalBetLimit?: number;
}

export interface MerchantGameTokenInfo<T extends MerchantGameTokenData = MerchantGameTokenData> {
    segmentFilter?: SegmentFilter;
    operatorSiteExternalCode?: string; // responsible for search operator site in available sites
    gameTokenData: T;
}

export interface StartGameTokenData extends BaseGameTokenData {
    providerCode: string;
    providerGameCode: string;
    deployment?: string;
    referrer?: string;
    /**
     * Optional player lobby session Id
     */
    lobbySessionId?: string;
    /**
     * Optional flag to define is lobby external login
     */
    isExternalLogin?: boolean;
    /**
     * Optional parameter to define environment platform, for example falcon_eu
     */
    env?: string;
    /**
     * Optional player's country from operator side
     */
    operatorCountry?: string;
    /**
     * Optional parameter specific for ITG
     */
    licenseeId?: string;
    /**
     * Lobby URL and cashier URL hashes
     */
    lb?: string;
    csh?: string;

    dynamicMaxTotalBetLimit?: number;
}

export interface SegmentInfo {
    segmentId?: number;
}

export interface MerchantStartGameTokenData extends StartGameTokenData, MerchantKeyData, AAMSTokenData {
    language?: string;
    gameGroup?: string;
}

export interface SegmentFilter {
    externalId?: string;
    matchingData?: {
        [field: string]: string;
    };
    externalResellerPath?: number[];
}

export interface MerchantGameURLInfo {
    urlParams: any;
    tokenData: MerchantStartGameTokenData;
}

export interface StartGameURLInfo {
    urlParams: any;
    tokenData: StartGameTokenData;
}

export enum GGRCalculationType {
    WALLET = "wallet",
    ROUND = "round"
}

export interface LoginTerminalRequest {
    [field: string]: any;
}

export interface LoginTerminalResponse<SGT extends MerchantStartGameTokenData = MerchantStartGameTokenData> {
    tokenData: SGT;
    sessionId: string;
}

export interface AdditionalInfo {
    freebets?: FreebetInfo[];
}

export interface FreebetInfo {
    promoId: number;
    externalId?: string;
    coin: number;
}
