import { GameWithHashList, ModuleWithHashList } from "./regulation";

export interface SWGameCriticalFilesRequest {
    regulation: string;         // example: "italian"
    games: string[];            // example: ["sw_ss"]
    includeVersions: boolean;
}

export interface SWPlatformCriticalFilesRequest {
    regulation: string;         // example: "italian"
}

export interface ModulesListWithHashes {
    modules: ModuleWithHashList[];
}

export interface GamesListWithHashes {
    games: GameWithHashList[];
}
