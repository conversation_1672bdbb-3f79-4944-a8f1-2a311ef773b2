export type SpinType = "main" | "freeGame" | "bonus" | "reSpin";

export interface PlayerInfo {
    id?: number;
    code: string;
    status: string;
    firstName?: string;
    lastName?: string;
    nickname?: string;
    email?: string;
    currency: string;
    country: string;
    language: string;
    gameGroup?: string;
    agentId?: number;
    agentTitle?: string;
    agentDomain?: string;
    isTest: boolean;
    lastLogin?: Date;
    lastAction?: Date;
    createdAt?: Date;
    updatedAt?: Date;
    brandId?: string | number;
    comments?: string;
}

export interface MerchantPlayerShortInfo {
    code: string;
    brandId: number;
    currency: string;
    roundEnded?: boolean;
    deviceId?: string;
    extTransactionId?: string;
    spinType?: SpinType;
    gameCode?: string;
    isTest?: boolean;
    playmode?: string;
}
