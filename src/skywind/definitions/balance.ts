import { MerchantPlayerShortInfo } from "./player";
import { ExtraData } from "./common";

export interface Balances {
    [field: string]: Balance;
}

export interface Balance {
    /**
     * The value of the balance
     */
    main: number;
    /**
     * Previous value of the balance, we made change operation
     */
    previousValue?: number;

    /**
     * Free bets amount and coin value.
     */
    freeBets?: FreeBetsBalance;

    extraData?: ExtraData;
}

export interface FreeBetsBalance {
    amount: number;
}

export interface BalanceRequestWithoutToken extends MerchantPlayerShortInfo {
    gameCode?: string;
}
