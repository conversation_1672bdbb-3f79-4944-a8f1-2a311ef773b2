export type PlayerRegulatoryActionAtServer = "resetRealityCheck" | "closeSession" | "cancelBet"
    | "resetSessionLimit" | "customAction" | "setPOPSpainLimits";

export interface PlayerActionServerCallParams {
    [field: string]: any;
    regulation?: string;
}

export interface PlayerRegulatoryActionRequest {
    gameToken: string;
    action: PlayerRegulatoryActionAtServer;
    params?: PlayerActionServerCallParams;
}

export interface CriticalFileWithHash {
    [path: string]: string;
}

export interface GameWithHashList {
    code: string;
    list: CriticalFileWithHash[];
    [field: string]: any; // for any additional info, e.g. aamsCode - required for stars integration
}

export interface ModuleWithHashList {
    name: string;
    list: CriticalFileWithHash[];
    [field: string]: any; // for any additional info
}

export interface ReportCriticalFilesToMerchantRequest {
    games?: GameWithHashList[];
    modules?: ModuleWithHashList[];
}

export enum MERCHANT_REGULATION {
    romanian = "romanian",
    italian = "italian",
    spanish = "spanish",
    swiss = "swiss",
    british = "british"
}
