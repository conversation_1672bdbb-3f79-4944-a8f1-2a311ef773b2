import { PaymentRequest } from "./payment";
export type TransferType = "transfer-in" | "transfer-out";

export interface TransferRequest extends PaymentRequest {
    operation: TransferType;
    amount: number;
}

export interface MerchantTransferRequest extends TransferRequest {
    actualBetAmount?: number;
    actualWinAmount?: number;
    jpWinAmount?: number;
    previousAmount?: number;
    betsCount?: number;
}
