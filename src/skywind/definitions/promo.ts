export enum PROMO_STATUS {
    ACTIVE = "active"
}

export enum PROMO_OWNER {
    OPERATOR = "operator"
}

export enum PROMO_TYPE {
    FREEBET = "freebet",
    BNS = "bonus_coin" // BNS is currently deprecated
}

export enum PROMO_TYPE_OPERATOR {
    FREEBET = "FREE_ROUND"
}

export interface PromoInfo {
    id?: string;
    externalId?: string;
    title: string;
    type?: string;
    owner?: string;
    status: string;
    startDate?: string;
    endDate: string;
    description: string;
    rewards?: RewardInfo[];
    players?: { playerCode: string }[];
    games?: string[];
    overridePreviousPlayerPromo?: boolean;
}

export interface RewardInfo {
    expirationDate: string;
    freebetAmount: number;
    games: FreebetGameConfig[];
}

export interface FreebetGameConfig {
    gameCode: string;
    coins: FreebetGameCoinConfig[];
}

export interface FreebetGameCoinConfig {
    [field: string]: { coin: number };
}

export interface PlayerPromotionInfo {
    promoId: number;
    playerCode?: string;
    status: string;
    expireAt?: Date;
    playedAt?: Date;
    freebets: FreebetsReward[];
    externalId?: string;
}

export interface FreebetsReward {
    currency: string;
    amount: number;
    games: FreebetGameConfig[];
    startDate: Date;
    endDate: Date;
}
