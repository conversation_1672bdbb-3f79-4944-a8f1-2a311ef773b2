import { BrandFinalizationType, JackpotStatistic, PromoInfo, TransactionData } from "./payment";

export enum GameLogoutGameState {
    /**
     * Has unfinished payment (broken payment)
     */
    BROKEN_PAYMENT = "brokenPayment",
    /**
     * round is not finished
     */
    UNFINISHED = "unfinished",

    /**
     *  Last round was successfully finished
     */
    FINISHED = "finished"
}

export enum GameLogoutType {
    /**
     *  logout for all type of games  (finished or not finished)
     */
    ALL = "all",
    /**
     * Logout only for game that expected player action  (to notify about broken games)
     */
    UNFINISHED = "unfinished"
}

export interface GameLogoutRequest {
    roundId: number;
    roundPID: string;
    logoutId: string;
    gameContextId: string;
    gameToken: string;
    state: GameLogoutGameState;
}

export interface GameLoginRequest {
    roundId: number;
    roundPID: string;
    logoutId: string;
    gameContextId: string;
    gameToken: string;
}

export interface GameLogoutResponse {
    requireLogin?: boolean;
}

export type RoundResolveMethod = "revert" | "close";
export type RoundResolveResult = "ok";

export interface MrchRoundResolveRequest {
    roundResolveMethod: RoundResolveMethod;
    roundId: string;
    gameCode?: string;
    playerCode?: string;
    roundPID?: string;
}

export interface MrchRoundResolveResponse {
    result: RoundResolveResult;
}

export interface GameLogoutOptions {
    /**
     * How long to do attempts to finalize payment (in hours)
     */
    maxRetryAttempts?: number;
    /**
     * Which type of games we need to notify about: all, unfinished
     */
    type?: GameLogoutType;

    /**
     * Custom session timeout for the game  (timeout to autodetect that player is not active)
     */
    maxSessionTimeout?: number;
}

export interface RoundStatistics {
    totalBet: number;
    totalWin: number;
    totalEvents: number;
    balanceBefore: number;
    balanceAfter: number;
    broken?: boolean;
    startedAt?: Date;
    finishedAt?: Date;
    totalJpContribution?: number;
    totalJpWin?: number;
    credit?: number;
    debit?: number;
    jpStatistic?: JackpotStatistic;
    // array of smResult strings
    smResults?: string[];
    // single smResult string formed from the smResults array
    smResult?: string;
}

export interface FinalizeGameRequest extends TransactionData, PromoInfo {
    operation: string;
    gameToken: string;
    roundId: string;
    roundPID: string;
    roundStatistics: RoundStatistics;
    deviceId: string;
    eventId: number;
    ts: string;
    finalizationType: BrandFinalizationType;
    closeInSWWalletOnly?: boolean;
}
