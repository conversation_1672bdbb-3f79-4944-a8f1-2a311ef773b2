import { ExtraDataContainer, ITrxId } from "./common";
import { MerchantPlayerShortInfo } from "./player";
import { DeferredPayment } from "@skywind-group/sw-deferred-payment";
import { Balance } from "./balance";
import { HistoryItemResultsData } from "@skywind-group/sw-round-details-report";

export type SpinType = "main" | "freeGame" | "bonus" | "reSpin";
export type GameType = "normal" | "freegame" | "bonusgame";
export type GameStatus = "settled" | "freegame" | "bonusgame";
export type PaymentType = "normal" | "bonus";

export interface JackpotInfo { // added to be passed into ipm adapter. SWS-2393
    isJPWin?: boolean;
    totalJpContribution?: number;
    totalJpWin?: number;
    jackpotDetails?: JackpotDetails;
    jackpotWinDetails?: JackpotWinDetails;
}

export interface JackpotDetails {
    contributionPrecision?: number;
    jackpotTypes: JackpotTypeStatistic;
    jackpots: JackpotStatistic;
}

export interface JackpotValues {
    seed?: number;
    progressive?: number;
}

export interface JackpotPoolDetails {
    contribution: JackpotValues;
    win: number;
    seedWin?: number;
    progressiveWin?: number;
    isLocal?: boolean;
}

export interface JackpotIdDetails {
    [pool: string]: JackpotPoolDetails;
}

export interface JackpotStatistic {
    [jpId: string]: JackpotIdDetails;
}

export interface JackpotTypeStatistic {
    [jpType: string]: string[];
}

export interface JackpotWinDetails {
    [jpId: string]: {
        [pool: string]: number;
    };
}

export interface FreeBetsBalance {
    amount: number;
    // For EGPs activePromoId can be also a string, for example a UUID
    activePromoId?: number | string;
    externalId?: string;
}

export interface PromoInfo {
    promoType?: string;
    promoId?: string;
    freeBetBalance?: FreeBetsBalance;
    freeBetCoin?: number;
    // true only for free spin from free bet
    freeBetMode?: boolean;
    externalId?: string;
}

export interface GameState {
    gameType?: GameType;
    gameStatus?: GameStatus;
    spinType?: SpinType;
    currentScene?: string;
    nextScene?: string;
    roundEnded?: boolean;
}

export interface GameRound extends TransactionData {
    roundId: string;
    roundPID?: string;
    gameSessionId?: string;
}

export interface PaymentRequestWithoutToken extends GameState, PromoInfo, GameRound,
    MerchantPlayerShortInfo, JackpotInfo {
    amount: number;
    deviceId?: string;
    gameCode: string;
    ts: string;
}

export interface PaymentExtraParams {
    nextRoundId?: string;
    nextRoundPID?: string;

    [field: string]: any;
}

export interface PaymentExtraParamsInfo {
    extraParams?: PaymentExtraParams;
}

export interface RTPInfo {
    rtpDeduction?: number;
}

export interface OperatorInfo {
    operatorSiteId?: number;
}

export interface SmResultTransaction {
    transactionId: string;
    type: string;
    ts: string;
    amount: number;
}

export interface SmResultExtraData {
    gameId: string;
    gameTitle: string;
    lines: number;
    transactions: SmResultTransaction[];
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface GameResults {}

export interface RouletteBetDetails {
    type: string;
    pocket: string;
    amount: number;
    win: number;
}

export interface RouletteGameResults extends GameResults {
    outcome: string;
    bets: RouletteBetDetails[];
}

export interface PaymentRequest extends GameState,
    GameRound,
    JackpotInfo,
    PromoInfo,
    EventIdValue,
    ExtraDataContainer, TransactionData, PaymentExtraParamsInfo, RTPInfo, OperatorInfo {

    gameToken: string;
    bet?: number;
    win?: number;
    deviceId?: string;
    retry?: number;
    offlineRetry?: boolean;
    finalizationType?: BrandFinalizationType;
    operation?: string;
    ts: string;
    currency?: string;

    totalBet?: number;
    totalWin?: number;

    grcRedeemInfo?: GRCRedeemInfo;
    smResult?: string;
    smResultExtraData?: SmResultExtraData;
    roundDetails?: HistoryItemResultsData;
    paymentType?: PaymentType;
}

export enum GameFinalizationType {
    // game doesn't support finalization
    NONE = "none",
    // game module has "finalizeGame" method
    FINALIZATION = "finalization",
    // game supports autoplay
    AUTO_PLAY = "autoPlay",
    // game supports finalization - for ITG games only
    ITG_FINALIZATION = "itgFinalization"
}

export enum BrandFinalizationType {
    // game server will not call finalization for this game
    NOT_SUPPORTED = "notSupported",

    // game server finalizes game context without payments and history, game provider saves finalize operation in
    // transaction log
    FORCE_FINISH = "forceFinish",

    // game server finalizes game context using game module, game provider saves each virtual payment in wallet, but
    // without real payments. On finalize transaction game provider sends round statistics to merchant.
    // In case of pending payment it will be virtual payment. Virtual payment is payment without request to merchant
    // adapter it's result is zero balance.
    ROUND_STATISTICS = "roundStatistics",

    // in this case game server is playing game like player with payments and history
    OFFLINE_PAYMENTS = "offlinePayments",

    // game server finalizes game context using game module, game provider saves each virtual payment in wallet, but
    // without real payments. On finalize transaction game provider saves round statistics to db table,
    // data from db will be send to operator offline. In case of pending payment it will be virtual payment.
    // Virtual payment is payment without request to merchant adapter it's result is zero balance.
    MANUAL_PAYMENTS = "manualPayments"
}

export interface EventIdValue {
    eventId?: number;
    totalEventId?: number;
}

export interface TransactionData {
    transactionId: ITrxId;
    extTransactionId?: string;
}

export interface GRCRedeemInfo {
    amount?: number;
    stars?: number;
    vipContribution?: number;
}

export interface DeferredPaymentOperation extends PaymentRequest {
    operation: "deferred_payment";
    cancel?: boolean;
    deferredPayment: DeferredPayment;
}

export interface OfflineBonusPaymentRequest extends TransactionData {
    playerCode: string;
    amount: number;
    currencyCode: string;
    promoId: string;
    promoType: string;
    externalPromoId?: string | number;
    distributionType?: string; // distribution type of offline deferred payment
}

export interface OfflineBonusInfo {
    balance: Balance;
    externalTrxId: string;
}

// added to migrate ITG round SWS-39669
export interface RegisterRoundRequest {
    account_id: string; // External system player id
    game_code: string;
    round_id: string;
    total_bet: number;
    total_win?: number;
    bet_count?: number;
    win_count?: number;
    started_at: number;
}

export interface RegisterRoundResponse extends RegisterRoundRequest {
    cust_id: string; // SW360 player ID
    merch_id: string;
    merch_password: string;
}
