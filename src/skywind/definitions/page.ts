import { MerchantKeyData } from "./common";

export type merchantPageType = "login" | "registration" | "playerinfo" | "password";

export interface MerchantPageRequest extends MerchantKeyData {
    lobbyId: number;
    pageType: merchantPageType;
    language?: string;
    token?: string; // token for get player info page
}

export interface MerchantPageResponse {
    url: string;
    size?: number;
}
