import { GameLogoutOptions } from "./brokenGame";

export interface MerchantKeyData {
    merchantType: string;
    merchantCode: string;
}

export enum PAYMENT_TYPE {
    WIN = "win",
    BET = "bet",
    TRANSFER = "transfer",
    ROLLBACK = "rollback",
    BONUS = "bonus"
}

export enum WALLET_TYPE {
    UPDATE_WIN_ON_ROUND_FINISHED, // merchant send cashed balance in response on commit win payment
    DEFAULT, // balance is updated on commit payment
    UPDATE_WIN_ON_ROUND_FINISHED_WITH_INTERMEDIATE_PHASES // the same as UPDATE_WIN_ON_ROUND_FINISHED, but with intermediate steps like the initial bet
}

export enum PLATFORM_TYPE {
    MOBILE = "mobile",
    WEB = "web"
}

export interface ExtraDataContainer {
    extraData?: ExtraData;
}

export interface ExtraData {
    [field: string]: any;
}

export interface MerchantInfo {
    type: string;
    code: string;
    params: MerchantParams;
    isTest?: boolean;
    proxy?: Proxy;
    proxyId?: number;
    lastTestsPassing?: Date;
    brandId: number;
    brandTitle?: string;
}

export interface Proxy {
    url: string;
    id?: number;
    description?: string;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface MerchantParams {
    supportTransfer?: boolean;
    keepAliveSec?: number;
    supportForceFinishAndRevert?: boolean;
    forceFinishAndRevertInSWWalletOnly?: boolean;
    isPromoInternal?: boolean;
    proxyUrl?: string;
    adapterUrl?: string;
    serverUrl?: string;
    password?: string;
    username?: string;
    sameUrlForTerminalLoginAndTicket?: boolean;
    gameLogoutOptions?: GameLogoutOptions;
    walletType?: WALLET_TYPE;
    [field: string]: any;

    /** true for merchants under Italy jurisdiction.
     * Means we have to get AAMS Participant Code from merchant on game start
     */
    isUnderAAMSRegulation?: boolean;
    gsId?: string;
    gpId?: string;

    retryPolicy?: {
        delay?: number;
        maxAttempts?: number;
        cancelMaxAttempts?: number;
    };
    certSettings?: MerchantCertSettings;
    refundBetInsteadOfRetry?: boolean;
    apiTimeoutMsec?: number;
    isPasswordBonusAuth?: boolean;

    // if true will add 'distributionType' to deferred payment request
    // note, this param was supposed to go in bonusApiAdditionalFields - but for backward compatibility its on this lvl
    supportPromoDistributionType?: boolean;
    // define fields that should be send to operator with bonus payment request
    bonusApiAdditionalFields?: MerchantBonusApiAdditionalFields;
}

export interface MerchantCertSettings {
    useCert: boolean;
    settings: {
        cert: any;
        key: any;
        password: any;
        ca?: any;
    };
}

export interface MerchantBonusApiAdditionalFields {
    // Mean that 'operation_ts' should be send with deferred payment request to operator
    supportOperationTs?: boolean;
}

export interface ITrxId {
    /**
     * Internal sequential transaction identifier
     */
    serialId: number;
    /**
     *  Timestamp when identifier as created
     */
    timestamp: number;
    /**
     * Public transaction identifier used to pass to external systems
     */
    publicId: string;
}

export interface SSLData {
    ssl?: {
        cert: string;
        key: string;
        pfx?: string;
        password: string;
        ca?: string;
    };
}

export interface HTTPOptions extends SSLData {
    username?: string;
    password?: string;
    proxy?: string;
    timeout?: number;
    auth?: SWAuthData;
}

export interface SWAuthData {
    provider?: {
        user: string;
        code: string;
        secret: string;
    };
    accessToken?: string;
    internalToken?: string;
}

export interface ErrorResponse {
    code: number;
    message: string;
    extraData?: any;
    subErrors?: ErrorResponse[];
}

export interface BaseUrlIncludeQuestionMarkInfo {
    isBaseUrlIncludeQuestion: boolean;
    baseUrlWithoutQuestionMark: string;
    partWithQuestionMark: string;
}
