/**
 * TypeScript interfaces for OANDA Exchange Rates API v2/rates/candle endpoint
 * 
 * Endpoint: GET /v2/rates/candle.{ext}
 * Description: Retrieves one daily candle for specified base currencies
 * 
 * @see https://exchange-rates-api.oanda.com/#get-/v2/rates/candle.-ext-
 */

/**
 * Individual quote data from OANDA candle response
 * Contains exchange rate information between two currencies
 */
export interface OANDAQuote {
    /** Base currency code (e.g., "USD", "EUR") */
    base_currency: string;
    
    /** Quote currency code (e.g., "EUR", "CNY") */
    quote_currency: string;
    
    /** Average bid rate as string (e.g., "0.861603") */
    average_bid: string;
    
    /** Average ask rate as string (e.g., "0.861704") */
    average_ask: string;
}

/**
 * Successful response (HTTP 200) from OANDA /v2/rates/candle endpoint
 * Contains array of currency exchange rate quotes
 */
export interface OANDACandleSuccessResponse {
    /** Array of currency exchange rate quotes */
    quotes: OANDAQuote[];
}

/**
 * Error response from OANDA /v2/rates/candle endpoint
 * Used for HTTP status codes: 400, 401, 403, 404, and other errors
 */
export interface OANDACandleErrorResponse {
    /** Error code number (e.g., 2 for missing required argument, 8 for invalid token) */
    code: number;
    
    /** Human-readable error message (e.g., "Missing required argument: [base]") */
    message: string;
}

/**
 * Union type for all possible OANDA candle endpoint responses
 */
export type OANDACandleResponse = OANDACandleSuccessResponse | OANDACandleErrorResponse;

/**
 * Request parameters for OANDA /v2/rates/candle endpoint
 */
export interface OANDACandleRequestParams {
    /** API key for authentication */
    api_key: string;
    
    /** Comma-separated list of base currencies (e.g., "USD,EUR") */
    base: string;
    
    /** Date and time in ISO format (e.g., "2024-01-01T00:00:00Z") */
    date_time: string;
    
    /** Fields to include in response (typically "averages") */
    fields?: string;
}

/**
 * Common error codes returned by OANDA API
 */
export enum OANDAErrorCode {
    /** Missing required argument */
    MISSING_REQUIRED_ARGUMENT = 2,
    
    /** Malformed Authorization header or invalid access token */
    INVALID_ACCESS_TOKEN = 8,
    
    /** Rate limit exceeded */
    RATE_LIMIT_EXCEEDED = 429,
    
    /** Internal server error */
    INTERNAL_SERVER_ERROR = 500
}

/**
 * Type guard to check if response is a success response
 */
export function isOANDASuccessResponse(response: OANDACandleResponse): response is OANDACandleSuccessResponse {
    return 'quotes' in response;
}

/**
 * Type guard to check if response is an error response
 */
export function isOANDAErrorResponse(response: OANDACandleResponse): response is OANDACandleErrorResponse {
    return 'code' in response && 'message' in response;
}
