import * as Sequelize from "sequelize";
import { sequelize as db } from "./db";
import type { GameLimitsCurrency } from "@skywind-group/sw-currency-exchange";

const schema = {
    currency: {
        type: Sequelize.DataTypes.STRING,
        primaryKey: true
    },
    version: {
        type: Sequelize.DataTypes.INTEGER,
        primaryKey: true
    },
    toEURMultiplier: {
        field: "to_eur_multiplier",
        type: Sequelize.DataTypes.INTEGER,
    },
    copyLimitsFrom: {
        field: "copy_limits_from",
        type: Sequelize.DataTypes.STRING,
    },
    createdAt: {
        field: "created_at",
        type: Sequelize.DataTypes.DATE,
        defaultValue: Sequelize.DataTypes.NOW
    },
    updatedAt: {
        field: "updated_at",
        type: Sequelize.DataTypes.DATE,
        defaultValue: Sequelize.DataTypes.NOW
    }
};

export interface GameLimitsCurrencyDBInstance extends Sequelize.Model<
    Sequelize.InferAttributes<GameLimitsCurrencyDBInstance>,
    Sequelize.InferCreationAttributes<GameLimitsCurrencyDBInstance>
>, GameLimitsCurrency {
    toInfo(): GameLimitsCurrency;
}

export type GameLimitsCurrencyModel = Sequelize.ModelStatic<GameLimitsCurrencyDBInstance>;

const gameLimitsCurrencyModel: GameLimitsCurrencyModel = db.define<GameLimitsCurrencyDBInstance, GameLimitsCurrency>(
    "game_limits_currencies",
    schema,
    {
        timestamps: false,
        tableName: "game_limits_currencies",
    }
);

export function getGameLimitsCurrencyModel(): GameLimitsCurrencyModel {
    (gameLimitsCurrencyModel as any).prototype.toInfo = function () {
        return {
            currency: this.currency,
            toEURMultiplier: this.toEURMultiplier,
            copyLimitsFrom: this.copyLimitsFrom,
            version: this.version
        };
    };
    return gameLimitsCurrencyModel;
}
