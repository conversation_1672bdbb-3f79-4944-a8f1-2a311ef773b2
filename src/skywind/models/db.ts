import type { Options } from "sequelize";
import { Sequelize, Transaction } from "sequelize";
import config from "../config";
import logger from "../logger";

const log = logger();

const dbOptions: Options = {
    /**
     * The dialect of the database you are connecting to. One of mysql, postgres, sqlite, mariadb and mssql.
     */
    dialect: "postgres",

    /**
     * Default isolation level
     */
    isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,

    /**
     * The dialect specific options
     */
    dialectOptions: {},

    /**
     * The host of the relational database.
     */
    host: config.db.host,

    /**
     * The port of the relational database.
     */
    port: config.db.port,

    /**
     * Connection pool options
     */
    pool: {
        /**
         * Maximum connections of the pool
         */
        max: config.db.maxConnections,

        /**
         * The maximum time, in milliseconds, that a connection can be idle before being released.
         */
        idle: config.db.maxIdleTime,
    },

    /**
     * A function that gets executed everytime <PERSON>quel<PERSON> would log something.
     *
     * Defaults to console.log
     */
    logging: (sql: string) => {
        if (config.queryLogging) {
            log.info(sql);
        }
    },

    define: {
        schema: config.db.schema,
    }
};

if (config.db.ssl.isEnabled) {
    dbOptions.dialectOptions["ssl"] = config.db.ssl;
}

export const sequelize = new Sequelize(config.db.database, config.db.user, config.db.password, dbOptions);
