import type { Transaction } from "sequelize";
import { QueryTypes } from "sequelize";
import { sequelize as db } from "./db";
import { measures } from "@skywind-group/sw-utils";
import measure = measures.measure;
import measureParam = measures.measureParam;

export enum ApplicationLockId {
    UPDATE_RATES = -10
}

export class ApplicationLock {
    @measure({ name: "ApplicationLock.lock", isAsync: true})
    public static async lock(transaction: Transaction, @measureParam() id: ApplicationLockId) {
        return db.query({ query: "select pg_advisory_lock(?)", values: [id] },
            { type: QueryTypes.SELECT, transaction, raw: true });
    }

    @measure({ name: "ApplicationLock.unlock", isAsync: true})
    public static async unlock(transaction: Transaction,  @measureParam() id: ApplicationLockId) {
        return db.query({ query: "select pg_advisory_unlock(?)", values: [id] },
            { type: QueryTypes.SELECT, transaction, raw: true });
    }
}
