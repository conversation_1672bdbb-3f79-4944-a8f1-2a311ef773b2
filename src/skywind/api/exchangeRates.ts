import * as ExchangeRateService from "../services/exchangeRates";
import { verifyToken } from "../token";
import { getDaysCount } from "../providers/utils";
import config from "../config";
import { ValidationError } from "../errors";
import type { FastifyInstanceType } from "../fastify";
import { ExchangeRateType } from "@skywind-group/sw-currency-exchange";

export default function (router: FastifyInstanceType, _options, done) {

    router.get("/rates/:date",
        {
            schema: {
                params: {
                    type: "object",
                    properties: {
                        date: { type: "string", format: "date" },
                    },
                    required: ["date"]
                },
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" },
                        type: { type: "string", enum: [ExchangeRateType.ASK, ExchangeRateType.BID] },
                        from: { type: "string" },
                        to: { type: "string" }
                    },
                    required: ["token"]
                }
            }
        },
        async (req, res) => {
            await verifyToken(req.query.token);
            const date = new Date(req.params.date);
            const rates = await ExchangeRateService.getRates(date, {
                type: req.query.type,
                from: req.query.from,
                to: req.query.to
            });
            return res.send(rates);
        });

    router.get("/rates",
        {
            schema: {
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" },
                        startDate: { type: "string", format: "date" },
                        endDate: { type: "string", format: "date" },
                        type: { type: "string", enum: [ExchangeRateType.ASK, ExchangeRateType.BID] },
                        from: { type: "string" },
                        to: { type: "string" },
                        includeArtificial: { type: "boolean" },
                        hideGGR: { type: "boolean" }
                    },
                    required: ["token", "startDate", "endDate"]
                }
            }
        },
        async (req, res) => {
            await verifyToken(req.query.token);
            const startDate = new Date(req.query.startDate);
            const endDate = new Date(req.query.endDate);
            if (getDaysCount(startDate, endDate) > config.maxDateRange) {
                return Promise.reject(new ValidationError(`Date range must be less then ${config.maxDateRange} days`));
            }
            const rates = await ExchangeRateService.getRatesRange(
                startDate,
                endDate,
                {
                    type: req.query.type,
                    from: req.query.from,
                    to: req.query.to
                },
                req.query.includeArtificial,
                req.query.hideGGR,
            );
            return res.send(rates);
        });

    router.post("/load/rates",
        {
            schema: {
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" },
                        startDate: { type: "string", format: "date" },
                        endDate: { type: "string", format: "date" },
                        baseCurrencies: { type: "string" }
                    },
                    required: ["token", "startDate", "endDate"]
                }
            }
        },
        async (req, res) => {
            await verifyToken(req.query.token);
            const startDate = new Date(req.query.startDate);
            const endDate = new Date(req.query.endDate);
            if (getDaysCount(startDate, endDate) > config.maxDateRange) {
                return Promise.reject(new ValidationError(`Date range must be less then ${config.maxDateRange} days`));
            }
            const baseCurrencies = req.query.baseCurrencies ? req.query.baseCurrencies.split(",") : undefined;
            const rates = await ExchangeRateService.loadRates(startDate, endDate, baseCurrencies);
            return res.send(rates);
        });

    done();
}
