import { measures } from "@skywind-group/sw-utils";
import type { FastifyInstanceType } from "../fastify";

export default function (router: FastifyInstanceType, _options, done) {

    if (measures.providerName === "prometheus") {
        router.get("/metrics", measures.measureProvider.getMeasuresStream);
    } else if (measures.providerName === "memory") {
        router.get<{ Params: { name: string; } }>("/v1/measures/:name", async (req, res) => {
            if (req.params.name === "all") {
                return res.send(measures.measureProvider.getMeasures());
            }
            const measure = await measures.getMeasure(req.params.name);
            if (measure) {
                return res.send(measure);
            }
            return res.status(404).send({});
        });
    }

    done();
}
