import { getGameLimitsCurrencyModel } from "../models/gameLimitsCurrencies";
import { verifyToken } from "../token";
import { ValidationError } from "../errors";
import type { FastifyInstanceType } from "../fastify";

export default function (router: FastifyInstanceType, _options, done) {
    router.get("/game-limits-currencies",
        {
            schema: {
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" }
                    },
                    required: ["token"]
                }
            }
        },
        async (req, res) => {
            await verifyToken(req.query.token);
            const gameLimitsCurrencies = await getGameLimitsCurrencyModel().findAll();

            return gameLimitsCurrencies ?
                res.send(gameLimitsCurrencies.map((gameLimitsCurrency) => gameLimitsCurrency.toInfo())) :
                res.send([]);

        });

    router.get("/game-limits-currencies/:currency/:version",
        {
            schema: {
                params: {
                    type: "object",
                    properties: {
                        currency: { type: "string" },
                        version: { type: "number" },
                    },
                    required: ["currency", "version"]
                },
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" }
                    },
                    required: ["token"]
                }
            }
        },
        async (req, res) => {
            await verifyToken(req.query.token);
            const gameLimitsCurrency = await getGameLimitsCurrencyModel()
                .findOne({ where: { currency: req.params.currency, version: req.params.version } });

            return gameLimitsCurrency ? res.send(gameLimitsCurrency.toInfo()) : res.send({});
        });

    router.patch("/game-limits-currencies/:currency/:version",
        {
            schema: {
                params: {
                    type: "object",
                    properties: {
                        currency: { type: "string" },
                        version: { type: "number" },
                    },
                    required: ["currency", "version"]
                },
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" },
                        toEURMultiplier: { type: "number" },
                        copyLimitsFrom: { type: "string" }
                    },
                    required: ["token"]
                }
            }
        },
        async (req, res) => {
            await verifyToken(req.query.token);

            const currency = req.params.currency;
            const version = req.params.version;

            const toEURMultiplier = req.query.toEURMultiplier || undefined;
            const copyLimitsFrom = req.query.copyLimitsFrom || undefined;

            const [numberOfElements, updatedGameLimitsCurrencies] = await getGameLimitsCurrencyModel()
                .update({ toEURMultiplier, copyLimitsFrom }, { where: { currency, version }, returning: true });

            if (!numberOfElements) {
                return Promise.reject(new ValidationError("Cannot find game limits currency with such version"));
            }

            const updatedGameLimitsCurrency = updatedGameLimitsCurrencies[0];
            return res.send(updatedGameLimitsCurrency.toInfo());
        });

    router.post("/game-limits-currencies",
        {
            schema: {
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" },
                        currency: { type: "string" },
                        version: { type: "number" },
                        toEURMultiplier: { type: "string" },
                        copyLimitsFrom: { type: "string" }
                    },
                    required: ["token", "currency", "version"],
                }
            }
        },
        async (req, res) => {
            await verifyToken(req.query.token);

            const currency = req.query.currency;
            const version = req.query.version;
            const toEURMultiplier = req.query.toEURMultiplier || undefined;
            const copyLimitsFrom = req.query.copyLimitsFrom || undefined;

            const gameLimitsCurrency = await getGameLimitsCurrencyModel().findOne({ where: { currency, version } });
            if (gameLimitsCurrency) {
                return Promise.reject(
                    new ValidationError("Configuration for this currency version is already created")
                );
            }

            const createdGameLimitsCurrency = await getGameLimitsCurrencyModel()
                .create({
                    currency,
                    version,
                    toEURMultiplier: toEURMultiplier ? Number(toEURMultiplier) : undefined,
                    copyLimitsFrom
                });
            return res.status(201).send(createdGameLimitsCurrency.toInfo());
        });

    router.delete("/game-limits-currencies/:currency/:version",
        {
            schema: {
                params: {
                    type: "object",
                    properties: {
                        currency: { type: "string" },
                        version: { type: "number" },
                    },
                    required: ["currency", "version"]
                },
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" }
                    },
                    required: ["token"]
                }
            }
        },
        async (req, res) => {
            await verifyToken(req.query.token);

            const currency = req.params.currency;
            const version = req.params.version;

            await getGameLimitsCurrencyModel().destroy({ where: { currency, version } });
            return res.status(204).send();
        });

    done();
}
