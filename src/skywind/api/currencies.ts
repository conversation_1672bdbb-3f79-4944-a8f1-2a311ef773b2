import { verifyToken } from "../token";
import type { FastifyInstanceType } from "../fastify";
import { Currencies } from "@skywind-group/sw-currency-exchange";

export default function (router: FastifyInstanceType, _options, done) {
    router.get("/currencies",
        {
            schema: {
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" }
                    },
                    required: ["token"]
                }
            }
        },
        async (req, res) => {
            await verifyToken(req.query.token);
            return res.send(Currencies.values());
        });

    router.get("/currencies/:code",
        {
            schema: {
                params: {
                    type: "object",
                    properties: {
                        code: {
                            type: "string",
                            description: "Currency code"
                        }
                    },
                    required: ["code"]
                },
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" }
                    },
                    required: ["token"]
                }
            }
        },
        async (req, res) => {
            await verifyToken(req.query.token);
            const currency = Currencies.value(req.params.code.toUpperCase());
            if (!currency) {
                return res.status(404).send({
                    message: "Currency is not supported"
                });
            }
            return res.send(currency);
        });

    done();
}
