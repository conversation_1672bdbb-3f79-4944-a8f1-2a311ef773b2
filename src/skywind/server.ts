import { create } from "./fastify";
import type * as http from "http";
import * as fs from "fs";
import healthApi from "./api/health";
import versionApi from "./api/version";
import exchangeRatesApi from "./api/exchangeRates";
import currenciesApi from "./api/currencies";
import logger from "./logger";
import config from "./config";
import { getExchangeRateModel } from "./models/exchangeRates";
import { initUpdateJob } from "./services/updateJob";
import gameLimitsCurrenciesApi from "./api/gameLimitsCurrencies";

const log = logger();

const version = fs.readFileSync(__dirname + "/version", "utf8");

export function createApplication() {
    const app = create();

    app.register(versionApi, { prefix: "/v1" });
    app.register(healthApi, { prefix: "/v1" });
    app.register(exchangeRatesApi, { prefix: "/v1" });
    app.register(currenciesApi, { prefix: "/v1" });
    app.register(gameLimitsCurrenciesApi, { prefix: "/v1" });

    return app;
}

export async function start(port: number) {
    if (config.db.syncOnStart) {
        await getExchangeRateModel().sync();
    }

    await initUpdateJob();

    const app = createApplication();

    return new Promise<http.Server>((resolve, reject) => {
        app.listen({ port, host: "::" }, (err) => {
            if (err) {
                return reject(err);
            }
            resolve(app.server);
            log.info(`Currency Exchange API listening on: ${port}`);
            log.info(`Currency Exchange API AppVersion: ${version}`);
        });
    });
}
