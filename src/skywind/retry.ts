import { sleep } from "./time";
import { RetryConfig } from "../../resources";

export async function retry<T>(config: RetryConfig,
                               action: () => Promise<T>,
                               shouldRetry?: (err: Error) => boolean,
                               wrapError: (err: Error) => Error = (err: Error) => err): Promise<T> {

    if (!config.sleep) {
        return action();
    }
    let attemptTimeout = 0;
    let lastError;
    const ts = Date.now();
    let totalSpendTime = 0;
    do {
        //  await for every next attempt except the first
        if (attemptTimeout > 0) {
            await sleep(attemptTimeout);
        }
        try {
            return await action();
        } catch (err) {
            lastError = err;
            if (shouldRetry && !shouldRetry(lastError)) {
                break;
            }
        }

        //  exponential increase or first setup
        attemptTimeout = attemptTimeout > 0 ? attemptTimeout * 2 : config.sleep;
        totalSpendTime = Date.now() - ts;
        // do not exceed the max interval
        if (totalSpendTime >= config.maxTimeout) {
            break;
        } else if (totalSpendTime + attemptTimeout > config.maxTimeout) {
            attemptTimeout = config.maxTimeout - totalSpendTime;
        }
    // eslint-disable-next-line no-constant-condition
    } while (true);

    return Promise.reject(wrapError(lastError));
}
