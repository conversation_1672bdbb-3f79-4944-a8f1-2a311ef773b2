// this should be the first line
import { measures, logging } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import logger from "./logger";
import config, { logConfig } from "./config";

logging.setUpOutput(logConfig() as any);

import * as server from "./server";
import * as internalServer from "./internalserver";

const log = logger();

process.on("uncaughtException", (err) => {
    log.error(err, "uncaughtException occurred. Server continuing to work");
});

process.on("unhandledRejection", (err, promise) => {
    log.error(err, "unhandledRejection", promise);
});

export async function start() {
    try {
        await internalServer.start(config.internalPort);
        await server.start(config.appPort);
    } catch (err) {
        log.error(err, "Can't start server");
        process.exit(-1);
    }
}

start();
