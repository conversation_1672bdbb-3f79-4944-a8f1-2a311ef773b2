import * as jwt from "jsonwebtoken";
import { TokenExpiredException, TokenVerifyException } from "./errors";
import config from "./config";

const cfg: any = config.internalServerToken;

export async function generateToken<T>(data: T): Promise<string> {
    return new Promise<string>((resolve, reject) => {
        jwt.sign(data, cfg.secret, {
            algorithm: cfg.algorithm,
            expiresIn: cfg.expiresIn,
            issuer: cfg.issuer,
        }, (err, token) => {
            return err ? reject(err) : resolve(token);
        });
    });
}

export function verifyToken<T>(token: string): Promise<T> {
    return new Promise<T>((resolve, reject) => {
        const ignoreExpiration: boolean = cfg.expiresIn ? false : true;
        jwt.verify(token, cfg.secret, {
            algorithms: [cfg.algorithm],
            issuer: cfg.issuer,
            ignoreExpiration,
        }, (err, decoded) => {
            if (err) {
                // catch unique error
                if (err.name === "TokenExpiredError") {
                    return reject(new TokenExpiredException());
                }

                if (err.name === "JsonWebTokenError") {
                    return reject(new TokenVerifyException());
                }

                return reject(err);
            }
            return resolve(decoded);
        });
    });
}
