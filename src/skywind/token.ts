import * as jwt from "jsonwebtoken";
import { errors } from "./errors";

export namespace token {
    export interface TokenConfig {
        secret: string;
        algorithm: jwt.Algorithm;
        issuer: string;
        expiresIn?: number;
    }

    export class TokenVerifyException extends errors.SWBaseError {
        constructor() {
            super(400, 748, "Token verification failure");
        }
    }

    export class TokenExpiredException extends errors.SWBaseError {
        constructor() {
            super(400, 749, "Token has expired");
        }
    }

    export async function generate<T extends object>(data: T, config: TokenConfig): Promise<string> {
        return new Promise<string>((resolve, reject) => {
            const options: jwt.SignOptions = {
                algorithm: config.algorithm,
                issuer: config.issuer,
            };
            if ("expiresIn" in config) {
                options["expiresIn"] = config.expiresIn;
            }
            jwt.sign(data, config.secret, options, (err, token) => err ? reject(err) : resolve(token));
        });
    }

    export async function verify<T extends jwt.JwtPayload>(token: string, config: TokenConfig): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            const ignoreExpiration: boolean = !config.expiresIn;
            jwt.verify(
                token,
                config.secret,
                {
                    algorithms: [config.algorithm],
                    issuer: config.issuer,
                    ignoreExpiration,
                },
                (err, decoded: T) => {
                    if (err) {
                        // catch unique error
                        if (err.name === "TokenExpiredError") {
                            return reject(new TokenExpiredException());
                        }

                        if (err.name === "JsonWebTokenError") {
                            return reject(new TokenVerifyException());
                        }

                        return reject(err);
                    }
                    return resolve(decoded);
                },
            );
        });
    }

    export function parse<T extends jwt.JwtPayload>(token: string): T {
        return jwt.decode(token) as T;
    }
}
