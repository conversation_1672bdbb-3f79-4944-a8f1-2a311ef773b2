import type { Cluster, Redis } from "ioredis";
import { Lazy } from "../../resources";
import { redis } from "./redis";
import { measures } from "./measures/measures";
import { errors } from "./errors";
import big = require("js-big-integer");
import RedisPool = redis.RedisPool;
import measure = measures.measure;
import SWBaseError = errors.SWBaseError;

export class GeneratorBoundaryError extends SWBaseError {
    constructor(currentId: big.BigInteger, min: big.BigInteger, max: big.BigInteger) {
        super(500, 514, `Generator id reached boundary ${currentId.toString()}: ${min.toString()} - ${max.toString()}`);
    }
}

export class HiLoIdGenerator {
    private low: big.BigInteger;
    private min: big.BigInteger;
    private max: big.BigInteger;
    private currentId: big.BigInteger;
    private threshold: big.BigInteger;

    constructor(private key: string,
                low: number,
                private connection: Lazy<RedisPool>,
                min: string = "0",
                max: string = Number.MAX_SAFE_INTEGER.toString()) {
        this.threshold = new big.BigInteger((low - 1).toString());
        this.low = new big.BigInteger(low.toString());
        this.min = new big.BigInteger(min);
        this.max = new big.BigInteger(max);
    }

    public async init(): Promise<void> {
        const client = await this.connection.get().get();
        try {
            await client.set(`${this.key}:min`, this.min);
            await client.set(`${this.key}:max`, this.max);
        } finally {
            await this.connection.get().release(client);
        }
    }

    public async nextId(): Promise<string> {
        this.checkBoundary();
        const id = await this.generateNextId();
        return id.toString();
    }

    private async generateNextId(): Promise<any> {
        if (this.currentId === undefined ||
            this.threshold === undefined ||
            this.currentId.compareTo(this.threshold) >= 0) {
            const hiValue: string = await this.incrementHiValue();
            this.currentId = new big.BigInteger(hiValue, 10).subtract(big.BigInteger.ONE)
                .multiply(this.low)
                .add(this.min);
            this.threshold = this.currentId.add(this.low).subtract(big.BigInteger.ONE);
            this.checkBoundary();
        } else {
            this.currentId = this.currentId.add(big.BigInteger.ONE);
        }
        return this.currentId;
    }

    private checkBoundary() {
        if (this.currentId && this.currentId.compareTo(this.max) >= 0) {
            throw new GeneratorBoundaryError(this.currentId, this.min, this.max);
        }
    }

    @measure({ name: "HiLoIdGenerator.incrementHiValue", isAsync: true, debugOnly: true })
    private async incrementHiValue(): Promise<string> {
        const client: Redis | Cluster = await this.connection.get().get();
        try {
            const n = await client.incrby(this.key, 1);
            return n.toString();
        } finally {
            await this.connection.get().release(client);
        }
    }
}
