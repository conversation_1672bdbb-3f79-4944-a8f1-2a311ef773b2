const config = {
    measures: {
        /**
         * Marks measures to use memory to collect measured info. Otherwise, newrelic is used.
         */
        provider: process.env.MEASURES_PROVIDER || "prometheus",
        baseInstrument: JSON.parse(process.env.MEASURES_BASE_INSTRUMENT || "true"),
        trackTransaction: JSON.parse(process.env.MEASURES_TRACK_TRANSACTION || "true"),
        leakProtectionInterval: +process.env.MEASURES_LEAK_PROTECTION_INTERVAL || (5 * 60 * 1000),
        leakCleanupBatchSize: +process.env.MEASURES_LEAK_BATCH_SIZE || 1000,
        setTransferableHeaders: JSON.parse(process.env.SET_TRANSFERABLE_HEADERS || "true"), // for security reasons, disable transferable headers if routes are used to return data to the client
        useOldVersionOfGeneratingTraceId: process.env.USE_OLD_VERSION_OF_GENERATING_TRACE_ID === "true",
        allowedPrefixes: JSON.parse(process.env.MEASURES_ALLOWED_PREFIXES || "[]"), // should contain an array of string like "[\"/v1\",\"/health\",\"/game2\"]" for filtering out not allowed requests
        normalizedPaths: JSON.parse(process.env.MEASURES_NORMALIZED_PATHS || "[]"), // should contain 2-dimensions array with RegExp and replacement string for each path to normalize. E.g. [["^\/casino\/history/.*", "/casino/history/#id"], ["RegExp2", "Replacement2"]]
    },

    server: {
        envPrefix: process.env.ENV_PREFIX || "HEALTHCHECK_",
    },

    publicId: {
        password: process.env.PCID_PASSWORD || "PjvMCu7AmUuhNTzYFqLHrYTctKxUQEpcygDJ7qePxjhWDahCQ2PqSynf93wt8ndW",
        minLength: +process.env.PCID_MIN_HASH_LENGTH || 8,
    },

    serviceName: process.env.SW_SERVICE_NAME,

    logging: {
        graylog: {
            host: process.env.GRAYLOG_HOST,
            port: +process.env.GRAYLOG_PORT || null,
        },

        kafka: {
            highWaterMark: +process.env.KAFKA_LOGGING_HIGHWATER_MARK || 1024,
            recreateStreamTimeout: +process.env.KAFKA_LOGGING_STREAM_RECREATE_TIMEOUT || 1000,
            kafkaHost: process.env.KAFKA_LOGGIN_HOST || "kafka:9092",
            requestTimeout: +process.env.KAFKA_LOGGING_REQUEST_TIMEOUT || 1000,
            connectionTimeout: +process.env.KAFKA_LOGGING_CONNECT_TIMEOUT || 1000,
            requireAcks: +process.env.KAFKA_LOGGING_REQUIRE_ACK || 0,
            ackTimeoutMs: +process.env.KAFKA_LOGGING_ACK_TIMEOUT || 0,
            partitionerType: +process.env.KAFKA_LOGGING_PARTITIONER_TYPE || 1,
            topic: process.env.KAFKA_LOGGING_TOPIC || "sw-logging",
        },
    },
};

export default config;
