import { lazy } from "./lazy";
import config from "./config";

const container = lazy(() => {
    const params = {};

    const healthKeys = Object.keys(process.env)
        .filter(c => c.startsWith(config.server.envPrefix));

    for (const key of healthKeys) {
        const cleanedKey = key.replace(config.server.envPrefix, "");
        params[cleanedKey] = process.env[key];
    }
    return params;

});

export function getEnvironmentInfo(): any {
    return container.get();
}
