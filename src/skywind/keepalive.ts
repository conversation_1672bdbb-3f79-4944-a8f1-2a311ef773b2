import * as agent from "agentkeepalive";
import type { Agent as HttpAgent } from "http";
import type { Agent as HttpsAgent } from "https";

export namespace keepalive {
    export interface KeepAliveConfig {
        maxFreeSockets: number;
        freeSocketKeepAliveTimeout: number;
        socketActiveTTL: number;
    }

    export function createAgent(config: KeepAliveConfig, https: boolean | string = false): HttpAgent | HttpsAgent {
        if (config.maxFreeSockets) {
            const isHttps = (typeof https === "boolean") ? https : https.startsWith("https");
            const agentConfig = {
                maxFreeSockets: config.maxFreeSockets,
                socketActiveTTL: config.socketActiveTTL,
                freeSocketTimeout: config.freeSocketKeepAliveTimeout,
            };
            return isHttps ? new agent.HttpsAgent(agentConfig) : new agent(agentConfig);
        }
    }
}
