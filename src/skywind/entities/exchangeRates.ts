import type { ExchangeRateType } from "@skywind-group/sw-currency-exchange";

export interface ProviderExchangeRate {
    from: string;
    to: string;
    rate: number;
    providerDate: Date;
    provider: string;
    type: ExchangeRateType;
}

export interface ExchangeRate extends ProviderExchangeRate {
    rateDate: Date;
    artificial?: boolean;
    disableGGR?: true;
}

export interface ExchangeRateFilter {
    type?: ExchangeRateType;
    from?: string;
    to?: string;
}
