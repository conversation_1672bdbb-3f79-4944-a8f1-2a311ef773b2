import { suite, test } from "@testdeck/mocha";
import { expect } from "chai";
import { createHash } from "crypto";

const shaOld = require("sha1");
const shaNew = require("sha-1");

@suite()
class HashSpec {
    @test
    public checkSha() {
        expect(shaOld("test")).eq("a94a8fe5ccb19ba61c4c0873d391e987982fbbd3");
        expect(shaNew("test")).eq("a94a8fe5ccb19ba61c4c0873d391e987982fbbd3");
        expect(
            createHash("sha1").update("test").digest("hex"),
        ).eq("a94a8fe5ccb19ba61c4c0873d391e987982fbbd3");
    }
}
