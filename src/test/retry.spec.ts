import { suite, test, timeout } from "@testdeck/mocha";
import { retry } from "../";
import { expect, should, use } from "chai";

should();
use(require("chai-as-promised"));

function getAction(attempts: number, callback: () => any) {
    let i = attempts;

    return () => {
        // eslint-disable-next-line sonarjs/no-one-iteration-loop
        while (i > 0) {
            callback();
            i--;
            // eslint-disable-next-line prefer-promise-reject-errors
            return Promise.reject("Error");
        }

        return Promise.resolve(attempts);
    };
}

@suite()
class RetrySpec {

    @test()
    @timeout(3000)
    public async testRetries() {
        let counter = 0;
        const result = await retry({ sleep: 10, maxTimeout: 1000 },
            getAction(3, () => counter++) as any);
        expect(result).equals(counter).equals(3);
    }

    @test()
    @timeout(3000)
    public async testRetriesFailed() {
        let counter = 0;
        await expect(retry({ sleep: 10, maxTimeout: 1000 },
            getAction(30, () => counter++) as any)).rejectedWith("Error");
    }

    @test()
    // @timeout(2000)
    public async testRetryWithCondition() {
        let counter = 0;
        await expect(
            retry({ sleep: 10, maxTimeout: 160 },
                getAction(6, () => counter++) as any ))
            .rejectedWith("Error");
        expect(counter).equal(6);

        counter = 0;
        await expect(
            retry({ sleep: 10, maxTimeout: 60 },
                getAction(5, () => counter++) as any, (err: any) => err !== "Error"))
            .rejectedWith("Error");

        expect(counter).equal(1);
    }

    @test()
    @timeout(3000)
    public async testRetryWithWrap() {
        await expect(
            retry({ sleep: 10, maxTimeout: 200 },
                // eslint-disable-next-line prefer-promise-reject-errors
                () => Promise.reject("Error") as any,
                (err: any) => false,
                (err: Error) => "My Error" as any))
            .rejectedWith("My Error");
    }

    @test()
    @timeout(3000)
    public async testNoRetries() {
        let counter = 0;
        await expect(
            retry({ sleep: 0, maxTimeout: 2000 },
                getAction(50, () => counter++) as any))
            .rejectedWith("Error");

        expect(counter).equal(1);
    }
}
