import { suite, test } from "@testdeck/mocha";
import { BaseHttpService } from "..";
import { expect } from "chai";

@suite
class BaseHttpSpec {
    @test
    public async testRightBaseUrl() {
        const http: any = new BaseHttpService("http://localhost.com/skywind/");
        const fullUrl = http.getFullUrl("bet");

        expect(fullUrl).eq("http://localhost.com/skywind/bet");
    }

    @test
    public async testWrongBaseUrl() {
        const http: any = new BaseHttpService("http://localhost.com/skywind");
        const fullUrl = http.getFullUrl("bet");

        expect(fullUrl).eq("http://localhost.com/skywind/bet");
    }

    @test
    public async testWrongBaseUrlAndLeftSlashedUrl() {
        const http: any = new BaseHttpService("http://localhost.com/skywind");
        const fullUrl = http.getFullUrl("/bet");

        expect(fullUrl).eq("http://localhost.com/skywind/bet");
    }

    @test
    public async testWrongBaseUrlAndBothSidesSlashedUrl() {
        const http: any = new BaseHttpService("http://localhost.com/skywind");
        const fullUrl = http.getFullUrl("/bet/");

        expect(fullUrl).eq("http://localhost.com/skywind/bet/");
    }

    @test
    public async testWrongBaseUrlAndRightSlashedUrl() {
        const http: any = new BaseHttpService("http://localhost.com/skywind");
        const fullUrl = http.getFullUrl("bet/");

        expect(fullUrl).eq("http://localhost.com/skywind/bet/");
    }

    @test
    public async testRightBaseUrlAndLeftSlashedUrl() {
        const http: any = new BaseHttpService("http://localhost.com/skywind/");
        const fullUrl = http.getFullUrl("/bet");

        expect(fullUrl).eq("http://localhost.com/skywind/bet");
    }

    @test
    public async testRightBaseUrlAndBothSidesSlashedUrl() {
        const http: any = new BaseHttpService("http://localhost.com/skywind/");
        const fullUrl = http.getFullUrl("/bet/");

        expect(fullUrl).eq("http://localhost.com/skywind/bet/");
    }

    @test
    public async testRightBaseUrlAndRightSlashedUrl() {
        const http: any = new BaseHttpService("http://localhost.com/skywind/");
        const fullUrl = http.getFullUrl("bet/");

        expect(fullUrl).eq("http://localhost.com/skywind/bet/");
    }

    @test
    public async testBaseUrlAndUrlLikeBaseUrlUrlWithParams() {
        // Isoftbet case
        const http: any = new BaseHttpService("http://localhost/rest/service/98");
        const fullUrl = http.getFullUrl("http://localhost/rest/service/98?hash=1234");

        expect(fullUrl).eq("http://localhost/rest/service/98?hash=1234");
    }

    @test
    public async testBaseUrlWithQuestionAndUrl() {
        const http: any = new BaseHttpService("http://localhost.com/skywind?");
        const fullUrl = http.getFullUrl("/bet");

        expect(fullUrl).eq("http://localhost.com/skywind?/bet");
    }

    @test
    public async testBaseUrlWithQuestionMarkUrl() {
        const http: any = new BaseHttpService("http://localhost.com/skywind/?");
        const fullUrl = http.getFullUrl("bet/");

        expect(fullUrl).eq("http://localhost.com/skywind/?/bet/");
    }

    @test
    public async testBaseUrlWithSymbolsAfterQuestionMarkUrl() {
        const http: any = new BaseHttpService("http://localhost.com/skywind/?asd");
        const fullUrl = http.getFullUrl("bet/");

        expect(fullUrl).eq("http://localhost.com/skywind/?asd/bet/");
    }

    @test
    public async testBaseUrlWithEndSlashWithoutSymbolsAfterQuestionMarkUrl() {
        const http: any = new BaseHttpService("http://localhost.com/skywind/?/");
        const fullUrl = http.getFullUrl("bet/");

        expect(fullUrl).eq("http://localhost.com/skywind/?/bet/");
    }

}
