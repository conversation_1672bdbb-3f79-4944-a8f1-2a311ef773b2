/**
 * Command to run:
 * node --require ts-node/register src/test/benchmark/generateTraceId.ts
 */
import { Suite } from "benchmark";
import { generateTraceId } from "../../skywind/measures/traceId";
import config from "../../skywind/config";

const crypto = require("crypto");
const nanoid = require("nanoid");

const suite = new Suite("generate traceId", { minSamples: 200 });
suite
    .add("crypto.randomUUID", () => {
        crypto.randomUUID({ disableEntropyCache: false });
    })
    .add("crypto.randomUUID - disable cache", () => {
        crypto.randomUUID({ disableEntropyCache: true });
    })
    .add("nanoid", () => {
        nanoid.nanoid();
    })
    .add("uuid without hostname", () => {
        config.measures.useOldVersionOfGeneratingTraceId = true;
        generateTraceId();
        config.measures.useOldVersionOfGeneratingTraceId = undefined;
    })
    .add("uuid with hostname", () => {
        process.env["HOSTNAME"] = "redis-api.local";
        config.measures.useOldVersionOfGeneratingTraceId = true;
        generateTraceId();
        config.measures.useOldVersionOfGeneratingTraceId = undefined;
    })
    .on("cycle", (event: any) => {
        process.stdout.write(`${String(event.target)}\n`);
    })
    .on("complete", function() {
        process.stdout.write("Fastest is " + this.filter("fastest").map("name"));
    })
    .run({ async: false });
