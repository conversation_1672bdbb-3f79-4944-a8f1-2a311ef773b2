/**
 * Command to run:
 * node --require ts-node/register src/test/benchmark/generateHash.ts
 */
import { Suite } from "benchmark";
import { createHash } from "crypto";

const shaOld = require("sha1");
const shaNew = require("sha-1");

const data = "\nlocal TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]" +
    "\n" +
    "local TEST_VALUE = \"test\"\n" +
    "return TEST_VALUE .. KEYS[1] .. ARGV[1]";
const suite = new Suite("generate hash", { minSamples: 1000 });
suite
    .add("sha1", () => {
        shaOld(data);
    })
    .add("sha-1", () => {
        shaNew(data);
    })
    .add("crypto.createHash", () => {
        createHash("sha1").update(data).digest("hex");
    })
    .on("cycle", (event: any) => {
        process.stdout.write(`${String(event.target)}\n`);
    })
    .on("complete", function() {
        process.stdout.write("Fastest is " + this.filter("fastest").map("name"));
    })
    .run({ async: false });
