import { expect, should, use } from "chai";
import Ioredis, { type Redis } from "ioredis";
import { redis } from "../../skywind/redis";
import { logging } from "../../skywind/logging/logging";
import logger = logging.logger;
import RedisProc = redis.RedisProc;

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

const redisConf = {
    host: process.env.REDIS_HOST || "redis",
    port: +process.env.REDIS_PORT || 6379,
};

describe("RedisProc", () => {
    it("checks pop script", async () => {
        const client: Redis = new Ioredis(redisConf.port, redisConf.host);
        try {
            const dir = __dirname + "/../../../src/test/redis";

            const redisProc1 = new RedisProc(logger("test"), `${dir}/script1.lua`, `${dir}/script2.lua`);

            const result1 = await redisProc1.exec(client,
                ["test_key1"],
                ["test_value1"]);

            expect(result1).deep.equal("testtest_key1test_value1");

            const result2 = await redisProc1.exec(client,
                ["test_key2"],
                ["test_value2"]);

            expect(result2).deep.equal("testtest_key2test_value2");

            const redisProc2 = new RedisProc(logger("test"), `${dir}/script1.lua`, `${dir}/script2.lua`);
            const result3 = await redisProc2.exec(client,
                ["test_key3"],
                ["test_value3"]);
            expect(result3).deep.equal("testtest_key3test_value3");

        } finally {
            client.disconnect();
        }
    });

    it("file not found", async () => {
        const client: Redis = new Ioredis(redisConf.port, redisConf.host);
        try {
            const script = __dirname + "/../../../resources/lua/wrongFile";

            const redisProc1 = new RedisProc(logger("test"), script);
            await expect(redisProc1.exec(client,
                ["test_key2"],
                ["test_value2"])).to.be.rejectedWith(Error);
        } finally {
            client.disconnect();
        }
    });

    it("recreate procedure after script flush", async () => {
        const client: Redis = new Ioredis(redisConf.port, redisConf.host);
        try {
            const dir = __dirname + "/../../../src/test/redis";

            const redisProc1 = new RedisProc(logger("test"), `${dir}/script1.lua`, `${dir}/script2.lua`);

            const result1 = await redisProc1.exec(client,
                ["test_key1"],
                ["test_value1"]);

            expect(result1).deep.equal("testtest_key1test_value1");

            await client.script("FLUSH");

            const result2 = await redisProc1.exec(client,
                ["test_key2"],
                ["test_value2"]);

            expect(result2).deep.equal("testtest_key2test_value2");

        } finally {
            client.disconnect();
        }
    });
});
