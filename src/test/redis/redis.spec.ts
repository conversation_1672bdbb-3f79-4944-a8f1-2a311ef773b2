import { expect, should } from "chai";
import RedisPool from "../redisPool";

should();

describe("RedisPool", () => {
    it("get/release pool - no leaks", async () => {
        for (let i = 0; i < 100; i++) {
            const client = await RedisPool.get().get();
            await RedisPool.get().release(client);
        }
    });

    it("usingDB", async () => {
        await RedisPool.get().usingDb(async (client) => client.set("test", "1"));
        expect(await RedisPool.get().usingDb((client) => client.get("test")));
    });

    it("usingDbWithReplicate (without replicate)", async () => {
        await RedisPool.get().usingDbWithReplicate(async (client) => client.set("test", "1"));
        expect(await RedisPool.get().usingDbWithReplicate((client) => client.get("test")));
    });
});
