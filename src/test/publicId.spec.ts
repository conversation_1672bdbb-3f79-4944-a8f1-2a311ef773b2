import { suite, test } from "@testdeck/mocha";
import { publicId } from "../skywind/publicId";
import { expect } from "chai";

@suite
class PCIDSpec {
    @test
    public testEncodeDecodeDefault() {
        this.makeTest(publicId.instance);
    }

    @test
    public testEncodeDecode() {
        this.makeTest(new publicId.PCID({ password: "12345678", minLength: 10 }));
    }

    private makeTest(pcid: publicId.PCID) {
        const value = 10;
        const v1 = pcid.encode(value);
        const v2 = pcid.encode(`${value}`);
        expect(v1).equal(v2);
        expect(v1.length).gte(pcid.options.minLength);
        expect(pcid.decode(v1)).eq(value);

        expect(pcid.decode(value)).eq(value);

        expect(Number.isFinite(pcid.decode("AB"))).is.false;
    }
}
