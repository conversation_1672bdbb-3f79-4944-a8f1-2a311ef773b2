import { expect } from "chai";
import { sleep } from "../../skywind/time";
import * as crypto from "crypto";
import { EventEmitter } from "events";
import * as superagent from "superagent";
import { suite, test, timeout } from "@testdeck/mocha";
import { AsyncStorageTransactionRunner } from "../../skywind/measures/runner/asyncStorageRunner";
import { ClsHookedTransactionRunner } from "../../skywind/measures/runner/clsHookedRunner";
import { type TransactionRunner } from "../../skywind/measures/runner/definition";
import Bluebird = require("bluebird");

class BaseTransactionRunnerSpec {
    protected runner: TransactionRunner;
    protected getTestContext = () => this.runner.getContextVariable("context_variable");
    protected setTestContext = (value: string) => this.runner.setContextVariable("context_variable", value);

    @test()
    public async testAsyncAwaitPromise() {
        const id = "testAsyncPromise";

        const m = async () => {
            expect(this.getTestContext()).eq(id);
        };
        await this.runner.runInTransaction(async () => {
            this.setTestContext(id);
            await m();
        });
        expect(this.getTestContext()).is.undefined;
    }

    @test()
    public async testPromise() {
        const id = "testPromise";

        const m = async () => {
            expect(this.getTestContext()).eq(id);
        };
        await this.runner.runInTransaction(async () => {
            this.setTestContext(id);

            // eslint-disable-next-line no-async-promise-executor
            return new Promise<void>(async (resolve) => {
                await m();
                resolve();
            });
        });
        expect(this.getTestContext()).is.undefined;
    }

    @test()
    public async testTimeout() {
        const id = "testTimeout";

        const m = async () => {
            expect(this.getTestContext()).eq(id);
        };
        let success = false;
        await this.runner.runInTransaction(async () => {
            this.setTestContext(id);

            setTimeout(async () => {
                await m();
                success = true;
            }, 100);
        });

        await sleep(1000);
        expect(success).is.true;
        expect(this.getTestContext()).is.undefined;
    }

    @test()
    public async testInterval() {
        const id = "testInterval";

        const m = async () => {
            expect(this.getTestContext()).eq(id);
        };
        let success = false;
        await this.runner.runInTransaction(async () => {
            this.setTestContext(id);

            setInterval(async () => {
                await m();
                success = true;
            }, 100);
        });

        await sleep(1000);
        expect(success).is.true;
        expect(this.getTestContext()).is.undefined;
    }

    @test()
    public async testNextTick() {
        const id = "testNextTick";

        const m = async () => {
            expect(this.getTestContext()).eq(id);
        };
        let success = false;
        await this.runner.runInTransaction(async () => {
            this.setTestContext(id);

            process.nextTick(async () => {
                await m();
                success = true;
            });
        });

        await sleep(100);
        expect(success).is.true;
        expect(this.getTestContext()).is.undefined;
    }

    @test()
    public async testCallback() {
        const id = "testCallback";

        let success = false;

        const m = () => {
            expect(this.getTestContext()).eq(id);
            success = true;
        };
        await this.runner.runInTransaction(async () => {
            this.setTestContext(id);

            crypto.randomBytes(10, m);
        });

        await sleep(1000);
        expect(success).is.true;
        expect(this.getTestContext()).is.undefined;
    }

    @test()
    public async testBluebird() {
        const id = "testBluebird";
        let success = false;

        const m = () => {
            expect(this.getTestContext()).eq(id);
        };
        this.runner.runInTransaction(async () => {
            this.setTestContext(id);
            new Bluebird((resolve, reject) => {
                m();
                resolve();
            }).then(() => {
                m();
                success = true;
                return true;
            }).catch(() => false);
        });

        await sleep(1000);
        expect(success).is.true;
        expect(this.getTestContext()).is.undefined;
    }

    @test()
    public async testEmitter() {
        const id = "testEmitter";
        let success = false;

        const m = () => {
            expect(this.getTestContext()).eq(id);
        };
        const emitter = new EventEmitter();
        emitter.on("data", () => {
            m();
            success = true;
        });
        this.runner.runInTransaction(async () => {
            this.setTestContext(id);
            emitter.emit("data", id);
        });
        expect(this.getTestContext()).is.undefined;

        await sleep(1000);
        expect(success).is.true;
        expect(this.getTestContext()).is.undefined;
    }

    @test()
    public async testNested() {
        const id1 = "nested_1";
        const id2 = "nested_2";

        const m1 = async () => {
            expect(this.getTestContext()).eq(id1);
        };

        const m2 = async () => {
            expect(this.getTestContext()).eq(id2);
        };
        await this.runner.runInTransaction(async () => {
            this.setTestContext(id1);
            await m1();

            await this.runner.runInTransaction(async () => {
                this.setTestContext(id2);
                await m2();
            });
            await m1();
        });
        expect(this.getTestContext()).is.undefined;
    }

    @test()
    public async testThentative() {
        const id = "test";

        const m = async () => {
            expect(this.getTestContext()).eq(id);
        };

        await this.runner.runInTransaction(async () => {
            this.setTestContext(id);
            await superagent.get("http://google.com").then(() => m());
            await new Promise<void>((resolve, reject) => {
                superagent.get("http://google.com", (res) => {
                    if (this.getTestContext() !== id) {
                        reject(new Error("Wrong test context"));
                    }
                    resolve();
                });
            });
        });
        expect(this.getTestContext()).is.undefined;
    }

    @test()
    public async testTrackTransaction() {
        const id = "testTrackTransaction";

        const m = async (value) => {
            expect(this.getTestContext()).eq(id);
            expect(value).eq("check_argument");
        };
        const func = this.runner.trackTransaction(async (value) => {
            this.setTestContext(id);
            await m(value);
        });

        await func("check_argument");

        expect(this.getTestContext()).is.undefined;
    }

    @test()
    public async testTransferableContext() {
        const id = "testContext";

        const m = async () => {
            expect(this.getTestContext()).eq(id);
            expect(this.runner.getContextVariable("transferable")).is.undefined;
            expect(this.runner.getContextVariable("transferable", true)).eq("success");
            expect(this.runner.getContext(true)).deep.eq({ transferable: "success" });
        };
        await this.runner.runInTransaction(async () => {
            this.setTestContext(id);
            this.runner.setContextVariable("transferable", "success", true);
            await m();
        });
        expect(this.getTestContext()).is.undefined;
    }
}

@suite
@timeout(20000)
class AsyncStorageTransactionRunnerSpec extends BaseTransactionRunnerSpec {
    constructor() {
        super();
        this.runner = new AsyncStorageTransactionRunner();
    }

    @test()
    public async testExitTransaction() {
        const id = "exitTransaction";

        const m1 = async () => {
            expect(this.getTestContext()).eq(id);
        };

        const m2 = async () => {
            expect(this.getTestContext()).is.undefined;
        };
        await this.runner.runInTransaction(async () => {
            this.setTestContext(id);
            await m1();

            await this.runner.runOutOfTransaction(async () => {
                await m2();
            });
            await m1();
        });
        expect(this.getTestContext()).is.undefined;
    }
}

@suite
@timeout(20000)
class ClsHookedTransactionRunnerSpec extends BaseTransactionRunnerSpec {
    constructor() {
        super();
        this.runner = new ClsHookedTransactionRunner();
    }
}
