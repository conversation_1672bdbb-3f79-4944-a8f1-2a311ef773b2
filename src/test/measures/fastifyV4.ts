import PrometheusProvider from "../../skywind/measures/prometheus";

PrometheusProvider.baseInstrument();
PrometheusProvider.baseInstrument();
const fastifyV4 = require("fastify")();

fastifyV4.get("/", (req, reply) => reply.send({ hello: "world" }));
fastifyV4.get("/prometheus", async (req, reply) => {
    return await PrometheusProvider.getMeasuresStream();
});
fastifyV4.route({
    method: "GET",
    url: "/route",
    handler: function (request, reply) {
        reply.send({ hello: "world" });
    },
});
fastifyV4.listen({ port: 10000 }, err => {
    if (err) throw err;
    console.log(`server listening on ${fastifyV4.server.address().port}`);
});
