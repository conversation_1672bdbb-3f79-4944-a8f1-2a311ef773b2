import { measures } from "../../skywind/measures/measures";
import { expect } from "chai";

import InMemoryProvider from "../../skywind/measures/inMemory";
import PrometheusProvider from "../../skywind/measures/prometheus";
import { MeasureProvider } from "../../skywind/measures/provider";

PrometheusProvider.baseInstrument();
InMemoryProvider.baseInstrument();
const redis = require("redis");
const pg = require("pg");
const kafka = require("kafka-node");
const http = require("http");
const https = require("https");
const bluebird = require("bluebird");

import measure = measures.measure;
import measureParam = measures.measureParam;
import Measurable = measures.Measurable;
import Ioredis, { Redis } from "ioredis";
import { errors } from "../../skywind/errors";
import ERROR_LEVEL = errors.ERROR_LEVEL;

class Merchant implements Measurable {

    constructor(private readonly type: string,
                private readonly code: string) {
    }

    public getMeasureKey(): string {
        return this.type + ":" + this.code;
    }
}

interface Test {
    test(): string;

    testAsync(): Promise<string>;

    testAsync2(test: Merchant,
               someOtherParam: any,
               type: string): Promise<string>;
}

function getTest(provider: MeasureProvider): Test {
    measures.measureProvider = provider;

    class TestClass implements Test {
        @measure({ name: "test" })
        public test(): string {
            return "test-ok";
        }

        @measure({ name: "testAsync", isAsync: true })

        public async testAsync(): Promise<string> {
            return "testAsync1-ok";
        }

        @measure({ name: "testAsync2", isAsync: true })
        public async testAsync2(@measureParam() test: Merchant,
                                someOtherParam: any,
                                @measureParam() type: string): Promise<string> {
            return "testAsync2-ok";
        }
    }

    return new TestClass();
}

describe("Measures", () => {

    it("in memory", async () => {
        const test = getTest(InMemoryProvider);
        expect(test.test()).equal("test-ok");
        expect(await test.testAsync()).equal("testAsync1-ok");

        expect(await test.testAsync2(new Merchant("1", "001"), "something", "type1")).equal("testAsync2-ok");
        expect(await test.testAsync2(new Merchant("2", "002"), "something", "type2")).equal("testAsync2-ok");
        expect(await InMemoryProvider.getMeasure("test")).is.not.undefined;
        expect(await InMemoryProvider.getMeasure("testAsync")).is.not.undefined;
        expect(await InMemoryProvider.getMeasure("testAsync2")).is.not.undefined;
    });

    it("prometheus", async () => {
        const test = getTest(PrometheusProvider);
        expect(test.test()).equal("test-ok");
        expect(await test.testAsync()).equal("testAsync1-ok");

        expect(await test.testAsync2(new Merchant("1", "001"), "something", "type1")).equal("testAsync2-ok");
        expect(await test.testAsync2(new Merchant("2", "002"), "something", "type2")).equal("testAsync2-ok");
        expect(await PrometheusProvider.getMeasure("test")).is.not.undefined;
        expect(await PrometheusProvider.getMeasure("testAsync")).is.not.undefined;
        expect(await PrometheusProvider.getMeasure("testAsync2")).is.not.undefined;
    });
});

describe("Measures.Transactions", () => {
    it("set trx", async () => {
        const action = async () => {
            const test = getTest(PrometheusProvider);
            expect(test.test()).equal("test-ok");
            expect(await test.testAsync()).equal("testAsync1-ok");

            expect(await test.testAsync2(new Merchant("1", "001"), "something", "type1")).equal("testAsync2-ok");
            expect(await test.testAsync2(new Merchant("2", "002"), "something", "type2")).equal("testAsync2-ok");
            const m1 = await PrometheusProvider.getMeasure("test");
            expect(test).is.not.undefined;
            expect(await PrometheusProvider.getMeasure("testAsync")).is.not.undefined;
            expect(await PrometheusProvider.getMeasure("testAsync2")).is.not.undefined;
        };

        await PrometheusProvider.runInTransaction("init", action);
    });

    it("instrumentation", async () => {

        class Test2 implements Test {
            public test(): string {
                return "test-ok - 2";
            }

            public async testAsync(): Promise<string> {
                return "testAsync1-ok - 2";
            }

            public async testAsync2(test: Merchant,
                                    someOtherParam: any,
                                    type: string): Promise<string> {
                return "testAsync2-ok - 2";
            }
        }

        const action = async () => {
            PrometheusProvider.setTransaction("init");
            expect(PrometheusProvider.getTraceID().length).equal(36);
            PrometheusProvider.instrument(Test2.prototype, "test2");
            const test = new Test2();
            expect(test.test()).equal("test-ok - 2");
            expect(await test.testAsync()).equal("testAsync1-ok - 2");

            expect(await test.testAsync2(new Merchant("1", "001"), "something", "type1")).equal("testAsync2-ok - 2");
            expect(await test.testAsync2(new Merchant("2", "002"), "something", "type2")).equal("testAsync2-ok - 2");
            const m1 = await PrometheusProvider.getMeasure("test2.test");
            expect(m1).is.not.undefined;
            expect(await PrometheusProvider.getMeasure("test2.testAsync")).is.not.undefined;
            expect(await PrometheusProvider.getMeasure("test2.testAsync2")).is.not.undefined;
        };

        await PrometheusProvider.trackTransaction(action)();
    });

});

describe("Measures.Gauging", () => {
    it("incrementGauge", async () => {
        PrometheusProvider.incrementGauge("gaugingTest1", 1);
        PrometheusProvider.incrementGauge("gaugingTest1", 1);
        PrometheusProvider.incrementGauge("gaugingTest2", 1);
        const param = await PrometheusProvider.getMeasure("gaugingTest1");
        expect(await param.get()).deep.eq({
            aggregator: "sum",
            help: "gaugingTest1",
            name: "gaugingTest1",
            type: "gauge",
            values: [
                {
                    labels: {},
                    value: 2,
                },
            ],
        });
        expect(await (await PrometheusProvider.getMeasure("gaugingTest2")).get()).deep.eq({
            aggregator: "sum",
            help: "gaugingTest2",
            name: "gaugingTest2",
            type: "gauge",
            values: [
                {
                    labels: {},
                    value: 1,
                },
            ],
        });

        await PrometheusProvider.runInTransaction("init", () => {
            PrometheusProvider.incrementGauge("gaugingTest1", 1);
            PrometheusProvider.incrementGauge("gaugingTest2", 2);
        });

        await PrometheusProvider.runInTransaction("play", () => {
            PrometheusProvider.incrementGauge("gaugingTest1", 3);
            PrometheusProvider.incrementGauge("gaugingTest2", 4);
        });

        expect(await (await PrometheusProvider.getMeasure("gaugingTest1")).get()).deep.eq({
            aggregator: "sum",
            help: "gaugingTest1",
            name: "gaugingTest1",
            type: "gauge",
            values: [
                {
                    labels: {},
                    value: 2,
                },
                {
                    labels: {
                        transaction: "init",
                    },
                    value: 1,
                },
                {
                    labels: {
                        transaction: "play",
                    },
                    value: 3,
                },
            ],
        });
        expect(await (await PrometheusProvider.getMeasure("gaugingTest2")).get()).deep.eq({
            aggregator: "sum",
            help: "gaugingTest2",
            name: "gaugingTest2",
            type: "gauge",
            values: [
                {
                    labels: {},
                    value: 1,
                },
                {
                    labels: {
                        transaction: "init",
                    },
                    value: 2,
                },
                {
                    labels: {
                        transaction: "play",
                    },
                    value: 4,
                },

            ],
        });
    });

    it("setGauge", async () => {
        PrometheusProvider.setGauge("gaugingTest3", 1);
        PrometheusProvider.setGauge("gaugingTest4", 1);
        const param = await PrometheusProvider.getMeasure("gaugingTest3");
        expect(await param.get()).deep.eq({
            aggregator: "sum",
            help: "gaugingTest3",
            name: "gaugingTest3",
            type: "gauge",
            values: [
                {
                    labels: {},
                    value: 1,
                },
            ],
        });
        expect(await (await PrometheusProvider.getMeasure("gaugingTest4")).get()).deep.eq({
            aggregator: "sum",
            help: "gaugingTest4",
            name: "gaugingTest4",
            type: "gauge",
            values: [
                {
                    labels: {},
                    value: 1,
                },
            ],
        });

        await PrometheusProvider.runInTransaction("init", () => {
            PrometheusProvider.setGauge("gaugingTest3", 4);
            PrometheusProvider.setGauge("gaugingTest4", 5);
        });

        await PrometheusProvider.runInTransaction("play", () => {
            PrometheusProvider.setGauge("gaugingTest3", 6);
            PrometheusProvider.setGauge("gaugingTest4", 7);
        });

        expect(await (await PrometheusProvider.getMeasure("gaugingTest3")).get()).deep.eq({
            aggregator: "sum",
            help: "gaugingTest3",
            name: "gaugingTest3",
            type: "gauge",
            values: [
                {
                    labels: {},
                    value: 1,
                },
                {
                    labels: {
                        transaction: "init",
                    },
                    value: 4,
                },
                {
                    labels: {
                        transaction: "play",
                    },
                    value: 6,
                },
            ],
        });
        expect(await (await PrometheusProvider.getMeasure("gaugingTest4")).get()).deep.eq({
            aggregator: "sum",
            help: "gaugingTest4",
            name: "gaugingTest4",
            type: "gauge",
            values: [
                {
                    labels: {},
                    value: 1,
                },
                {
                    labels: {
                        transaction: "init",
                    },
                    value: 5,
                },
                {
                    labels: {
                        transaction: "play",
                    },
                    value: 7,
                },
            ],
        });
    });

    it("saveError", async () => {
        PrometheusProvider.saveError("exception1");
        PrometheusProvider.saveError(new Error("Some error"));
        PrometheusProvider.saveError(new TypeError("Some type error"));
        PrometheusProvider.saveError({ status: 400, code: 123, message: "Some error" });
        PrometheusProvider.saveError({ responseStatus: 400, code: 123, message: "Some error" });
        PrometheusProvider.saveError({ code: 1, status: 4000 }, { action: "test" });
        PrometheusProvider.saveError({ code: 1, status: 4000 }, { provider: "test" });
        PrometheusProvider.saveError({ code: 1, status: 4000 }, { provider: "test" });
        PrometheusProvider.saveError({ code: 1, status: 4000 }, { provider: "test", action: "1" });
        PrometheusProvider.saveError({ code: 1, status: 4000 }, { provider: "test", action: "2" });
        PrometheusProvider.saveError({ code: 1, status: 4000 }, { provider: "test", action: "2" });
        PrometheusProvider.saveError({ code: 1, status: 4000 }, { provider: "test", action: "2" });
        PrometheusProvider.saveError({ code: 1, status: 123, level: ERROR_LEVEL.WARN });

        const measures = await PrometheusProvider.getMeasure("error");
        expect(await measures.get()).deep.eq({
            help: "error",
            name: "error",
            type: "gauge",
            values: [
                {
                    value: 1,
                    labels: {
                        details: "exception:exception1",
                        external: false,
                        level: 0,
                        type: "string",
                        status: "",
                        code: "",
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "Error",
                        external: false,
                        level: 0,
                        type: "Error",
                        status: "",
                        code: "",
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "TypeError",
                        external: false,
                        level: 0,
                        type: "TypeError",
                        status: "",
                        code: "",
                    },
                },
                {
                    value: 2,
                    labels: {
                        details: "Object:400:123",
                        external: false,
                        level: 0,
                        type: "Object",
                        status: 400,
                        code: 123,
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "error",
                        external: false,
                        level: 0,
                        type: "Object",
                        status: 4000,
                        code: 1,
                        action: "test",
                    },
                },
                {
                    value: 2,
                    labels: {
                        details: "error",
                        external: false,
                        level: 0,
                        type: "Object",
                        status: 4000,
                        code: 1,
                        provider: "test",
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "error",
                        external: false,
                        level: 0,
                        type: "Object",
                        status: 4000,
                        code: 1,
                        provider: "test",
                        action: "1",
                    },
                },
                {
                    value: 3,
                    labels: {
                        details: "error",
                        external: false,
                        level: 0,
                        type: "Object",
                        status: 4000,
                        code: 1,
                        provider: "test",
                        action: "2",
                    },
                },
                {
                    labels: {
                        code: 1,
                        details: "error",
                        external: false,
                        level: 1,
                        status: 123,
                        type: "Object",
                    },
                    value: 1,
                },
            ],
            aggregator: "sum",
        });

        await PrometheusProvider.runInTransaction("init", () => {
            PrometheusProvider.saveError("exception1");
            PrometheusProvider.saveError(new Error("Some error"));
            PrometheusProvider.saveError(new TypeError("Some type error"));
            PrometheusProvider.saveError({ status: 400, code: 123, message: "Some error" });
            PrometheusProvider.saveError({ responseStatus: 400, code: 123, message: "Some error" });
        });

        await PrometheusProvider.runInTransaction("play", () => {
            PrometheusProvider.saveError("exception1");
            PrometheusProvider.saveError(new Error("Some error"));
            PrometheusProvider.saveError(new TypeError("Some type error"));
            PrometheusProvider.saveError({ status: 400, code: 123, message: "Some error" });
            PrometheusProvider.saveError({ responseStatus: 400, code: 123, message: "Some error" });
        });
        
        expect(await (await PrometheusProvider.getMeasure("error")).get()).deep.eq({
            help: "error",
            name: "error",
            type: "gauge",
            values: [
                {
                    value: 1,
                    labels: {
                        details: "exception:exception1",
                        external: false,
                        level: 0,
                        type: "string",
                        status: "",
                        code: "",
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "Error",
                        external: false,
                        level: 0,
                        type: "Error",
                        status: "",
                        code: "",
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "TypeError",
                        external: false,
                        level: 0,
                        type: "TypeError",
                        status: "",
                        code: "",
                    },
                },
                {
                    value: 2,
                    labels: {
                        details: "Object:400:123",
                        external: false,
                        level: 0,
                        type: "Object",
                        status: 400,
                        code: 123,
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "error",
                        external: false,
                        level: 0,
                        type: "Object",
                        status: 4000,
                        code: 1,
                        action: "test",
                    },
                },
                {
                    value: 2,
                    labels: {
                        details: "error",
                        external: false,
                        level: 0,
                        type: "Object",
                        status: 4000,
                        code: 1,
                        provider: "test",
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "error",
                        external: false,
                        level: 0,
                        type: "Object",
                        status: 4000,
                        code: 1,
                        provider: "test",
                        action: "1",
                    },
                },
                {
                    value: 3,
                    labels: {
                        details: "error",
                        external: false,
                        level: 0,
                        type: "Object",
                        status: 4000,
                        code: 1,
                        provider: "test",
                        action: "2",
                    },
                },
                {
                    labels: {
                        code: 1,
                        details: "error",
                        external: false,
                        level: 1,
                        status: 123,
                        type: "Object",
                    },
                    value: 1,
                },
                {
                    value: 1,
                    labels: {
                        details: "exception:exception1",
                        transaction: "init",
                        external: false,
                        level: 0,
                        type: "string",
                        status: "",
                        code: "",
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "Error",
                        transaction: "init",
                        external: false,
                        level: 0,
                        type: "Error",
                        status: "",
                        code: "",
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "TypeError",
                        transaction: "init",
                        external: false,
                        level: 0,
                        type: "TypeError",
                        status: "",
                        code: "",
                    },
                },
                {
                    value: 2,
                    labels: {
                        details: "Object:400:123",
                        transaction: "init",
                        external: false,
                        level: 0,
                        type: "Object",
                        status: 400,
                        code: 123,
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "exception:exception1",
                        transaction: "play",
                        external: false,
                        level: 0,
                        type: "string",
                        status: "",
                        code: "",
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "Error",
                        transaction: "play",
                        external: false,
                        level: 0,
                        type: "Error",
                        status: "",
                        code: "",
                    },
                },
                {
                    value: 1,
                    labels: {
                        details: "TypeError",
                        transaction: "play",
                        external: false,
                        level: 0,
                        type: "TypeError",
                        status: "",
                        code: "",
                    },
                },
                {
                    value: 2,
                    labels: {
                        details: "Object:400:123",
                        transaction: "play",
                        external: false,
                        level: 0,
                        type: "Object",
                        status: 400,
                        code: 123,
                    },
                },
            ],
            aggregator: "sum",
        });

        (await PrometheusProvider.getMeasuresStream()).pipe(process.stdout);
    });

    it("ioredis", async () => {
        const redisConf = {
            host: process.env.REDIS_HOST || "redis",
            port: +process.env.REDIS_PORT || 6379,
        };
        const client: Redis = new Ioredis(redisConf.port, redisConf.host);
        try {
            await client.set("test", "1");
            await client.call("get", "foo");
            // legacy
            await (client as any).send_command("get", "foo");
        } finally {
            client.disconnect();
        }
        (await PrometheusProvider.getMeasuresStream()).pipe(process.stdout);
    });
});
