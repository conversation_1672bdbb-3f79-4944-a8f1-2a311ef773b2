import PrometheusProvider from "../../skywind/measures/prometheus";

PrometheusProvider.baseInstrument();
const fastifyV2 = require("fastify")();

fastifyV2.get("/", (req, res) => res.send({ hello: "world" }));
fastifyV2.get("/prometheus", async (req, res) => {
    res.code(200).type("text/plain").send(await PrometheusProvider.getMeasuresStream());
});
fastifyV2.route({
    method: "GET",
    url: "/route",
    handler: function (request, res) {
        res.send({ hello: "world" });
    },
});

fastifyV2.listen({ port: 10000 }, err => {
    if (err) throw err;
    console.log(`server listening on ${fastifyV2.server.address().port}`);
});
