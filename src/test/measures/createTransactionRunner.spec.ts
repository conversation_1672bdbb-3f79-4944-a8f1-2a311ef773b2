import { expect } from "chai";
import { createTransactionRunner } from "../../skywind/measures/runner/transactionRunner";
import { DisabledTransactionRunner } from "../../skywind/measures/runner/disabledRunner";
import { AsyncStorageTransactionRunner } from "../../skywind/measures/runner/asyncStorageRunner";
import { ClsHookedTransactionRunner } from "../../skywind/measures/runner/clsHookedRunner";
import config from "../../skywind/config";
import { stub, restore } from "sinon";

describe("Transaction Runner", () => {
    after(() => {
        restore();
    });
    it("createTransactionRunner", () => {
        expect(createTransactionRunner()).instanceof(AsyncStorageTransactionRunner);

        stub(process.versions, "node").value("8.9.16");
        expect(createTransactionRunner()).instanceof(ClsHookedTransactionRunner);

        config.measures.trackTransaction = undefined;
        expect(createTransactionRunner()).instanceof(DisabledTransactionRunner);
    });
});
