import { suite, test } from "@testdeck/mocha";
import { GelfMessageMapper } from "../../skywind/logging/gelfMapper";
import { expect } from "chai";

@suite()
class GelfMapperSpec {

    @test()
    public testMapping() {
        const ts = Date.now();
        const mapper = new GelfMessageMapper();
        const result = mapper.map({
            name: "test_logger",
            hostname: "localhost",
            message: {
                type: "some_message",
            },
            level: "warn",
            time: ts,
        });

        expect(result).deep.eq(
            {
                host: "localhost",
                timestamp: ts / 1000,
                short_message: { type: "some_message" },
                facility: "test_logger",
                level: "warn",
                // tslint:disable-next-line:max-line-length
                full_message: `{\n  "name": "test_logger",\n  "hostname": "localhost",\n  "message": {\n    "type": "some_message"\n  },\n  "level": "warn",\n  "time": ${ts}\n}`,
            },
        );
    }

    @test()
    public testErrMapping() {
        const ts = Date.now();
        const mapper = new GelfMessageMapper();
        const result = mapper.map({
            name: "test_logger",
            hostname: "localhost",
            message: {
                type: "some_message",
            },
            level: "warn",
            time: ts,
            err: new Error("custom error"),
        });

        // 'line' field has been removed because has 2 different values, when you test using ts or js
        expect(result).contain(
            {
                host: "localhost",
                timestamp: ts / 1000,
                short_message: "custom error",
                facility: "test_logger",
                level: "warn",
                full_message: `{\n  "name": "test_logger",\n  "hostname": "localhost",\n  "message": {\n    "type": "some_message"\n  },\n  "level": "warn",\n  "time": ${ts},\n  "err": {}\n}`,
            },
        );
        expect(result.file.indexOf("gelfMapper.spec")).greaterThan(0);
    }
}
