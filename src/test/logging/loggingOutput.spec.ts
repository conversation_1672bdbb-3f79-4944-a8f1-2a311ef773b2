import { suite, test } from "@testdeck/mocha";
import { LoggingOutput } from "../../skywind/logging/outputStream";
import { expect } from "chai";
import { sleep } from "../../skywind/time";
import type { LogRecord, MessageMapper } from "../../skywind/logging/definitions";

const streamBuffers = require("stream-buffers");

class TestMessageMapper implements MessageMapper {
    public map(log: LogRecord): any {
        return Buffer.from(JSON.stringify(log));
    }
}

class TestMessagesMapper implements MessageMapper {
    public map(log: LogRecord): any {
        return [Buffer.from(JSON.stringify(log)), Buffer.from(JSON.stringify(log))];
    }
}

@suite()
class LoggingOutputSpec {

    @test()
    public testPipe() {
        const stream = new streamBuffers.WritableStreamBuffer({
            initialSize: (100 * 1024), // start at 100 kilobytes.
            incrementAmount: (10 * 1024), // grow by 10 kilobytes each time buffer overflows.
        });
        const ts = Date.now();
        const output = new LoggingOutput(() => stream, {} as any, new TestMessageMapper());

        const data = {
            name: "test_logger",
            hostname: "localhost",
            message: {
                type: "some_message",
            },
            level: "warn",
            time: ts,
        };
        output._write(data, undefined, () => null);

        expect(stream.getContentsAsString("utf8"))
            // tslint:disable-next-line:max-line-length
            .eq(`{"name":"test_logger","hostname":"localhost","message":{"type":"some_message"},"level":"warn","time":${ts}}`);
    }

    @test()
    public async testHandleError() {
        let stream;
        let counter = 0;
        const ts = Date.now();
        const output = new LoggingOutput(() => {
            counter++;
            stream = new streamBuffers.WritableStreamBuffer({
                initialSize: (100 * 1024), // start at 100 kilobytes.
                incrementAmount: (10 * 1024), // grow by 10 kilobytes each time buffer overflows.
            });
            return stream;
        }, { recreateStreamTimeout: 0 } as any, new TestMessageMapper());

        stream.emit("error", new Error());
        await sleep(100);

        const data = {
            name: "test_logger",
            hostname: "localhost",
            message: {
                type: "some_message",
            },
            level: "warn",
            time: ts,
        };
        output._write(data, undefined, () => null);

        expect(stream.getContentsAsString("utf8"))
            // tslint:disable-next-line:max-line-length
            .eq(`{"name":"test_logger","hostname":"localhost","message":{"type":"some_message"},"level":"warn","time":${ts}}`);
    }

    @test()
    public testPipeWithAdditionalTopic() {
        const stream = new streamBuffers.WritableStreamBuffer({
            initialSize: (100 * 1024), // start at 100 kilobytes.
            incrementAmount: (10 * 1024), // grow by 10 kilobytes each time buffer overflows.
        });
        const ts = Date.now();
        const output = new LoggingOutput(() => stream, {} as any, new TestMessagesMapper());

        const data = {
            name: "test_logger",
            hostname: "localhost",
            message: {
                type: "some_message",
            },
            level: "warn",
            time: ts,
        };
        output._write(data, undefined, () => null);

        expect(stream.getContentsAsString("utf8"))
            // tslint:disable-next-line:max-line-length
            .eq(`{"name":"test_logger","hostname":"localhost","message":{"type":"some_message"},"level":"warn","time":${ts}}{"name":"test_logger","hostname":"localhost","message":{"type":"some_message"},"level":"warn","time":${ts}}`);
    }
}
