import { expect } from "chai";
import { describe, it } from "mocha";
import { deepCloneWithoutCircular } from "../../skywind/logging/deepCloneWithoutCircular";

describe("deepCloneWithoutCircular", () => {
    it("object", () => {
        const original = { a: 1, b: { c: 2 } };
        const cloned = deepCloneWithoutCircular(original);
        expect(cloned).to.deep.equal(original);
        expect(cloned).to.not.equal(original);
        expect(cloned.b).to.not.equal(original.b);
    });

    it("array", () => {
        const original = [1, [2, 3], { a: 4 }];
        const cloned = deepCloneWithoutCircular(original);
        expect(cloned).to.deep.equal(original);
        expect(cloned).to.not.equal(original);
        expect(cloned[1]).to.not.equal(original[1]);
        expect(cloned[2]).to.not.equal(original[2]);
    });

    it("circularObject", () => {
        const original: any = { a: 1 };
        original.circular = original;
        const cloned = deepCloneWithoutCircular(original);
        expect(cloned.a).to.equal(1);
        expect(cloned.circular).to.equal("[Circular]");
    });

    it("circularArray", () => {
        const original: any[] = [1, 2, 3];
        original.push(original);
        const cloned = deepCloneWithoutCircular(original);
        expect(cloned).to.deep.equal([1, 2, 3, "[Circular]"]);
    });

    it("Date", () => {
        const original = { date: new Date("2023-01-01") };
        const cloned = deepCloneWithoutCircular(original);
        expect(cloned.date).to.be.instanceOf(Date);
        expect(cloned.date.getTime()).to.equal(original.date.getTime());
    });

    it("RegExp", () => {
        const original = { regex: /test/gi };
        const cloned = deepCloneWithoutCircular(original);
        expect(cloned.regex).to.be.instanceOf(RegExp);
        expect(cloned.regex.toString()).to.equal(original.regex.toString());
    });

    it("Error", () => {
        const original = { error: new Error("Test error") };
        const cloned = deepCloneWithoutCircular(original);
        expect(cloned.error).to.be.instanceOf(Error);
        expect(cloned.error.message).to.equal(original.error.message);
    });

    it("undefined & null", () => {
        const original = { undef: undefined, null: null };
        const cloned = deepCloneWithoutCircular(original);
        expect(cloned.undef).to.be.undefined;
        expect(cloned.null).to.be.null;
    });
});
