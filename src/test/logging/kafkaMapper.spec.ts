import { suite, test } from "@testdeck/mocha";
import { GelfMessageMapper } from "../../skywind/logging/gelfMapper";
import { expect } from "chai";
import { KafkaMessageMapper } from "../../skywind/logging/kafkaOutputStream";

@suite()
class KafkaMapperSpec {

    @test()
    public testMapping() {
        const ts = Date.now();
        const mapper = new KafkaMessageMapper({ topic: "sw_logging" } as any, new GelfMessageMapper());
        const result = mapper.map({
            name: "test_logger",
            hostname: "localhost",
            message: {
                type: "some_message",
            },
            level: "warn",
            time: ts,
        });

        expect(result).deep.eq([
            {
                topic: "sw_logging",
                messages: JSON.stringify({
                    host: "localhost",
                    timestamp: ts / 1000,
                    short_message: { type: "some_message" },
                    facility: "test_logger",
                    level: "warn",
                    // tslint:disable-next-line:max-line-length
                    full_message: `{\n  "name": "test_logger",\n  "hostname": "localhost",\n  "message": {\n    "type": "some_message"\n  },\n  "level": "warn",\n  "time": ${ts}\n}`,
                }),
            },
        ]);
    }

    @test()
    public testMappingWithAdditionalTopic() {
        const ts = Date.now();
        const mapper = new KafkaMessageMapper(
            { topic: "sw_logging", filterFacility: "test", additionalTopic: "test" } as any,
            new GelfMessageMapper(),
        );
        const result = mapper.map({
            name: "test_logger",
            hostname: "localhost",
            message: {
                type: "some_message",
            },
            level: "warn",
            time: ts,
        });

        const message = JSON.stringify({
            host: "localhost",
            timestamp: ts / 1000,
            short_message: { type: "some_message" },
            facility: "test_logger",
            level: "warn",
            // tslint:disable-next-line:max-line-length
            full_message: `{\n  "name": "test_logger",\n  "hostname": "localhost",\n  "message": {\n    "type": "some_message"\n  },\n  "level": "warn",\n  "time": ${ts}\n}`,
        });
        expect(result).deep.eq([
            {
                topic: "sw_logging",
                messages: message,
            }, {
                topic: "test",
                messages: message,
            },
        ]);
    }

    @test()
    public testMappingFalseFilter() {
        const ts = Date.now();
        const mapper = new KafkaMessageMapper(
            { topic: "sw_logging", filterFacility: "sw", additionalTopic: "test" } as any,
            new GelfMessageMapper(),
        );
        const result = mapper.map({
            name: "test_logger",
            hostname: "localhost",
            message: {
                type: "some_message",
            },
            level: "warn",
            time: ts,
        });

        const message = JSON.stringify({
            host: "localhost",
            timestamp: ts / 1000,
            short_message: { type: "some_message" },
            facility: "test_logger",
            level: "warn",
            // tslint:disable-next-line:max-line-length
            full_message: `{\n  "name": "test_logger",\n  "hostname": "localhost",\n  "message": {\n    "type": "some_message"\n  },\n  "level": "warn",\n  "time": ${ts}\n}`,
        });
        expect(result).deep.eq([
            {
                topic: "sw_logging",
                messages: message,
            },
        ]);
    }

    @test()
    public replaceMainTopicToAdditionalTopic() {
        const ts = Date.now();
        const config = {
            topic: "sw_logging",
            isReplaceMainTopicEnabled: true,
            filterFacility: "test",
            additionalTopic: "test",
        } as any;
        const mapper = new KafkaMessageMapper(config, new GelfMessageMapper());
        const result = mapper.map({
            name: "test_logger",
            hostname: "localhost",
            message: {
                type: "some_message",
            },
            level: "warn",
            time: ts,
        });

        const message = JSON.stringify({
            host: "localhost",
            timestamp: ts / 1000,
            short_message: { type: "some_message" },
            facility: "test_logger",
            level: "warn",
            // tslint:disable-next-line:max-line-length
            full_message: `{\n  "name": "test_logger",\n  "hostname": "localhost",\n  "message": {\n    "type": "some_message"\n  },\n  "level": "warn",\n  "time": ${ts}\n}`,
        });
        expect(result).deep.eq([
            {
                topic: "test",
                messages: message,
            },
        ]);
    }
}
