import { describe, it } from "mocha";
import { expect } from "chai";
import { logging } from "../../skywind/logging/logging";

const bole = require("bole");
const streamBuffers = require("stream-buffers");

describe("BoleStream", () => {
    const level = "debug";
    const name = "base";

    const log = logging.logger(name);
    const stream = new streamBuffers.WritableStreamBuffer();
    stream.objectMode = true;
    bole.output([{ level, stream }]);

    it("object", () => {
        const obj = { request: { qwerty: "bla" } };
        log[level](obj, "GET %s", "http://google.com");
        const data = JSON.parse(stream.getContentsAsString("utf8"));
        expect(Object.keys(data)).deep.eq(["time", "hostname", "pid", "level", "name", "message", "request"]);
        expect(data.level).eq(level);
        expect(data.message).eq("GET http://google.com");
        expect(data.request).deep.eq(obj.request);
    });

    it("error", () => {
        const errMessage = "Test error";
        const obj = new Error(errMessage);
        log[level](obj, "GET %s", "http://google.com");
        const data = JSON.parse(stream.getContentsAsString("utf8"));
        expect(Object.keys(data)).deep.eq(["time", "hostname", "pid", "level", "name", "message", "err"]);
        expect(data.err.name).eq("Error");
        expect(data.err.message).eq(errMessage);
    });
});
