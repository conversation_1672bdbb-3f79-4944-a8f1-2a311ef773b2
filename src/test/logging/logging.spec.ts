import { measures } from "../../skywind/measures/measures";
import { suite, test } from "@testdeck/mocha";
import { type SinonStub, stub } from "sinon";
import { logging } from "../../skywind/logging/logging";
import { expect } from "chai";
import db from "../storage/db";

measures.measureProvider.baseInstrument();
import logger = logging.logger;
import errAsObject = logging.errAsObject;
import setRootLogger = logging.setRootLogger;

const bole = require("bole");

bole.output([
    {
        level: "debug", stream: require("bole-console")({ timestamp: true }),
    },
]);

class CustomError extends Error {
    constructor(public readonly responseStatus: number, public readonly code: number, message: string) {
        super(message);
    }
}

@suite()
class LoggingSpec {

    private baseLogger = logger("base");
    private stubLogger;
    private stubWarning: SinonStub;
    private stubInfo: SinonStub;
    private stubDebug: SinonStub;
    private stubError: SinonStub;

    public before() {
        // @ts-ignore
        this.stubLogger = stub(this.baseLogger, "boleLogger");
        this.stubWarning = stub(this.stubLogger, "warn");
        this.stubDebug = stub(this.stubLogger, "debug");
        this.stubInfo = stub(this.stubLogger, "info");
        this.stubError = stub(this.stubLogger, "error");
    }

    public after() {
        this.stubLogger.restore();
        this.stubWarning.restore();
        this.stubDebug.restore();
        this.stubInfo.restore();
        this.stubError.restore();
    }

    @test()
    public testTraceID() {
        measures.measureProvider.runInTransaction("test", () => {
            const traceId = measures.measureProvider.getTraceID();

            this.baseLogger.debug("test");
            expect(this.stubDebug.args).deep.equal([
                [
                    {
                        "sw-trace-id": traceId,
                    },
                    "test",
                ],
            ]);
            this.baseLogger.info("test");
            expect(this.stubInfo.args).deep.equal([
                [
                    {
                        "sw-trace-id": traceId,
                    },
                    "test",
                ],
            ]);
            this.baseLogger.warn("test");
            expect(this.stubWarning.args).deep.equal([
                [
                    {
                        "sw-trace-id": traceId,
                    },
                    "test",
                ],
            ]);
            this.baseLogger.error("test");
            expect(this.stubError.args).deep.equal([
                [
                    {
                        "sw-trace-id": traceId,
                    },
                    "test",
                ],
            ]);
        });
    }

    @test()
    public testWithoutTraceID() {
        this.baseLogger.debug("test");
        expect(this.stubDebug.args).deep.equal([["test"]]);
        this.baseLogger.info("test");
        expect(this.stubInfo.args).deep.equal([["test"]]);
        this.baseLogger.warn("test");
        expect(this.stubWarning.args).deep.equal([["test"]]);
        this.baseLogger.error("test");
        expect(this.stubError.args).deep.equal([["test"]]);
    }

    @test()
    public testWithError() {
        measures.measureProvider.runInTransaction("test", () => {
            const traceId = measures.measureProvider.getTraceID();
            const error = new Error();
            this.baseLogger.error(error, { requestId: 1 }, "Bad request");
            expect(this.stubError.args).deep.equal([
                [
                    {
                        "err": {
                            code: undefined,
                            message: "",
                            name: "Error",
                            stack: this.stubError.args[0][0].err.stack,
                        },
                        "sw-trace-id": traceId,
                    },
                    {
                        requestId: 1,
                    },
                    "Bad request",
                ],
            ]);
        });
    }

    @test()
    public testWithCustomError() {
        measures.measureProvider.runInTransaction("test", () => {
            const traceId = measures.measureProvider.getTraceID();
            const error = new CustomError(500, 333, "Something wrong");
            this.baseLogger.error(error, { requestId: 1 }, "Bad request");
            expect(this.stubError.args).deep.equal([
                [
                    {
                        "err": {
                            code: 333,
                            responseStatus: 500,
                            message: "Something wrong",
                            name: "CustomError",
                            stack: this.stubError.args[0][0].err.stack,
                        },
                        "sw-trace-id": traceId,
                    },
                    {
                        requestId: 1,
                    },
                    "Bad request",
                ],
            ]);
        });
    }

    @test()
    public async testWithSequelizeDBError() {
        const sql: string = "select * from missing_table;";
        await measures.measureProvider.runInTransaction("test", async () => {
            const traceId = measures.measureProvider.getTraceID();
            try {
                await db.query(sql);
            } catch (error) {
                this.baseLogger.error(error, { requestId: 1 }, "Wrong request");
            }
            expect(this.stubError.args).deep.equal([
                [
                    {
                        "err": {
                            code: undefined,
                            message: "relation \"missing_table\" does not exist",
                            name: "SequelizeDatabaseError",
                            sql,
                            stack: this.stubError.args[0][0].err.stack,
                        },
                        "sw-trace-id": traceId,
                    },
                    {
                        requestId: 1,
                    },
                    "Wrong request",
                ],
            ]);
        });
    }

    @test()
    public testErrAsObject() {
        const err = new CustomError(500, 1, "Error as Object message");

        expect(errAsObject(err)).to.be.deep.equal({
            code: 1,
            message: "Error as Object message",
            name: "CustomError",
            responseStatus: 500,
            stack: err.stack,
        });
    }

    @test()
    public testRootLogger_smoke() {
        setRootLogger("root");
        const log = logger("newLogger");
        log.info("test");
    }

    @test()
    public testTransferableVariables() {
        measures.measureProvider.runInTransaction("test", () => {
            const traceId = measures.measureProvider.getTraceID();
            measures.measureProvider.setContextVariable("non-transferable", "value1");
            measures.measureProvider.setContextVariable("player", "player_0001", true);
            measures.measureProvider.setContextVariable("game", "game_001", true);

            this.baseLogger.debug("test");
            expect(this.stubDebug.args).deep.equal([
                [
                    {
                        "sw-trace-id": traceId,
                        "sw-player": "player_0001",
                        "sw-game": "game_001",
                    },
                    "test",
                ],
            ]);
            this.baseLogger.info("test");
            expect(this.stubInfo.args).deep.equal([
                [
                    {
                        "sw-trace-id": traceId,
                        "sw-player": "player_0001",
                        "sw-game": "game_001",
                    },
                    "test",
                ],
            ]);
            this.baseLogger.warn("test");
            expect(this.stubWarning.args).deep.equal([
                [
                    {
                        "sw-trace-id": traceId,
                        "sw-player": "player_0001",
                        "sw-game": "game_001",
                    },
                    "test",
                ],
            ]);
            this.baseLogger.error("test");
            expect(this.stubError.args).deep.equal([
                [
                    {
                        "sw-trace-id": traceId,
                        "sw-player": "player_0001",
                        "sw-game": "game_001",
                    },
                    "test",
                ],
            ]);
        });
    }

    @test()
    public testWithCircularReference() {
        const err: { err0: any } = { err0: undefined };
        err.err0 = { err };

        this.baseLogger.error(err, "test");
        expect(this.stubError.args).deep.equal([
            [
                {
                    err0: {
                        err: "[Circular]",
                    },
                },
                "test",
            ],
        ]);
    }
}
