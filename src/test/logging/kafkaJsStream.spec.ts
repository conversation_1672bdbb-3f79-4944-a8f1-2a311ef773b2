import { expect, use } from "chai";
import { describe, it, beforeEach, afterEach } from "mocha";
import * as sinon from "sinon";
import * as sinon<PERSON>hai from "sinon-chai";
import { <PERSON><PERSON><PERSON>, Producer, ProducerRecord } from "kafkajs";
import {
    createKafkaJsonOutput,
    KafkaJsWritableStream,
    KafkaJsMessageMapper,
} from "../../skywind/logging/kafkaJsStream";
import { KafkaOutputConfig, LogRecord } from "../../skywind/logging/definitions";
import { LoggingOutput } from "../../skywind/logging/outputStream";

use(sinonChai);

describe("KafkaJsWritableStream", () => {
    let mockKafka: sinon.SinonStubbedInstance<Kafka>;
    let mockProducer: sinon.SinonStubbedInstance<Producer>;
    let kafkaStub: sinon.SinonStub;
    let stream: any;

    beforeEach(() => {
        sinon.restore();

        mockProducer = {
            connect: sinon.stub().resolves(),
            send: sinon.stub().resolves(),
            disconnect: sinon.stub().resolves(),
        } as any;

        mockKafka = {
            producer: sinon.stub().returns(mockProducer),
        } as any;

        kafkaStub = sinon.stub(require("kafkajs"), "Kafka").returns(mockKafka);
    });

    afterEach(() => {
        sinon.restore();
    });

    describe("constructor", () => {
        it("should create stream with default options", () => {
            const options = {
                brokers: ["localhost:9092"],
            };

            stream = new KafkaJsWritableStream(options);

            expect(kafkaStub).to.have.been.calledOnce;
            expect(mockKafka.producer).to.have.been.calledOnce;
        });

        it("should create stream with custom options", () => {
            const options = {
                brokers: ["localhost:9092", "localhost:9093"],
                highWaterMark: 2048,
                requestTimeout: 5000,
                connectionTimeout: 3000,
                ackTimeoutMs: 1000,
                retry: { retries: 5 },
            };

            stream = new KafkaJsWritableStream(options);

            expect(kafkaStub).to.have.been.calledWith({
                brokers: options.brokers,
                connectionTimeout: options.connectionTimeout,
                requestTimeout: options.requestTimeout,
                retry: options.retry,
            });
        });
    });

    describe("_write", () => {
        beforeEach(() => {
            stream = new KafkaJsWritableStream({ brokers: ["localhost:9092"] });
        });

        it("should send message successfully", async () => {
            const message: ProducerRecord = {
                topic: "test-topic",
                messages: [{ value: "test message" }],
            };
            const callback = sinon.stub();

            await stream._write(message, "utf8", callback);

            expect(mockProducer.send).to.have.been.calledWith(message);
            expect(callback).to.have.been.calledWith();
        });

        it("should handle send error", async () => {
            const message: ProducerRecord = {
                topic: "test-topic",
                messages: [{ value: "test message" }],
            };
            const error = new Error("Send failed");
            mockProducer.send.rejects(error);
            const callback = sinon.stub();

            await stream._write(message, "utf8", callback);

            expect(callback).to.have.been.calledWith(error);
        });
    });

    describe("_destroy", () => {
        beforeEach(() => {
            stream = new KafkaJsWritableStream({ brokers: ["localhost:9092"] });
        });

        it("should not disconnect when not connected", async () => {
            stream.connected = false;
            const callback = sinon.stub();

            await stream._destroy(null, callback);

            expect(mockProducer.disconnect).to.not.have.been.called;
            expect(callback).to.have.been.calledWith(null);
        });

        it("should handle disconnect error", async () => {
            stream.connected = true;
            const disconnectError = new Error("Disconnect failed");
            mockProducer.disconnect.rejects(disconnectError);
            const callback = sinon.stub();

            await stream._destroy(null, callback);

            expect(callback).to.have.been.calledWith(disconnectError);
        });

        it("should pass through original error when disconnect succeeds", async () => {
            stream.connected = true;
            const originalError = new Error("Original error");
            const callback = sinon.stub();

            await stream._destroy(originalError, callback);

            expect(callback).to.have.been.calledWith(originalError);
        });
    });
});

describe("KafkaJsMessageMapper", () => {
    let mapper: any;
    let mockOriginMapper: sinon.SinonStubbedInstance<any>;
    let config: KafkaOutputConfig;

    beforeEach(() => {
        mockOriginMapper = {
            map: sinon.stub(),
        };

        config = {
            topic: "main-topic",
            requireAcks: 1,
            filterFacility: "test-facility",
            additionalTopic: "additional-topic",
            isReplaceMainTopicEnabled: false,
        } as KafkaOutputConfig;

        mapper = new KafkaJsMessageMapper(config, mockOriginMapper);
    });

    describe("map", () => {
        it("should map log to main topic only when no filter facility match", () => {
            const log: LogRecord = {
                name: "other-service",
                message: "test message",
            } as LogRecord;

            const originResult = { formatted: "message" };
            mockOriginMapper.map.returns(originResult);

            const result = mapper.map(log);

            expect(result).to.have.length(1);
            expect(result[0]).to.deep.equal({
                acks: config.requireAcks,
                topic: config.topic,
                messages: [{
                    value: JSON.stringify(originResult),
                }],
            });
        });

        it("should map log to both topics when filter facility matches and replace is disabled", () => {
            const log: LogRecord = {
                name: "test-facility-service",
                message: "test message",
            } as LogRecord;

            const originResult = { formatted: "message" };
            mockOriginMapper.map.returns(originResult);

            const result = mapper.map(log);

            expect(result).to.have.length(2);
            expect(result[0].topic).to.equal(config.topic);
            expect(result[1].topic).to.equal(config.additionalTopic);
        });

        it("should map log to additional topic only when filter facility matches and replace is enabled", () => {
            config.isReplaceMainTopicEnabled = true;
            mapper = new KafkaJsMessageMapper(config, mockOriginMapper);

            const log: LogRecord = {
                name: "test-facility-service",
                message: "test message",
            } as LogRecord;

            const originResult = { formatted: "message" };
            mockOriginMapper.map.returns(originResult);

            const result = mapper.map(log);

            expect(result).to.have.length(1);
            expect(result[0].topic).to.equal(config.additionalTopic);
        });

        it("should handle log with undefined name", () => {
            const log: LogRecord = {
                message: "test message",
            } as LogRecord;

            const originResult = { formatted: "message" };
            mockOriginMapper.map.returns(originResult);

            const result = mapper.map(log);

            expect(result).to.have.length(1);
            expect(result[0].topic).to.equal(config.topic);
        });

        it("should handle log with empty name", () => {
            const log: LogRecord = {
                name: "",
                message: "test message",
            } as LogRecord;

            const originResult = { formatted: "message" };
            mockOriginMapper.map.returns(originResult);

            const result = mapper.map(log);

            expect(result).to.have.length(1);
            expect(result[0].topic).to.equal(config.topic);
        });
    });
});

describe("createKafkaJsonOutput", () => {
    let config: KafkaOutputConfig;

    beforeEach(() => {
        config = {
            kafkaHost: "localhost:9092,localhost:9093",
            topic: "test-topic",
            highWaterMark: 1024,
            requestTimeout: 30000,
            connectionTimeout: 3000,
            requireAcks: 1,
            ackTimeoutMs: 10000,
            filterFacility: "test",
            additionalTopic: "additional",
            isReplaceMainTopicEnabled: false,
        } as KafkaOutputConfig;
    });

    it("should create LoggingOutput with correct configuration", () => {
        const result = createKafkaJsonOutput(config);

        expect(result).to.be.instanceOf(LoggingOutput);
    });

    it("should handle single kafka host", () => {
        config.kafkaHost = "localhost:9092";

        const result = createKafkaJsonOutput(config);

        expect(result).to.be.instanceOf(LoggingOutput);
    });

    it("should handle multiple kafka hosts", () => {
        config.kafkaHost = "host1:9092,host2:9092,host3:9092";

        const result = createKafkaJsonOutput(config);

        expect(result).to.be.instanceOf(LoggingOutput);
    });
});
