import { suite, test } from "@testdeck/mocha";
import { token } from "../skywind/token";
import { expect, use } from "chai";

use(require("chai-as-promised"));

@suite
class TokensSpec {

    @test
    public async testGenerateVerify() {
        const data = { id: 1, ts: Date.now() };
        const config: token.TokenConfig = { secret: "111111", issuer: "skywind", algorithm: "HS512" };
        const t = await token.generate(data, config);
        expect(await token.verify(t, config)).contains(data);
    }

    @test
    public async testGenerateParse() {
        const data = { id: 1, ts: Date.now() };
        const config: token.TokenConfig = { secret: "111111", issuer: "skywind", algorithm: "HS512" };
        const t = await token.generate(data, config);
        expect(token.parse(t)).contains(data);
    }

    @test
    public async testGenerateVerifyWithExpire() {
        const data = { id: 1, ts: Date.now() };
        const config: token.TokenConfig = { secret: "111111", issuer: "skywind", algorithm: "HS512", expiresIn: 3600 };
        const t = await token.generate(data, config);
        expect(await token.verify(t, config)).contains(data);
    }

    @test
    public async testVerifyIssueError() {
        const data = { id: 1, ts: Date.now() };
        const config: token.TokenConfig = { secret: "111111", issuer: "skywind", algorithm: "HS512" };
        const t = await token.generate(data, config);
        await expect(
            token.verify(t, { ...config, issuer: "someoneelse" }),
        ).to.be.rejectedWith(token.TokenVerifyException);
    }

    @test
    public async testVerifyExpired() {
        let data: any = { id: 1, ts: Date.now(), iat: 111, exp: 111 };
        let config: token.TokenConfig = { secret: "111111", issuer: "skywind", algorithm: "HS512" };
        let t = await token.generate(data, config);
        await expect(
            token.verify(t, { ...config, expiresIn: 1000 }),
        ).to.be.rejectedWith(token.TokenExpiredException);

        data = {};
        config = { secret: "111111", issuer: "skywind", algorithm: "HS512", expiresIn: 0 };
        t = await token.generate(data, config);
        await expect(
            token.verify(t, { ...config, expiresIn: 1000 }),
        ).to.be.rejectedWith(token.TokenExpiredException);
    }

}
