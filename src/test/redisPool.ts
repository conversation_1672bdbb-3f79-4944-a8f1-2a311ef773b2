import { redis } from "../skywind/redis";
import { lazy } from "..";

const redisConf: redis.RedisPoolConfig = {
    host: process.env.REDIS_HOST || "redis",
    port: +process.env.REDIS_PORT || 6379,
    password: undefined,
    minConnections: 1,
    maxConnections: 10,
    maxIdleTime: 1000,
    replicationFactor: 0,
    maxRetriesPerRequest: 0,
    showFriendlyErrorStack: true,
};
export default lazy(() => redis.createRedisPool(redisConf));
