import { expect } from "chai";
import type { SinonStub } from "sinon";
import { stub } from "sinon";
import * as Model from "../../skywind/models/exchangeRates";
import type { ExchangeRate, ProviderExchangeRate } from "../../skywind/entities/exchangeRates";
import { ExchangeRateType } from "@skywind-group/sw-currency-exchange";
import { ExchangeRatesUpdateJob } from "../../skywind/services/updateJob";
import type { CurrencyProvider} from "../../skywind/providers/provider";
import { Provider } from "../../skywind/providers/provider";
import { sleep } from "@skywind-group/sw-utils";
import config from "../../skywind/config";
import { getNextDay, getPrevDay, getToday } from "../../skywind/providers/utils";
import { logging } from "@skywind-group/sw-utils";

logging.setUpOutput({
    type: "console",
    logLevel: "info"
});

describe("Exchange Rates Update Job", () => {

    const originalJobConfig = config.job;

    let currencyProvider: CurrencyProvider;
    let providerGetRates: SinonStub;
    let modelHasRates: SinonStub;
    let modelSaveRates: SinonStub;
    let modelGetRates: SinonStub;

    let updateJob: ExchangeRatesUpdateJob;

    const rates = (providerDate: Date): ProviderExchangeRate[] => [
        {
            from: "USD",
            to: "CNY",
            rate: 6.8309,
            type: ExchangeRateType.BID
        },
        {
            from: "USD",
            to: "EUR",
            rate: 0.861603,
            type: ExchangeRateType.BID
        },
        {
            from: "USD",
            to: "VND",
            rate: 23227.4,
            type: ExchangeRateType.BID
        },
        {
            from: "USD",
            to: "CNY",
            rate: 6.8307,
            type: ExchangeRateType.ASK
        },
        {
            from: "USD",
            to: "EUR",
            rate: 0.861601,
            type: ExchangeRateType.ASK
        },
        {
            from: "USD",
            to: "VND",
            rate: 23227.1,
            type: ExchangeRateType.ASK
        }
    ].map((rate) => ({
        ...rate,
        provider: Provider.DEFAULT,
        providerDate,
    }));
    const storedRates = (providerDate: Date, rateDate: Date): ExchangeRate[] => rates(providerDate)
        .map((rate) => ({
            ...rate,
            rateDate
        }));

    before(() => {
        currencyProvider = {
            getRates: () => { /* stub */
            }
        } as any;
        providerGetRates = stub(currencyProvider, "getRates");
        modelHasRates = stub(Model, "hasRates");
        modelSaveRates = stub(Model, "saveRates");
        modelGetRates = stub(Model, "getRates");
    });

    after(() => {
        providerGetRates.restore();
        modelHasRates.restore();
        modelSaveRates.restore();
        modelGetRates.restore();
    });

    beforeEach(() => {
        updateJob = new ExchangeRatesUpdateJob(currencyProvider);
    });

    afterEach(() => {
        updateJob.clean();
        providerGetRates.reset();
        modelHasRates.reset();
        modelSaveRates.reset();
        modelGetRates.reset();
        config.job = originalJobConfig;
    });

    it("init job", async () => {
        providerGetRates.onCall(0).returns(rates(getPrevDay(new Date(), 2)));
        providerGetRates.onCall(1).returns(rates(getPrevDay()));

        await updateJob.init();

        expect(providerGetRates.calledTwice).to.be.true;
        expect(modelHasRates.calledTwice).to.be.true;
        expect(modelSaveRates.calledTwice).to.be.true;

        expect(modelSaveRates.args[0][0]).to.deep.equal(storedRates(getPrevDay(new Date(), 2), getToday()));
        expect(modelSaveRates.args[1][0]).to.deep.equal(storedRates(getPrevDay(), getNextDay()));
    });

    it("load rates", async () => {
        const historicalDate = getPrevDay(new Date(), 256);

        const providerDate = getPrevDay(historicalDate, 2);
        providerGetRates.returns(rates(providerDate));

        await updateJob.init();

        const historicalRates = await updateJob.load(historicalDate);

        expect(historicalRates).to.deep.equal(storedRates(getPrevDay(historicalDate, 2), historicalDate));
    });

    it("scheduled updates", async () => {
        providerGetRates.returns(rates(getToday()));

        config.job.updateSchedule = "* * * * * *";

        await updateJob.init();

        await sleep(1000);

        expect(providerGetRates.callCount).to.be.eq(3);
        expect(modelHasRates.callCount).to.be.eq(3);
        expect(modelSaveRates.callCount).to.be.eq(3);
    });

    it("retry scheduled updates", async () => {
        providerGetRates.onFirstCall().returns(rates(getToday()));
        providerGetRates.onSecondCall().returns(rates(getToday()));
        providerGetRates.onThirdCall().throws(new Error("test"));
        providerGetRates.onCall(3).returns(rates(getToday()));

        config.job.updateSchedule = "* * * * * *";
        config.job.updateRetryTimeout = 1;

        await updateJob.init();

        await sleep(1000);

        expect(providerGetRates.callCount).to.be.eq(4);
        expect(modelHasRates.callCount).to.be.eq(4);
        expect(modelSaveRates.callCount).to.be.eq(3);
    });

    it("fallback failed update", async () => {
        providerGetRates.onFirstCall().returns(rates(getToday()));
        providerGetRates.onSecondCall().returns(rates(getToday()));
        providerGetRates.throws(new Error("test"));
        modelGetRates.returns(storedRates(getPrevDay(), getNextDay()));

        config.job.updateSchedule = "* * * * * *";
        config.job.updateFallbackTimeout = -1;
        config.job.updateRetryTimeout = 1;

        await updateJob.init();

        await sleep(1000);

        expect(modelGetRates.callCount).to.be.eq(1);
        expect(providerGetRates.callCount).to.be.eq(2);
        expect(modelHasRates.callCount).to.be.eq(2);
        expect(modelSaveRates.callCount).to.be.eq(3);
    });
});
