import { expect, use } from "chai";
import { suite, test } from "@testdeck/mocha";
import RedisPool from "./redisPool";
import { HiLoIdGenerator } from "../skywind/hilowgenerator";

use(require("chai-as-promised"));

@suite()
class HiLoIdGeneratorSpec {

    public async before() {
        RedisPool.get().usingDb(client => client.flushall());
    }

    @test()
    public async generateSequentialIds() {
        const test1 = new HiLoIdGenerator("test1", 10, RedisPool);
        for (let i = 0; i < 1000; i++) {
            const id = await test1.nextId();
            expect(id).equal(i.toString());
        }

        const test2 = new HiLoIdGenerator("test2", 1000, RedisPool);
        for (let i = 0; i <= 2500; i++) {
            const id = await test2.nextId();
            expect(id).equal(i.toString());
        }
    }

    @test()
    public async generateSequentialIdsWithMinMax() {
        const min = 123456789;
        const test1 = new HiLoIdGenerator("test1", 10, RedisPool, min.toString());
        for (let i = 0; i < 1000; i++) {
            const id = await test1.nextId();
            expect(id).equal((min + i).toString());
        }

        const test2 = new HiLoIdGenerator("test2", 1000, RedisPool, min.toString());
        for (let i = 0; i <= 2500; i++) {
            const id = await test2.nextId();
            expect(id).equal((min + i).toString());
        }
    }

    @test()
    public async generateBiggerThanMaxSafeNumber() {
        const min = Number.MAX_SAFE_INTEGER;
        const test1 = new HiLoIdGenerator("test1", 10, RedisPool, min.toString(), "10007199254740991");

        for (let i = 0; i < 1000; i++) {
            expect(await test1.nextId()).equal(`900719925474${(i + 991).toString().padStart(4, "0")}`);
        }

        const test2 = new HiLoIdGenerator("test2", 1000, RedisPool, min.toString(), "10007199254740991");
        for (let i = 0; i < 2500; i++) {
            expect(await test2.nextId()).equal(`900719925474${(i + 991).toString().padStart(4, "0")}`);
        }
    }

    @test()
    public async reachThreshold() {
        const test1 = new HiLoIdGenerator("test1", 10, RedisPool, "0", "1000");
        for (let i = 0; i < 1000; i++) {
            const id = await test1.nextId();
            expect(id).equal(i.toString());
        }

        await expect(test1.nextId()).to.be.rejectedWith(Error);

        const test2 = new HiLoIdGenerator("test2", 1000, RedisPool, "0", "2500");
        for (let i = 0; i <= 2500; i++) {
            const id = await test2.nextId();
            expect(id).equal(i.toString());
        }
        await expect(test2.nextId()).to.be.rejectedWith(Error);

        const test3 = new HiLoIdGenerator("test3", 10, RedisPool, "1000", "2500");
        for (let i = 0; i < 1500; i++) {
            const id = await test3.nextId();
            expect(id).equal(`${i + 1000}`);
        }

        await expect(test1.nextId()).to.be.rejectedWith(Error);
    }

    @test()
    public async testInit() {
        const test1 = new HiLoIdGenerator("test1", 10, RedisPool, "100", "200");
        await test1.init();
        const client = await RedisPool.get().get();
        try {
            expect(await client.get("test1:min")).equal("100");
            expect(await client.get("test1:max")).equal("200");
        } finally {
            RedisPool.get().release(client);
        }
    }
}
