import { expect } from "chai";
import * as request from "supertest";
import { createApplication } from "../../skywind/server";
import { logging } from "@skywind-group/sw-utils";
import type { FastifyInstance } from "fastify";

logging.setUpOutput({
    type: "console",
    logLevel: "info"
});

describe("Health Check", () => {
    let server: FastifyInstance["server"];

    before(async () => {
        const app = createApplication();
        await app.ready();
        server = app.server;
    });

    it("/health", async () => {
        const res = await request(server)
            .get("/v1/health")
            .send();
        expect(res.status).to.be.equal(200);
    });
});
