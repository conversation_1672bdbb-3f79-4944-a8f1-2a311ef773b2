import { expect } from "chai";
import * as request from "supertest";
import { createApplication } from "../../skywind/server";
import { getTimestamp } from "../../skywind/providers/utils";
import { generateToken } from "../../skywind/token";
import { getExchangeRateModel } from "../../skywind/models/exchangeRates";
import { ExchangeRatesUpdateJob } from "../../skywind/services/updateJob";
import { DefaultCurrencyProvider } from "../../skywind/providers/defaultCurrencyProvider";
import config from "../../skywind/config";
import { Provider } from "../../skywind/providers/provider";
import type { FastifyInstance } from "fastify";
import { logging } from "@skywind-group/sw-utils";

logging.setUpOutput({
    type: "console",
    logLevel: "info"
});

describe("Exchange Rates API", () => {
    const originalConfig = config.provider;
    let updateJob: ExchangeRatesUpdateJob;
    let server: FastifyInstance["server"];
    let token: string;

    before(async () => {
        await getExchangeRateModel().sync();
        await getExchangeRateModel().truncate();

        config.provider = Provider.DEFAULT;
        updateJob = new ExchangeRatesUpdateJob(new DefaultCurrencyProvider());
        await updateJob.init();

        const app = createApplication();
        await app.ready();

        server = app.server;
        token = await generateToken({});
    });

    after(() => {
        config.provider = originalConfig;
        updateJob.clean();
    });

    it("/rates", async () => {
        const ts = getTimestamp(new Date());
        const res = await request(server)
            .get("/v1/rates")
            .query({
                startDate: ts,
                endDate: ts,
                token
            })
            .send();
        expect(res.status).to.be.equal(200);
        expect(res.body.length).to.equal(1);
    });

    it("/rates - bid", async () => {
        const ts = getTimestamp(new Date());
        const res = await request(server)
            .get("/v1/rates")
            .query({
                startDate: ts,
                endDate: ts,
                token,
                type: "bid"
            })
            .send();
        expect(res.status).to.be.equal(200);
        expect(res.body.length).to.equal(1);
        expect(res.body[0].source.type).to.equal("bid");
    });

    it("/rates - ask", async () => {
        const ts = getTimestamp(new Date());
        const res = await request(server)
            .get("/v1/rates")
            .query({
                startDate: ts,
                endDate: ts,
                token,
                type: "ask"
            })
            .send();
        expect(res.status).to.be.equal(200);
        expect(res.body.length).to.equal(1);
        expect(res.body[0].source.type).to.equal("ask");
    });

    it("/rates - from/to", async () => {
        const ts = getTimestamp(new Date());
        const res = await request(server)
            .get("/v1/rates")
            .query({
                startDate: ts,
                endDate: ts,
                token,
                from: "USD",
                to: "EUR"
            })
            .send();
        expect(res.status).to.be.equal(200);
        expect(res.body.length).to.equal(1);
        expect(res.body[0].rates.length).to.equal(1);
        expect(res.body[0].rates[0]).to.deep.equal({
            "from": "USD",
            "to": "EUR",
            "rate": 0.87224
        });
    });

    it("/rates - validation error", async () => {
        const res = await request(server)
            .get("/v1/rates")
            .query({
                startDate: "invalid",
                endDate: "invalid",
                token
            })
            .send();
        expect(res.status).to.be.equal(400);
        expect(res.body.code).to.equal(3);
    });

    it("/rates - forbidden", async () => {
        const ts = getTimestamp(new Date());
        const res = await request(server)
            .get("/v1/rates")
            .query({
                startDate: ts,
                endDate: ts,
                token: "invalid"
            })
            .send();
        expect(res.status).to.be.equal(403);
        expect(res.body.code).to.equal(6);
    });

    it("/load/rates", async () => {
        const ts = getTimestamp(new Date());
        const res = await request(server)
            .post("/v1/load/rates")
            .query({
                startDate: ts,
                endDate: ts,
                token
            })
            .send();
        expect(res.status).to.be.equal(200);
        expect(res.body.length).to.equal(2);
    });

    it("/rates/:date", async () => {
        const ts = getTimestamp(new Date());
        const res = await request(server)
            .get("/v1/rates/" + ts)
            .query({ token })
            .send();
        expect(res.status).to.be.equal(200);
        expect(res.body.rates.length).to.equal(171);
    });

    it("/rates/:date - validation error", async () => {
        const res = await request(server)
            .get("/v1/rates/invalid")
            .query({ token })
            .send();
        expect(res.status).to.be.equal(400);
        expect(res.body.code).to.equal(3);
    });

    it("/rates/:date - not found", async () => {
        const res = await request(server)
            .get("/v1/rates/1970-01-01")
            .query({ token })
            .send();
        expect(res.status).to.be.equal(404);
        expect(res.body.code).to.equal(4);
    });

    it("/rates/:date - forbidden", async () => {
        const ts = getTimestamp(new Date());
        const res = await request(server)
            .get("/v1/rates/" + ts)
            .query({ token: "invalid" })
            .send();
        expect(res.status).to.be.equal(403);
        expect(res.body.code).to.equal(6);
    });
});
