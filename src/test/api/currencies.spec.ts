import { expect } from "chai";
import * as request from "supertest";
import { createApplication } from "../../skywind/server";
import { generateToken } from "../../skywind/token";
import { logging } from "@skywind-group/sw-utils";
import type { FastifyInstance } from "fastify";

logging.setUpOutput({
    type: "console",
    logLevel: "info"
});

describe("Currencies API", () => {
    let server: FastifyInstance["server"];
    let validToken: string;

    before(async () => {
        const app = createApplication();
        await app.ready();
        server = app.server;
        validToken = await generateToken({});
    });

    describe("GET /v1/currencies", () => {
        it("should return all currencies with valid token", async () => {
            const res = await request(server)
                .get("/v1/currencies")
                .query({ token: validToken })
                .send();

            expect(res.status).to.equal(200);
            expect(res.body).to.be.an("array");
        });

        it("should return 401 without token", async () => {
            const res = await request(server)
                .get("/v1/currencies")
                .send();

            expect(res.status).to.equal(400); // Fastify validation error for missing required field
        });

        it("should return 403 with invalid token", async () => {
            const res = await request(server)
                .get("/v1/currencies")
                .query({ token: "invalid-token" })
                .send();

            expect(res.status).to.equal(403);
        });

        it("should have valid currency structure", async () => {
            const res = await request(server)
                .get("/v1/currencies")
                .query({ token: validToken })
                .send();

            expect(res.status).to.equal(200);
            res.body.forEach(currency => {
                expect(currency).to.have.property("code");
                expect(currency.code).to.be.a("string");
            });
        });
    });

    describe("GET /v1/currencies/:code", () => {
        it("should return currency details for currency", async () => {
            const res = await request(server)
                .get("/v1/currencies/USD")
                .query({ token: validToken })
                .send();

            expect(res.status).to.equal(200);
            expect(res.body).to.have.property("code", "USD");
        });

        it("should return 404 for unsupported currency", async () => {
            const res = await request(server)
                .get("/v1/currencies/ZZZ")
                .query({ token: validToken })
                .send();

            expect(res.status).to.equal(404);
            expect(res.body).to.have.property("message");
        });

        it("should handle lowercase currency codes", async () => {
            const res = await request(server)
                .get("/v1/currencies/usd")
                .query({ token: validToken })
                .send();

            expect(res.status).to.equal(200);
            expect(res.body.code).to.equal("USD");
        });

        it("should return 401 without token", async () => {
            const res = await request(server)
                .get("/v1/currencies/USD")
                .send();

            expect(res.status).to.equal(400);
        });

        it("should return 403 with invalid token", async () => {
            const res = await request(server)
                .get("/v1/currencies/USD")
                .query({ token: "invalid-token" })
                .send();

            expect(res.status).to.equal(403);
        });
    });
});
