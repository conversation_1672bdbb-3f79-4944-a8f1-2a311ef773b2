import { Sequelize, type PoolOptions, type Options } from "sequelize";
import { config } from "./testConfig";

const db = config.db;
const dbPool: PoolOptions = {

    /**
     * Maximum connections of the pool
     */
    max: 10,

    /**
     * The maximum time, in milliseconds, that a connection can be idle before being released.
     */
    idle: 30000,

};
const dbOptions: Options = {

    /**
     * The dialect of the database you are connecting to. One of mysql, postgres, sqlite, mariadb and mssql.
     */
    dialect: "postgres",

    /**
     * The dialect specific options
     */
    dialectOptions: {},

    /**
     * The host of the relational database.
     */
    host: db.host,

    /**
     * The port of the relational database.
     */
    port: db.port,

    /**
     * Connection pool options
     */
    pool: dbPool,

    /**
     * A function that gets executed everytime Sequelize would log something.
     *
     * Defaults to console.log
     */
    logging: db.queryLogging,

};

if (config.db.ssl.isEnabled) {
    /* tslint:disable:no-string-literal */
    dbOptions.dialectOptions["ssl"] = config.db.ssl;
    /* tslint:enable:no-string-literal */
}

const sequelizeInstance = new Sequelize(db.database, db.user, db.password, dbOptions);
export default sequelizeInstance;
