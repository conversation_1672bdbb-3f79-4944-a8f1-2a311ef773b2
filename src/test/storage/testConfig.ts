export const config = {
    db: {
        database: process.env.PGDATABASE,
        user: process.env.PGUSER,
        password: process.env.PGPASSWORD,
        host: process.env.PGHOST || "db",
        port: +process.env.PGPORT || 5432,
        // wallet uses PG.POOL, which does not support CA certs, only true/false setting for ssl
        ssl: {
            isEnabled: process.env.PG_SECURE_CONNECTION === "true",
            ca: process.env.PG_CA_CERT || "../ca.pem",
        },
        queryLogging(args) {
            /* tslint:disable-next-line:no-console */
            console.log(args);
        },
    },

    measures: {
        /**
         * Turns on debug mode for gathering more measures.
         */
        includeDebugOnly: JSON.parse(process.env.MEASURES_INCLUDE_DEBUG_ONLY || "false"),
    },
};
