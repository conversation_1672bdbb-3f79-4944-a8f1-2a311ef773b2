import { suite, test, timeout } from "@testdeck/mocha";
import { expect } from "chai";
import { Latch, sleep } from "..";

@suite()
class LatchSpec {

    @test()
    @timeout(1000)
    public async testAwaitNotify() {
        const latch = new Latch();

        const result = this.func(latch, 200);
        await sleep(50);
        latch.notify();
        expect(await result).equal(true);
    }

    @test()
    @timeout(1000)
    public async testAwaitNotifyTimeout() {
        const latch = new Latch();

        const result = this.func(latch, 50);
        await sleep(100);
        latch.notify();
        expect(await result).equal(false);
    }

    @test()
    @timeout(1000)
    public async testNotifyOnlyOne() {
        const latch = new Latch();
        const result = [...Array(100).keys()].map(i => this.func(latch, 100));
        await sleep(50);
        expect(latch.waitingCount).eq(100);
        latch.notify();
        await sleep(100);
        const res = await Promise.all(result);
        const notified = res.reduce((s: number, i: boolean) => {
            return i ? s + 1 : s;
        }, 0);
        expect(notified).equals(1);
    }

    @test()
    @timeout(1000)
    public async testNotifyAll() {
        const latch = new Latch();
        const result = [...Array(100).keys()].map(i => this.func(latch, 100));
        await sleep(50);
        expect(latch.waitingCount).eq(100);
        latch.notifyAll();
        const res = await Promise.all(result);
        const notified = res.reduce((s: number, i: boolean) => {
            return i ? s + 1 : s;
        }, 0);
        expect(notified).equals(100);
        expect(latch.waitingCount).eq(0);
    }

    @test()
    @timeout(1000)
    public async testNotifyAllTimeout() {
        const latch = new Latch();
        const result = [...Array(100).keys()].map(i => this.func(latch, 100));
        await sleep(150);
        latch.notifyAll();
        const res = await Promise.all(result);
        const notified = res.reduce((s: number, i: boolean) => {
            return i ? s + 1 : s;
        }, 0);
        expect(notified).equals(0);
    }

    private async func(latch: Latch, timeout: number): Promise<boolean> {
        return await latch.awaitFor(timeout);
    }
}
