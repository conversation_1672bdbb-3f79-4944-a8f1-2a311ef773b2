import { expect } from "chai";
import { stub } from "sinon";
import { SWSCurrencyDatasource } from "../../skywind/datasources/SWSCurrencyDatasource";
import { Provider } from "../../skywind/providers/provider";
import * as currencyExchange from "@skywind-group/sw-currency-exchange";

describe("SWS Currency Datasource", () => {
    let getCurrencyProviderStub;
    let mockProvider;
    let getRatesStub;

    before(() => {
        mockProvider = {
            getRates: stub()
        };
        getCurrencyProviderStub = stub(currencyExchange, "getCurrencyProvider").returns(mockProvider);
        getRatesStub = mockProvider.getRates;
    });

    after(() => {
        getCurrencyProviderStub.restore();
    });

    beforeEach(() => {
        getRatesStub.reset();
    });

    it("get rates", async () => {
        const mockRates = {
            rates: {
                "USD": {
                    "EUR": 0.861603,
                    "CNY": 6.83090,
                    "VND": 23227.4
                },
                "EUR": {
                    "USD": 1.16049,
                    "CNY": 7.92719
                },
                "CNY": {
                    "USD": 0.14637,
                    "EUR": 0.12611
                },
                "VND": {
                    "USD": 0.00004
                }
            }
        };

        getRatesStub.resolves(mockRates);

        const datasource = new SWSCurrencyDatasource();
        const today = new Date();
        const rates = await datasource.load(today, ["USD", "EUR"]);

        expect(rates).to.deep.equal({
            provider: Provider.SWS,
            ts: today,
            bidRates: mockRates.rates,
            askRates: mockRates.rates
        });

        // Verify that getRates was called twice with correct parameters
        expect(getRatesStub.calledTwice).to.be.true;
        expect(getRatesStub.firstCall.args).to.deep.equal([
            today,
            ["USD", "EUR"],
            currencyExchange.ExchangeRateType.ASK
        ]);
        expect(getRatesStub.secondCall.args).to.deep.equal([
            today,
            ["USD", "EUR"],
            currencyExchange.ExchangeRateType.BID
        ]);
    });

    it("handles empty rates response", async () => {
        const emptyRates = {
            rates: {}
        };

        getRatesStub.resolves(emptyRates);

        const datasource = new SWSCurrencyDatasource();
        const today = new Date();
        const rates = await datasource.load(today, ["USD", "EUR"]);

        expect(rates).to.deep.equal({
            provider: Provider.SWS,
            ts: today,
            bidRates: {},
            askRates: {}
        });
    });

    it("handles single base currency", async () => {
        const mockRates = {
            rates: {
                "USD": {
                    "EUR": 0.861603,
                    "CNY": 6.83090
                }
            }
        };

        getRatesStub.resolves(mockRates);

        const datasource = new SWSCurrencyDatasource();
        const today = new Date();
        const rates = await datasource.load(today, ["USD"]);

        expect(rates).to.deep.equal({
            provider: Provider.SWS,
            ts: today,
            bidRates: mockRates.rates,
            askRates: mockRates.rates
        });

        expect(getRatesStub.calledTwice).to.be.true;
        expect(getRatesStub.firstCall.args[1]).to.deep.equal(["USD"]);
        expect(getRatesStub.secondCall.args[1]).to.deep.equal(["USD"]);
    });

    it("handles provider error", async () => {
        const error = new Error("Provider service unavailable");
        getRatesStub.rejects(error);

        const datasource = new SWSCurrencyDatasource();
        const today = new Date();

        try {
            await datasource.load(today, ["USD", "EUR"]);
            expect.fail("Should have thrown an error");
        } catch (err) {
            expect(err.message).to.equal("Provider service unavailable");
        }
    });

    it("passes correct date to provider", async () => {
        const mockRates = { rates: {} };
        getRatesStub.resolves(mockRates);

        const datasource = new SWSCurrencyDatasource();
        const specificDate = new Date('2023-12-25');

        await datasource.load(specificDate, ["USD"]);

        expect(getRatesStub.firstCall.args[0]).to.equal(specificDate);
        expect(getRatesStub.secondCall.args[0]).to.equal(specificDate);
    });
});
