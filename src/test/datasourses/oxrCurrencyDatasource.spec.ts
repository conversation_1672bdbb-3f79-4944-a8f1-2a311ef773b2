import { expect } from "chai";
import { stub } from "sinon";
import { OXRCurrencyDatasource } from "../../skywind/datasources/OXRCurrencyDatasource";
import config from "../../skywind/config";
import { Provider } from "../../skywind/providers/provider";
import { logging } from "@skywind-group/sw-utils";

logging.setUpOutput({
    type: "console",
    logLevel: "info"
});

describe("OXR Currency Datasource", () => {
    const originalConfig = config.oxr;
    let fetchStub: sinon.SinonStub;

    const mockResponseData = {
        "timestamp": **********,
        "base": "USD",
        "rates": {
            "EUR": 0.861603,
            "CNY": 6.83090,
            "VND": 23227.4
        }
    };

    before(() => {
        config.oxr.appKey = "test";
        fetchStub = stub(global, "fetch");
    });

    after(() => {
        config.oxr = originalConfig;
        fetchStub.restore();
    });

    it("get rates", async () => {
        fetchStub.resolves({
            ok: true,
            status: 200,
            json: async () => mockResponseData,
            text: async () => JSON.stringify(mockResponseData)
        } as Response);

        const provider = new OXRCurrencyDatasource();
        const today = new Date();
        const rates = await provider.load(today, ["USD"]);
        expect(rates).to.deep.equal({
            "provider": Provider.OXR,
            "bidRates": {
                "USD": {
                    "CNY": 6.8309,
                    "EUR": 0.861603,
                    "VND": 23227.4
                }
            },
            "askRates": {
                "USD": {
                    "CNY": 6.8309,
                    "EUR": 0.861603,
                    "VND": 23227.4
                }
            },
            "ts": today
        });
    });
});
