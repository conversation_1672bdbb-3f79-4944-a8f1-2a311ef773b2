import { suite, test } from "@testdeck/mocha";
import { expect } from "chai";
import { getSecuredObjectData, getSecuredXmlData } from "..";

@suite
class XmlSecuritySpec {
    @test
    public async secureXmlData() {
        const xml = `<root><username>username</username><password>123456</password></root>`;
        const securedXml = getSecuredXmlData(xml, ["password"]);
        expect(securedXml).eq(`<root><username>username</username><password>***</password></root>`);
    }

    @test
    public async undefinedXmlData() {
        const securedXml = getSecuredXmlData(undefined);
        expect(securedXml).eq(undefined);
    }

    @test
    public async secureObjectDataArray() {
        const objectData = {
            root: {
                data: [
                    {
                        item: {
                            value: 1
                        }
                    },
                    {
                        item: {
                            value: 2
                        }
                    },
                    {
                        item: {
                            value: 3
                        }
                    }
                ]
            }
        };

        const securedXml = getSecuredObjectData(objectData, ["password"]);
        expect(securedXml).to.be.deep.equal(objectData);
    }
}
