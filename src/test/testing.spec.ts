import { suite, test } from "@testdeck/mocha";
import * as superagent from "superagent";
import { expect } from "chai";
import { testing } from "../skywind/testing";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import status400 = testing.status400;
import status500 = testing.status500;
import onCall = testing.onCall;

async function testRequest() {
    try {
        return await superagent.get("/test").send();
    } catch (err) {
        return {
            error: err.toString(),
            response: err.response,
        };
    }
}

@suite()
class TestingSpec {
    public static mocker: RequestMock;
    public static before() {
        TestingSpec.mocker = requestMock(superagent);
    }

    public after() {
        TestingSpec.mocker.clearRoutes();
    }

    public static after() {
        TestingSpec.mocker.unmock(superagent);
    }

    @test()
    public async testGet200() {
        const expectedBody = { hello: "test" };
        TestingSpec.mocker.get("/test", status200(expectedBody));

        const result = await testRequest();

        expect(result).to.be.deep.equal({
            body: expectedBody,
            status: 200,
        });
    }

    @test()
    public async testGet400() {
        const expectedBody = { hello: "test" };
        TestingSpec.mocker.get("/test", status400(expectedBody));

        const result = await testRequest();

        expect(result).to.be.deep.equal({
            error: "Error: 400",
            response: {
                body: expectedBody,
                status: 400,
            },
        });
    }

    @test()
    public async testGet500() {
        const expectedBody = { hello: "test" };
        TestingSpec.mocker.get("/test", status500(expectedBody));

        const result = await testRequest();

        expect(result).to.be.deep.equal({
            error: "Error: 500",
            response: {
                body: expectedBody,
                status: 500,
            },
        });
    }

    @test()
    public async testOnCall() {
        const expectedBody = { hello: "test" };
        const mockHandler = onCall(status200(expectedBody))
            .onCall(status500(null))
            .onCall(status400(null));

        TestingSpec.mocker.get("/test", mockHandler);

        const result = await testRequest();
        const result2 = await testRequest();
        const result3 = await testRequest();

        expect(result).to.be.deep.equal({
            body: expectedBody,
            status: 200,
        });

        expect(result2).to.be.deep.equal({
            error: "Error: 500",
            response: {
                body: null,
                status: 500,
            },
        });

        expect(result3).to.be.deep.equal({
            error: "Error: 400",
            response: {
                body: null,
                status: 400,
            },
        });
    }

}
