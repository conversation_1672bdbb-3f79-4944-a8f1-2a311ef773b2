import { suite, test } from "@testdeck/mocha";
import { expect } from "chai";

@suite()
class OptionalChainingSpec {

    @test()
    public getsLevel1() {
        const obj = {
            p1: "test",
        };
        expect(obj?.p1).to.be.deep.equal("test");
    }

    @test()
    public getsLevel1Undefined() {
        const obj = {
            p1: undefined,
        };
        expect(obj?.p1).to.be.deep.equal(undefined);
    }

    @test()
    public getsLevel1FullPathUndefined() {
        const obj = {
            p1: undefined,
        };
        expect(obj?.p1?.p2?.p3?.p4?.p5?.p6).to.be.deep.equal(undefined);
    }

    @test()
    public getsLevel2() {
        const obj = {
            p1: {
                p2: "test",
            },
        };
        expect(obj?.p1?.p2).to.be.deep.equal("test");
    }

    @test()
    public getsLevel2Undefined() {
        const obj = {
            p1: {
                p2: undefined,
            },
        };
        expect(obj?.p1?.p2).to.be.deep.equal(undefined);
    }

    @test()
    public getsLevel3Undefined() {
        const obj = {
            p1: {
                p2: {
                    p3: undefined,
                },
            },
        };
        expect(obj?.p1?.p2?.p3).to.be.deep.equal(undefined);
    }

    @test()
    public getsLevel4() {
        const obj = {
            p1: {
                p2: {
                    p3: {
                        p4: "test",
                    },
                },
            },
        };
        expect(obj?.p1?.p2?.p3?.p4).to.be.deep.equal("test");
    }

    @test()
    public getsLevel4Undefined() {
        const obj = {
            p1: {
                p2: {
                    p3: {
                        p4: undefined,
                    },
                },
            },
        };
        expect(obj?.p1?.p2?.p3?.p4).to.be.deep.equal(undefined);
    }

    @test()
    public getsLevel5() {
        const obj = {
            p1: {
                p2: {
                    p3: {
                        p4: {
                            p5: "test",
                        },
                    },
                },
            },
        };
        expect(obj?.p1?.p2?.p3?.p4?.p5).to.be.deep.equal("test");
    }

    @test()
    public getsLevel5Undefined() {
        const obj = {
            p1: {
                p2: {
                    p3: {
                        p4: {
                            p5: undefined,
                        },
                    },
                },
            },
        };
        expect(obj?.p1?.p2?.p3?.p4?.p5).to.be.deep.equal(undefined);
    }

    @test()
    public getsLevel6() {
        const obj = {
            p1: {
                p2: {
                    p3: {
                        p4: {
                            p5: {
                                p6: "test",
                            },
                        },
                    },
                },
            },
        };
        expect(obj?.p1?.p2?.p3?.p4?.p5?.p6).to.be.deep.equal("test");
    }

    @test()
    public getsLevel6Undefined() {
        const obj = {
            p1: {
                p2: {
                    p3: {
                        p4: {
                            p5: {
                                p6: undefined,
                            },
                        },
                    },
                },
            },
        };
        expect(obj?.p1?.p2?.p3?.p4?.p5?.p6).to.be.deep.equal(undefined);
    }

    @test()
    public mixExpression1() {
        const obj1 = {
            p1: {
                p2: undefined,
            },
        };
        const obj2 = {
            p1: {
                p2: {
                    p3: "obj2.p3",
                },
            },
        };

        expect((obj1?.p1?.p2 || obj2?.p1?.p2)?.p3).to.be.deep.equal("obj2.p3");
    }

    @test()
    public mixExpression3() {
        const obj1: any = {
            p1: {
                p2: 2,
            },
        };
        const obj2 = {
            p1: {
                p2: {
                    p3: 3,
                },
            },
        };

        expect((obj1?.p1?.p2) + (obj2?.p1?.p2?.p3)).eq(5);
        expect((obj1?.p1?.p2?.p3) || 0 + (obj2?.p1?.p2?.p3)).eq(3);
    }

    @test()
    public testPropertyAccess() {
        const obj = {
            p1: {
                p2: {
                    p3: {
                        p4: {
                            p5: {
                                p6: "test",
                            },
                        },
                    },
                },
            },
        };
        expect(obj?.p1?.["p2"]?.["p3"]?.["p4"]?.["p5"]?.["p6"]).to.be.deep.equal("test");
    }

    @test()
    public testPropertyAccessForArray() {
        const obj = [
            {
                p1: {
                    p2: {
                        p3: {
                            p4: {
                                p5: {
                                    p6: "test",
                                },
                            },
                        },
                    },
                },
            },
            {
                p1: {
                    p2: {},
                },
            },
        ];
        expect(obj[0]?.p1?.p2?.p3?.p4?.p5?.p6).to.be.deep.equal("test");

        expect(obj[1]?.p1?.p2?.p3?.p4?.p5?.p6).equal(undefined);
    }
}
