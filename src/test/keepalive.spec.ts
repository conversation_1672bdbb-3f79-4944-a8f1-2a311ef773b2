import { suite, test } from "@testdeck/mocha";
import { expect } from "chai";
import { keepalive } from "../skywind/keepalive";
import { Agent } from "http";
import { HttpsAgent } from "agentkeepalive";

@suite()
class Keepalive {

    @test()
    public async testCreate() {
        expect(keepalive.createAgent({
            maxFreeSockets: 10,
            freeSocketKeepAliveTimeout: 10000,
            socketActiveTTL: 100000,
        }) instanceof Agent).is.true;

        expect(keepalive.createAgent({
            maxFreeSockets: 10,
            freeSocketKeepAliveTimeout: 10000,
            socketActiveTTL: 100000,
        }, false) instanceof Agent).is.true;

        expect(keepalive.createAgent({
            maxFreeSockets: 10,
            freeSocketKeepAliveTimeout: 10000,
            socketActiveTTL: 100000,
        }, "http://localhost:80") instanceof Agent).is.true;

        expect(keepalive.createAgent({
            maxFreeSockets: 10,
            freeSocketKeepAliveTimeout: 10000,
            socketActiveTTL: 100000,
        }, true) instanceof HttpsAgent).is.true;

        expect(keepalive.createAgent({
            maxFreeSockets: 10,
            freeSocketKeepAliveTimeout: 10000,
            socketActiveTTL: 100000,
        }, "https://localhost:8443") instanceof HttpsAgent).is.true;

        expect(keepalive.createAgent({
            maxFreeSockets: 0,
            freeSocketKeepAliveTimeout: 10000,
            socketActiveTTL: 100000,
        }, "https://localhost:8443")).is.undefined;
    }

}
