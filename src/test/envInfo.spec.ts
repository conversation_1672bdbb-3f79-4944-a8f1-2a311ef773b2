import { suite, test } from "@testdeck/mocha";
import { expect } from "chai";
import { getEnvironmentInfo } from "../skywind/envInfo";
import config from "../skywind/config";

@suite()
class EnvInfoSpec {
    @test()
    public getEnvInfo() {
        process.env[config.server.envPrefix + "FIRSTNAME"] = "Chebureck";
        process.env[config.server.envPrefix + "LASTNAME"] = "Buhanovich";

        expect(getEnvironmentInfo()).to.be.deep.equal({
            FIRSTNAME: "Chebureck",
            LASTNAME: "Buhanovich",
        });
    }
}
