import { suite, test } from "@testdeck/mocha";
import { expect } from "chai";
import { getSecuredObjectData, getSecuredXmlData } from "..";

@suite
class SecureDataSpec {
    @test
    public async secureXmlData() {
        const xml = `<root><username>username</username><password>123456</password></root>`;
        const securedXml = getSecuredXmlData(xml, ["password"]);
        expect(securedXml).eq(`<root><username>username</username><password>***</password></root>`);
    }

    @test
    public async undefinedXmlData() {
        const securedXml = getSecuredXmlData(undefined);
        expect(securedXml).eq(undefined);
    }

    @test
    public async secureObjectDataArrayWithoutCensoring() {
        const objectData = {
            root: {
                data: [
                    {
                        item: {
                            value: 1
                        }
                    },
                    {
                        item: {
                            value: 2
                        }
                    },
                    {
                        item: {
                            value: 3
                        }
                    }
                ]
            }
        };

        const securedData = getSecuredObjectData(objectData, ["password"]);
        expect(securedData).to.deep.equal(objectData);
    }

    @test
    public async secureObjectDataArrayWithCensoring() {
        const objectData = {
            root: {
                data: [
                    {
                        item: {
                            value: 1
                        }
                    },
                    {
                        item: {
                            value: 2
                        }
                    },
                    {
                        item: {
                            value: 3
                        }
                    }
                ],
                password: "1234"
            }
        };

        const securedData = getSecuredObjectData(objectData, ["password"]);
        expect(securedData).to.deep.equal({ root: { ...objectData.root, password: "***" } });
    }

    @test
    public async secureObjectDataArrayWithBooleanValues() {
        const objectData = {
            root: {
                data: [
                    {
                        item: {
                            value: 1
                        }
                    },
                    {
                        item: {
                            value: 2
                        }
                    },
                    {
                        item: {
                            value: 3
                        }
                    }
                ],
                isSuperAdmin: true
            }
        };

        const securedData = getSecuredObjectData(objectData, ["password"]);
        expect(securedData).to.deep.equal(objectData);
    }

    @test
    public async secureObjectDataArrayWithFalsyValues() {
        const objectData = {
            root: {
                data: [
                    {
                        item: {
                            value: 1
                        }
                    },
                    {
                        item: {
                            value: 2
                        }
                    },
                    {
                        item: {
                            value: 3
                        }
                    }
                ],
                password: undefined
            }
        };

        let securedData = getSecuredObjectData(objectData, ["password"]);
        expect(securedData).to.deep.equal(objectData);

        objectData.root.password = null;
        securedData = getSecuredObjectData(objectData, ["password"]);
        expect(securedData).to.deep.equal(objectData);

        objectData.root.password = "";
        securedData = getSecuredObjectData(objectData, ["password"]);
        expect(securedData).to.deep.equal(objectData);

        objectData.root.password = NaN;
        securedData = getSecuredObjectData(objectData, ["password"]);
        expect(securedData).to.deep.equal(objectData);
    }
}
