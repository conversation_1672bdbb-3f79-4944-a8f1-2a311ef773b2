import { suite, test } from "@testdeck/mocha";
import { expect } from "chai";
import { calculation } from "../skywind/calculation";
import normalizeAmountByPrecision = calculation.normalizeAmountByPrecision;
import safeAddWithPrecision = calculation.safeAddWithPrecision;

@suite()
class CalculationSpec {
    @test()
    public testNormalizeAmountByPrecision() {
        const value = 0.1231238128412371823;
        const precision = 7;

        const result = normalizeAmountByPrecision(precision, value);
        expect(result).to.be.equal(0.1231238);
    }

    @test()
    public testSafeAddWithPrecision() {
        const value1 = 0.1231238128412371823;
        const value2 = 0.8768762128412371823;
        const precision = 7;

        const result = safeAddWithPrecision(precision, value1, value2);
        expect(result).to.be.equal(1);
    }
}
