import { AAMSTokenData } from "./skywind/definitions/startGame";
import { MerchantBonusApiAdditionalFields } from "./skywind/definitions/common";

export {
    getSecuredObjectData,
    getSecuredXmlData
} from "./skywind/utils/common";

export {
    getDomain
} from "./skywind/utils/getDomain";

export {
    interruptSocketForLiveGame
} from "./skywind/utils/interruptSocketForLiveGame";

export {
    BaseHttpService,
    SuperAgentResponse,
    RequestInfo
} from "./skywind/services/baseHTTPService";

export {
    StartGameService,
    DefaultMerchantStartGameService,
    HTTPMerchantStartGameService
} from "./skywind/services/startGameService";

export {
    MerchantRegulationsService,
    DefaultMerchantRegulationsService,
    HTTPMerchantRegulationsService
} from "./skywind/services/regulationsService";

export {
    MerchantPaymentService,
    DefaultMerchantPaymentService,
    HTTPMerchantPaymentService
} from "./skywind/services/paymentService";

export {
    MerchantBrokenGameService,
    DefaultMerchantBrokenGameService,
    HTTPMerchantBrokenGameService
} from "./skywind/services/brokenGameService";

export {
    MerchantBalanceService,
    DefaultMerchantBalanceService,
    HTTPMerchantBalanceService
} from "./skywind/services/balanceService";

export {
    MerchantInfoService,
    DefaultMerchantInfoService,
    HTTPMerchantInfoService
} from "./skywind/services/merchantInfoService";

export {
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantGameURLInfo,
    SegmentFilter,
    MerchantStartGameTokenData,
    SegmentInfo,
    GameTokenData,
    BaseGameTokenData,
    StartGameTokenData,
    MerchantGameTokenInfo,
    StartGameURLInfo,
    AAMSTokenData,
    GGRCalculationType,
    LoginTerminalRequest,
    LoginTerminalResponse,
    AdditionalInfo,
    FreebetInfo
} from "./skywind/definitions/startGame";

export { Balance, BalanceRequestWithoutToken, Balances } from "./skywind/definitions/balance";

export {
    ExtraDataContainer,
    MerchantInfo,
    Proxy,
    MerchantBonusApiAdditionalFields,
    MerchantParams,
    ITrxId,
    HTTPOptions,
    SWAuthData,
    ExtraData,
    PAYMENT_TYPE,
    WALLET_TYPE,
    PLATFORM_TYPE,
    MerchantKeyData,
    MerchantCertSettings
} from "./skywind/definitions/common";

export * from "./skywind/definitions/payment";

export {
    RollbackBetRequest,
    RollbackRequest,
    RollbackWithoutTokenRequest
} from "./skywind/definitions/rollback";

export {
    PlayerInfo
} from "./skywind/definitions/player";

export {
    GameLogoutGameState,
    GameLogoutType,
    GameLogoutRequest,
    GameLoginRequest,
    GameLogoutResponse,
    RoundResolveMethod,
    RoundResolveResult,
    MrchRoundResolveRequest,
    MrchRoundResolveResponse,
    GameLogoutOptions,
    FinalizeGameRequest
} from "./skywind/definitions/brokenGame";

export {
    FreeBetInfoRequest,
    FreeBetInfo
} from "./skywind/definitions/freeBet";

export {
    PlayerRegulatoryActionRequest,
    ReportCriticalFilesToMerchantRequest,
    GameWithHashList,
    CriticalFileWithHash,
    ModuleWithHashList,
    MERCHANT_REGULATION
} from "./skywind/definitions/regulation";

export { PlayMode } from "./skywind/definitions/startGame";

export { MerchantTransferRequest } from "./skywind/definitions/transfer";

export {
    SWError,
    ConnectionError,
    escapeSomeHtmlChars,
    MerchantAdapterAPIError,
    MerchantAdapterAPITransientError,
    RequireRefundBetError,
    CannotCompletePayment,
    InterruptSocket,
    InvalidFreebet,
    InsufficientFreebet,
    AnotherGameInProgress,
    ERROR_LEVEL,
    ErrorSpecialFlag
} from "./skywind/errors";

export {
    ClientMessageType,
    PopupButtonGameAction,
    ClientMessageMap,
    PopupButtonGameActions,
    PlayerRegulatoryActionAtServer,
    PlayerRegulatoryActionsAtServer,
    ExtraMessage,
    ExtraMessageImpl,
    PopupButton,
    PopupButtonImpl,
    LinkButtonImpl,
    PlayerActionServerCallParams,
    PlayerActionServerCall,
    PlayerActionServerCallImpl,
    getExtraMessageTranslatorKey,
    getButtonLabelTranslatorKey,
    MrchExtraData,
    MrchExtraDataImpl
} from "./skywind/actionableResponse";

export { RefundBetRequest } from "./skywind/definitions/refund";

export { generateInternalToken, generateToken, parseInternalToken, verifyToken } from "./skywind/utils/token";

export * from "./skywind/services/internalAPIService";
export * from "./skywind/services/phantomService";

export {
    SWGameInfo,
    SWJackpotTicker,
    SWJackpotTickerMapping,
    Limits,
    SkywindGameTypes
} from "./skywind/definitions/game";

export {
    RoundImageResponse
} from "./skywind/definitions/history";

export {
    PromoInfo,
    PlayerPromotionInfo,
    RewardInfo,
    FreebetGameCoinConfig,
    FreebetGameConfig,
    PROMO_STATUS,
    PROMO_OWNER,
    PROMO_TYPE,
    PROMO_TYPE_OPERATOR
} from "./skywind/definitions/promo";

export {
    SWGameCriticalFilesRequest,
    SWPlatformCriticalFilesRequest,
    ModulesListWithHashes,
    GamesListWithHashes
} from "./skywind/definitions/criticalFiles";

export {
    TickerService,
    SWTickerService
} from "./skywind/services/tickerService";

export {
    MerchantPageRequest,
    MerchantPageResponse,
    merchantPageType
} from "./skywind/definitions/page";

export {
    XmlService
} from "./skywind/services/xmlService";

export { MerchantAdapter, MerchantAdapterDecorator } from "./skywind/services/adapter";
