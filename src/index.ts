function lazyExport(name: string, path: string) {
    let namespace;

    Object.defineProperty(exports, name, {
        enumerable: true,
        get: function() {
            if (namespace === undefined) {
                namespace = require(path)[name];
            }
            return namespace;
        },
    });
}

lazyExport("measures", "./skywind/measures/measures");
export { lazy } from "./skywind/lazy";
export { sleep, parseDateOrThrow } from "./skywind/time";
export { retry } from "./skywind/retry";
export { getEnvironmentInfo } from "./skywind/envInfo";
lazyExport("logging", "./skywind/logging/logging");
lazyExport("kafka", "./skywind/kafka");
lazyExport("redis", "./skywind/redis");
lazyExport("testing", "./skywind/testing");
lazyExport("calculation", "./skywind/calculation");
export { Latch } from "./skywind/latch";
lazyExport("errors", "./skywind/errors");
lazyExport("publicId", "./skywind/publicId");
lazyExport("keepalive", "./skywind/keepalive");
lazyExport("token", "./skywind/token");
lazyExport("HiLoIdGenerator", "./skywind/hilowgenerator");
