# Utils

## Important information!!!
This repository has two branches:
 - develop
 - release/v2

The 'develop' branch contains the old pieces of code, old dependencies and uses nodejs <= 14.
If you want to fix something, pls create a branch from develop and merge PR in the develop branch changing the version in package.json.
You can use any version from 1.0.0 to 2.0.0

The 'release/v2' branch is intended for the transition to new technologies. The version will start from 2.0.0 in package.json

### Breaking Changes

  - 'safe/safeGet' method has been removed. Use optional chaining. ([NJSMGRN-13](https://jira.skywindgroup.com/browse/NJSMGRN-13))
  - 'jsonwebtoken' has been moved to peerDependencies ([NJSMGRN-56](https://jira.skywindgroup.com/browse/NJSMGRN-56))
  - 'agentkeepalive' has been moved to peerDependencies ([NJSMGRN-60](https://jira.skywindgroup.com/browse/NJSMGRN-60))
  - 'hashids' has been moved to peerDependencies ([NJSMGRN-61](https://jira.skywindgroup.com/browse/NJSMGRN-61))
  - 'kafka-node' has been moved to peerDependencies ([NJSMGRN-18](https://jira.skywindgroup.com/browse/NJSMGRN-18))
  - '@skywind-group/gelf-stream' has been moved to peerDependencies ([NJSMGRN-69](https://jira.skywindgroup.com/browse/NJSMGRN-69))
  - 'sha1' has been replaced to native node:crypto ([NJSMGRN-80](https://jira.skywindgroup.com/browse/NJSMGRN-80))
  - 'ioredis' and 'generic-pool' have been moved to peerDependencies ([NJSMGRN-18](https://jira.skywindgroup.com/browse/NJSMGRN-18))
  - 'measured' has been replaced to 'measured-core' and moved to peerDependencies ([NJSMGRN-71](https://jira.skywindgroup.com/browse/NJSMGRN-71))
  - 'newRelic' has been removed ([NJSMGRN-72](https://jira.skywindgroup.com/browse/NJSMGRN-72))
  - 'Metrics' libs have been moved to peerDependencies ([NJSMGRN-19](https://jira.skywindgroup.com/browse/NJSMGRN-19)):
    - Now getMeasures, getMeasuresStream and getMeasure methods have been returned a Promise
    - 'prometheus-gc-stats' has been removed
    - 'prom-client', 'express-prom-bundle', 'crc', 'emitter-listener' have been moved to peerDependencies
  - 'js-big-integer' has been moved to peerDependencies ([NJSMGRN-75](https://jira.skywindgroup.com/browse/NJSMGRN-75))
  - 'sequelize' has been removed ([NJSMGRN-20](https://jira.skywindgroup.com/browse/NJSMGRN-20))
  - Added 'USE_OLD_VERSION_OF_GENERATING_TRACE_ID' env param to use crypto.randomUUID instead of uuid to generate trace id. It is used by default
    See benchmark below.
  - Fastify' team has removed ['use' middleware since v3](https://fastify.dev/docs/v4.23.x/Guides/Migration-Guide-V3#changed-middleware-support-2014) that is why you must install additional lib "@fastify/middie"
    I hardcoded this "@fastify/middie" lib because if we install @fastify/express, metrics has been counted twice


### Usage
1. If you want to use 'kafka' namespace, like kafka.KafkaWriter, you should add "kafka-node": "5.0.0" in package.json
2. If you want to use 'token' namespace, like token.TokenVerifyException/token.generate, you should add "jsonwebtoken": "^9.0.2" in package.json
3. If you want to use 'keepalive' namespace, like keepalive.createAgent, you should add "agentkeepalive": "^4.5.0" in package.json
4. If you want to use 'HiLoIdGenerator', you should add the next libs to package.json:
   - "js-big-integer": "1.0.2"
   - "ioredis": "^5.3.2"
   - "generic-pool": "^3.9.0"
5. If you want to use 'publicId' namespace, you should add "hashids": "^2.3.0" in package.json
6. If you want to use 'logging' namespace you must add "bole": "^5.0.8" and :
   - If you use gelf, you should add "@skywind-group/gelf-stream": "1.2.6" in package.json
   - If you use kafka, you should add "kafka-node": "5.0.0" in package.json
   - Add this "bole-console": "^0.1.10" lib for local
   - <b>Also you should install libs from point #8</b>
7. If you want to use 'redis' namespace, you should add the next libs to package.json:
   - "ioredis": "^5.3.2"
   - "generic-pool": "^3.9.0"
8. If you want to use 'measures' namespace, you should add the next libs to package.json:
    - "emitter-listener": "^1.1.2"
    - Added 'USE_OLD_VERSION_OF_GENERATING_TRACE_ID' env param to use crypto.randomUUID<b>(it is default)</b> instead of uuid. 
    - if you USE_OLD_VERSION_OF_GENERATING_TRACE_ID = true, you should install the next mandatory lib:
      - "uuid": "^9.0.1"
    - If you want to use "HOSTNAME" env (I don't recommend use it, pls see benchmark 'uuid with hostname'):
      - "crc": "^4.3.2"
    - If you use 'prometheus' provider <b>(it is default)</b>:
      - "express-prom-bundle": "^6.6.0"
      - "prom-client": "^14.2.0"
      - If you use fastify v2 you should install only "fastify"
      - If you use fastify v3/v4 you should install 2 libs:
        - "fastify"
        - "@fastify/middie"
    - If you use 'inMemory' provider:
      - "measured-core": "^2.0.0"

### Lint rules
It was added a lot of rules like:
  - "[eslint:recommended](https://github.com/eslint/eslint)",
  - "[@typescript-eslint/recommended](https://github.com/typescript-eslint/typescript-eslint)",
  - "[import/recommended](https://github.com/import-js/eslint-plugin-import)",
  - "[promise/recommended](https://github.com/eslint-community/eslint-plugin-promise)",
  - "[sonarjs/recommended](https://github.com/SonarSource/eslint-plugin-sonarjs)" 
  - Also was enabled some rules from "[eslint-plugin-unicorn](https://github.com/sindresorhus/eslint-plugin-unicorn)"

### Benchmark

#### Generate Trace ID
<p> Node.js 14.21 </p>

```nodejs
$ node lib/test/benchmark/generateTraceId.js

crypto.randomUUID x 8,934,958 ops/sec ±0.39% (93 runs sampled)
shortId x 126,729 ops/sec ±0.77% (96 runs sampled)
nanoid x 6,558,642 ops/sec ±1.25% (94 runs sampled)
uuid without hostname x 6,589,345 ops/sec ±0.35% (94 runs sampled)
uuid with hostname x 2,033,216 ops/sec ±0.29% (96 runs sampled)
Fastest is crypto.randomUUID
```
<p> Node.js 20.7.0 </p>

```nodejs
$ node lib/test/benchmark/generateTraceId.js
crypto.randomUUID x 25,494,545 ops/sec ±2.21% (90 runs sampled)
crypto.randomUUID - disable cache x 825,109 ops/sec ±5.70% (72 runs sampled)
shortId x 65,289 ops/sec ±5.47% (74 runs sampled)
nanoid x 6,606,546 ops/sec ±0.44% (90 runs sampled)
uuid without hostname x 2,532,345 ops/sec ±0.53% (98 runs sampled)
uuid with hostname x 1,606,909 ops/sec ±6.76% (86 runs sampled)
Fastest is crypto.randomUUID
```

#### Generate Hash - SHA1
<p> Node.js 14.21 </p>

```nodejs
$ node  lib/test/benchmark/generateHash.js

sha1 x 562,879 ops/sec ±0.35% (96 runs sampled)
sha-1 x 1,108,164 ops/sec ±0.26% (99 runs sampled)
Fastest is sha-1

With big string
sha1 x 26,993 ops/sec ±0.96% (91 runs sampled)
sha-1 x 62,006 ops/sec ±0.25% (100 runs sampled)
crypto.createHash x 598,657 ops/sec ±8.86% (72 runs sampled)
Fastest is crypto.createHash          
```
<p> Node.js 20.7.0 </p>

```nodejs
$ node  lib/test/benchmark/generateHash.js
sha1 x 26,862 ops/sec ±0.49% (99 runs sampled)
sha-1 x 63,588 ops/sec ±0.34% (97 runs sampled)
crypto.createHash x 604,872 ops/sec ±3.27% (78 runs sampled)
Fastest is crypto.createHash
```
Test configuration: macOS Monterey 12.2.1, M1 Pro
