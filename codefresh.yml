version: '1.0'
steps:
 compile:
   image: node:8.5
   working_directory: ${{main_clone}}
   environment:
     - NPM_USER=${{NPM_USER}}
     - NPM_PASSWORD=${{NPM_PASSWORD}}
     - NPM_EMAIL=${{NPM_EMAIL}}
   commands:
     - rm -rf node_modules
     - chmod +x npm-sw-login.sh
     - bash -c "./npm-sw-login.sh"
     - echo "NPM Install"
     - npm install --silent --unsafe-perm
     - echo "Compile Server"
     - npm run clean
     - npm run compile

 build_step:
   type: build
   dockerfile: Dockerfile
   image_name: skywindgroup/sw-utils

 unit_test_step:
   type: composition
   composition:
      version: '2'
      services:
        postgres:
          image: postgres
          ports:
            - 5432
   composition_candidates:
     sw-utils:
        image: ${{build_step}}
        environment:
          PGUSER: 'postgres'
          PGDATABASE: 'postgres'
        command: npm run test
        links:
          - 'postgres:db'

 push_to_registry:
   type: push
   candidate: ${{build_step}}
   tag: ${{CF_BRANCH_TAG_NORMALIZED}}

 publish_step:
   image: node:8.5
   working_directory: ${{main_clone}}
   environment:
     - CF_BRANCH=${{CF_BRANCH}}
     - NPM_USER=${{NPM_USER}}
     - NPM_PASSWORD=${{NPM_PASSWORD}}
     - NPM_EMAIL=${{NPM_EMAIL}}
   commands:
     - bash -c './deploy.sh'
