import { EventEmitter } from "node:events";
import { Readable } from "node:stream";

declare namespace measures {
    /*
     * Common metrics
     */
    export enum Metrics {
        WEBSOCKET_CONNECTION_COUNT = "websocket_connection_count",
        WEBSOCKET_REQUESTS_DURATION = "websocket_request_duration_seconds_sum",
        WEBSOCKET_REQUESTS_COUNT = "websocket_request_duration_seconds_count"
    }
    /**
     *
     */
    export interface MeasureInfo {
        /**
         * Measure name
         */
        name: string;
        /**
         * Should marked method be process asyncrononioulsy
         * This flag incluences on the way how we invoke measured method and waite the result to be complete.
         */
        isAsync?: boolean;
        /**
         * Is this metrics used only in debug mode.
         * Any debugMode metric has no influenced on production mode. Their calculation are eliminated.
         */
        debugOnly?: boolean;
    }

    /**
     * Decorator to mark method to be measured
     *
     */
    export function measure(info: MeasureInfo);

    export function measureParam();

    export type MeasurableFunction<T> = (...args) => T | EventEmitter;

    export type DetailsProvider = (args: any[]) => string;

    export interface MeasureProvider {

        /**
         * Make base instrumentation for commonly used frameworks if possible.
         */
        baseInstrument(): void;

        /**
         * Instrument module and correspondent properties.
         */
        instrument(module, name: string, properties?: string[], detailsProvider?: DetailsProvider): void;

        /**
         * Instrument specific function
         *
         */
        instrumentFunction(f: Function, name: string, detailsProvider?: DetailsProvider): Function;

        /**
         * Increment custom metric
         */
        incrementGauge(name: string, value: number, details?: string, attributes?: any): void;

        /**
         * Setup current value for metrics
         */
        setGauge(name: string, value: number, details?: string, attributes?: any): void;

        /**
         *   Add error to monitoring
         *
         * @param err error or exception
         * @param attributes additional extra attributes
         */
        saveError(err, attributes?): void;

        /**
         * Get current transaction name
         */
        getTransaction(): string;

        /**
         * Get current transaction unique ID
         */
        getTraceID(): string;

        /**
         * Set current transaction unique ID
         */
        setTraceID(tranceID: string): string;

        /**
         * Set current transaction name
         */
        setTransaction(name: string): void;

        /**
         * Set transaction context variable.
         * This is a convenient method to bypass context with async context
         *
         * @param name variable name
         * @param value variable value
         * @param transferable if the variable will be transferred remotely(http)
         */
        setContextVariable<T>(name: string, value: T, transferable?: boolean): boolean;

        /**
         * Get context variable.
         *
         * @param name variable name
         * @param transferable if the variable will be transferred remotely(http)
         */
        getContextVariable<T>(name: string, transferable?: boolean): T;

        /**
         * Run action under transaction and track transactional context
         */
        runInTransaction<T>(name: string, action: (...args) => T): T;

        /**
         * Bind action or EventEmmitter directly to the current transaction
         */
        trackTransaction<T>(action: MeasurableFunction<T>): MeasurableFunction<T>;

        /**
         * Get current metric information if possible
         */
        getMeasures(): Promise<any>;

        /**
         * Get string stream with measures data
         */
        getMeasuresStream(): Promise<Readable>;

        /**
         * Get specific metric if possible
         */
        getMeasure(name: string): Promise<any>;

        getContext<T>(transferable?: boolean): T;
    }

    /**
     *  Get measures
     */
    export const measureProvider: MeasureProvider;

    /**
     * Provider name
     */
    export const providerName;

    /**
     *  Get measure by name
     */
    export function getMeasure(name: string): Promise<any>;

    /**
     * Marks an object as a measurable param
     */
    export interface Measurable {
        getMeasureKey(): string;
    }
}

declare namespace logging {
    function logger(name): Logger;

    interface Logger {
        debug(...args: any[]): Logger;

        info(...args: any[]): Logger;

        warn(...args: any[]): Logger;

        error(...args: any[]): Logger;
    }

    function setRootLogger(name: string): void;

    function getRootLogger(): string;

    function errAsObject(error: Error & { code: number }): object;

    export interface OutputConfig {
        type: "console" | "graylog" | "kafka" | "kafkajs";
        logLevel: string;
    }

    /**
     * Graylog outout configuration
     */

    export interface GrayLogOutputConfig extends OutputConfig {
        type: "graylog";
        host: string;
        port: number;
    }

    /**
     * Common interface for generic stream output.
     * Currently kafka only.
     */
    export interface StreamOutputConfig extends OutputConfig {
        highWaterMark?: number;
        recreateStreamTimeout?: number;
    }

    /**
     * Kafka output configuration
     */
    export interface KafkaOutputConfig extends StreamOutputConfig {
        kafkaHost?: string;
        requestTimeout?: number;
        connectionTimeout?: number;
        requireAcks?: number;
        ackTimeoutMs?: number;
        partitionerType?: number;
        topic?: string;
        type: "kafka";
    }

    export interface KafkaJsOutputConfig extends KafkaOutputConfig {
        type: "kafkajs";
    }

    export function setUpOutput(configs: OutputConfig | OutputConfig[]): void;
}

declare namespace kafka {
    export interface KafkaConfiguration {

        kafkaBrokerHostnames: string;

        topicName: string;
        /**
         * Default value is -1, which is require ack from all
         */
        requireAck?: number;
        /**
         * default is 1000 ms
         */
        ackTimeoutMs?: number;
        /**
         * Default is 6000 ms
         */
        clientCreationTimeout?: number;
        /**
         * Default is 5000 ms
         */
        requestTimeout?: number;
        /**
         * Default is 2
         */
        partitionerType?: number;
        /**
         * Default is 10000 ms
         */
        maxSendAttemptTimeout?: number;

        /**
         *  Default is 0 (no batching)
         */
        noAckBatchSize?: number;

        /**
         * Default is 0 (no batching)
         */
        noAckBatchAge?: number;
    }

    export interface KafkaWriter {
        sendMessages(messages: string[], key?: string): Promise<void>;
    }

    function createWriter(config: KafkaConfiguration, logger: logging.Logger): Promise<KafkaWriter>;
}

declare interface Lazy<T> {
    get(options?): T;
}

declare function lazy<T>(initializer: (options?) => T): Lazy<T>;

declare function sleep(timeMs: number): Promise<void>;

declare function parseDateOrThrow(iso8601Date: string, errorSupplier: () => any): Date;

export interface RetryConfig {
    sleep: number;
    maxTimeout: number;
}

export function retry<T>(config: RetryConfig,
                         action: () => Promise<T>,
                         shouldRetry?: (err: Error) => boolean,
                         wrapError?: (err: Error) => Error): Promise<T>;

declare function getEnvironmentInfo(): any;

declare namespace redis {
    export type RedisClient = import ("ioredis").Redis | import ("ioredis").Cluster;

    export class RedisProc {

        public readonly handle: string;

        constructor(logger: logging.Logger, ...files: string[]);

        public init(client: RedisClient): Promise<void>;

        public exec<T>(client: RedisClient, keys?: string[], args?: string[]);
    }

    export interface RedisPoolConfig {
        host: string;
        port: number;
        password: string;
        sentinels?: Partial<import ("ioredis").SentinelAddress>[];
        sentinelUsername?: string;
        sentinelPassword?: string;
        clusterNodes?: import ("ioredis").ClusterNode[];
        clusterSlotsRefreshInterval?: number;
        clusterScaleReads?: import ("ioredis").NodeRole;
        clusterEnableOfflineQueue?: boolean;
        clusterEnableReadyCheck?: boolean;
        clusterName?: string;
        connectionTimeout: number;
        minConnections: number;
        maxConnections: number;
        maxIdleTime: number;
        replicationFactor?: number;
        replicationTimeout?: number;
        maxRetriesPerRequest: number;
        showFriendlyErrorStack?: boolean;
        enableOfflineQueue?: boolean;
        failoverDetector?: boolean;
    }

    export interface RedisPool<CLIENT extends RedisClient> {
        get(): Promise<CLIENT>;

        release(client: CLIENT): Promise<void>;

        usingDb<T>(callback: (client: CLIENT) => Promise<T>): Promise<T>;

        usingDbWithReplicate<T>(callback: (client: CLIENT) => Promise<T>): Promise<T>;

        waitForSync(client: CLIENT): Promise<void>;

        /**
         * this method is necessary for a graceful shutdown service
         */
        shutdown(): Promise<void>;
    }

    export function createRedisPool<CLIENT extends RedisClient>(config: RedisPoolConfig): RedisPool<CLIENT>;

    export interface RedisClientFactory<CLIENT extends RedisClient> {
        createClient(): CLIENT;
    }

    export function getRedisFactory<CLIENT extends RedisClient>(config: RedisPoolConfig): RedisClientFactory<CLIENT>;

    export function handleRedisEvents(redis: RedisClient, logger: logging.Logger): void
}

declare namespace testing {
    export type RequestMockHandler = (req: { qs?: any, body?: any, url: string }) =>
        { status: number, body?: any, headers?: any };

    export interface RequestMock {
        post(path: RegExp | string, handler: RequestMockHandler);

        put(path: RegExp | string, handler: RequestMockHandler);

        delete(path: RegExp | string, handler: RequestMockHandler);

        patch(path: RegExp | string, handler: RequestMockHandler);

        get(path: RegExp | string, handler: RequestMockHandler);

        clearRoutes(): void;

        clearRoute(method: string, url): void;

        unmock(agent: any): void;

        readonly args: any[];
    }

    export function status(statusCode: number, body?: any): RequestMockHandler;

    export function status200(body?: any): RequestMockHandler;

    export function status500(body?: any): RequestMockHandler;

    export function status400(body?: any): RequestMockHandler;

    export interface RequestMockHandlerOnCall extends RequestMockHandler {
        onCall(handler: RequestMockHandler): RequestMockHandlerOnCall;
    }

    export function onCall(handler: RequestMockHandler): RequestMockHandlerOnCall;

    export function requestMock(agent: any): RequestMock;
}

declare namespace calculation {
    export function normalizeAmountByPrecision(precision: number, value: number);
    export function safeAddWithPrecision(precision: number, a: number, b: number);
}

export declare class Latch {
    public awaitFor(ms: number): Promise<boolean>;
    public notify(): void;
    public notifyAll(): void;
    public readonly waitingCount: number;
}

declare namespace errors {

    export enum ERROR_LEVEL {
        ERROR,
        WARN,
        INFO,
        DEBUG,
    }

    interface ErrorData {
        [field: string]: string | string[];
    }

    export class SWBaseError extends Error {
        public responseStatus: number;
        public code: number;
        public data: ErrorData;
        public level: ERROR_LEVEL;
        public extraData?: any;
        public translateMessage?: boolean;
        public external?: boolean;
        public providerDetails?: object;

        constructor(status: number,
                    code: number,
                    message: string,
                    level?: ERROR_LEVEL,
                    extraData?: any,
                    external?: boolean,
                    providerDetails?: object);

        public getErrorLevel(): string;

        public dontTranslate(): SWBaseError;

        public setProviderDetails(details: object): SWBaseError;

        public static isSWError(err): err is SWBaseError;
    }
}

export namespace publicId {

    export interface Options {
        password: string;
        minLength: number;
    }

    export class PCID {
        constructor(options: Options);

        public encode(value: number | string);

        public decode(value: number | string);
    }

    export const instance: PCID;
}

declare namespace keepalive {
    type HttpAgent = import ("http").Agent;
    type HttpsAgent = import ("https").Agent;

    export interface KeepAliveConfig {
        maxFreeSockets: number;
        freeSocketKeepAliveTimeout: number;
        socketActiveTTL: number;
    }

    export function createAgent(config: KeepAliveConfig, https?: boolean | string): HttpAgent | HttpsAgent;
}

declare namespace token {
    type JwtPayload = import("jwt").JwtPayload
    type Algorithm = import("jwt").Algorithm
    export interface TokenConfig {
        readonly secret: string;
        readonly algorithm: Algorithm;
        readonly issuer: string;
        readonly expiresIn?: number;
    }

    export function generate<T extends object>(data: T, config: TokenConfig): Promise<string>;

    export function verify<T extends JwtPayload>(token: string, config: TokenConfig): Promise<T>;

    export function parse<T extends JwtPayload>(token: string): T;

    export class TokenVerifyException extends errors.SWBaseError {
        constructor();
    }

    export class TokenExpiredException extends errors.SWBaseError {
        constructor();
    }
}

export declare class HiLoIdGenerator {
    constructor(key: string,
                low: number,
                connection: Lazy<redis.RedisPool>,
                min?: string,
                max?: string)

    public init(): Promise<void>;

    public nextId(): Promise<string>;
}
