version: '3.5'
services:
  api:
    build:
      target: main
      context: .
      dockerfile: Dockerfile
    links:
      - db
      - redis
    environment:
      MEASURES_PROVIDER: "memory"
      SYNC_ON_START: "true"
      PGUSER: postgres
      PGDATABASE: postgres
      EVENT_CONSUMER_PGUSER: postgres
      EVENT_CONSUMER_PGDATABASE: postgres
    depends_on:
      - test
  test:
    build:
      target: build
      context: .
      dockerfile: Dockerfile
    links:
      - db
      - redis
    environment:
      MEASURES_PROVIDER: "memory"
      SYNC_ON_START: "true"
      PGUSER: postgres
      PGDATABASE: postgres
      EVENT_CONSUMER_PGUSER: postgres
      EVENT_CONSUMER_PGDATABASE: postgres
    command: sh -c "NODE_OPTIONS=--max-http-header-size=40960 npm run test"
  db:
    image: 'postgres:10'
    command: postgres -c fsync=off -c synchronous_commit=off -c full_page_writes=off
    ports:
      - 5432
    environment:
      POSTGRES_HOST_AUTH_METHOD: trust
  redis:
    image: redis:alpine
    command: redis-server --save "" --appendonly no
    ports:
      - 6379
